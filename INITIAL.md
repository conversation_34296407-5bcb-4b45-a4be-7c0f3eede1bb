# Feature Request Template

*Copy this file for each new feature request and fill out the sections below. Use this as input for implementation workflows.*

## SCOPE:
[Choose your development approach]
- **prototype**: Build minimal working version with proper patterns (focused on proving concept with good structure)
- **production**: Build comprehensive production-ready system (full feature set with enterprise patterns)

## FEATURE:
[Describe what you want to build - be specific about functionality and requirements]

Example:
- ❌ "Build a web scraper"
- ✅ "Build an async web scraper using BeautifulSoup that extracts product data from e-commerce sites, handles rate limiting, and stores results in PostgreSQL"

## EXAMPLES:
[List any example files in the examples/ folder and explain how they should be used as patterns]

Example:
- `examples/auth/jwt_handler.py` - Follow this pattern for token generation and validation
- `examples/database/async_crud.py` - Use this async database pattern for all DB operations
- `examples/tests/test_auth.py` - Follow this testing structure for authentication tests

## DOCUMENTATION:
[Include links to relevant documentation, APIs, libraries, or MCP server resources that will be needed]

Example:
- https://fastapi.tiangolo.com/tutorial/security/oauth2-jwt/ - JWT implementation guide
- https://docs.pydantic.dev/latest/concepts/validators/ - Pydantic validation patterns
- https://docs.python.org/3/library/asyncio.html - Async programming reference

## OTHER CONSIDERATIONS:
[Mention any gotchas, specific requirements, or things AI assistants commonly miss with your projects]

Example:
- Use RS256 algorithm for JWT tokens in production (not HS256)
- All database operations must use connection pooling
- Include rate limiting for all external API calls
- Follow the factory pattern for all configuration-dependent models
- Ensure all async functions have proper error handling

---

## Usage Instructions:

1. **Copy this template**: `cp INITIAL.md my-feature-request.md`
2. **Fill out all sections** with specific details for your feature
3. **Choose workflow**: Run `/research-analyze my-feature-request.md` for automatic routing
4. **Follow recommended path**: Use suggested commands based on your SCOPE selection

## Tips for Better Results:

- **Be specific** in the FEATURE section - include technical details
- **Reference existing patterns** in EXAMPLES - AI works better with concrete examples
- **Include complete URLs** in DOCUMENTATION - don't assume AI knows your preferred docs
- **Capture gotchas** in OTHER CONSIDERATIONS - prevent common mistakes upfront
- **Choose appropriate scope** - prototype for learning/discovery, production for known requirements
