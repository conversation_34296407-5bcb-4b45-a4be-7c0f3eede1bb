# Complex Workflow Step 7: Implement Module

## Command Purpose
Transform comprehensive module PRPs into working, tested module implementations through parallel specialist development with progressive validation and strategic thinking optimization.

## Development Standards Compliance
**CRITICAL**: Follow the comprehensive coding standards defined in development_standards.md:
- **File Length**: 500 lines maximum - refactor if approaching this limit
- **Function Length**: 50 lines maximum - maintain single responsibility
- **AI Agents**: Must be module-global constants, never instantiate per-call
- **Configuration**: Use factory pattern to respect environment variables
- **Type Safety**: All functions must have complete type hints
- **Package Management**: Always use `uv add/remove` - never edit pyproject.toml directly
- **Testing**: Co-located tests with comprehensive coverage
- **Code Quality**: Use Ruff for formatting, MyPy for type checking

## Usage
```
/complex-7-implement-module PRPs/modules/6-module-prp-[module-name].md
```

### Examples
```bash
/complex-7-implement-module PRPs/modules/6-module-prp-F1-vector-storage-foundation.md
/complex-7-implement-module PRPs/modules/6-module-prp-C1-conversation-manager-core.md  
/complex-7-implement-module PRPs/modules/6-module-prp-I1-external-api-integration.md
```

## Auto-Detection Features
When provided with a module PRP, the command automatically:
- **Detects implementation context**: Reads complete PRP specification and implementation blueprint
- **Identifies project context**: Accesses related project files and integration requirements
- **Validates readiness**: Ensures PRP contains sufficient context for autonomous implementation
- **Sets up workspace**: Creates appropriate directory structure and development environment

## Input Requirements
- **Primary Input**: `PRPs/modules/6-module-prp-[module-name].md` (comprehensive module PRP from `/complex-6-create-module-prp`)
- **Auto-Detected Context**: 
  - Related project files and integration specifications
  - Module dependency requirements and interface definitions
  - Project standards and quality frameworks
- **Validation**: Module PRP must be implementation-ready with autonomous capability score ≥8/10

## Output Generated
- **Working Module**: Complete, tested module implementation with all required functionality
- **Integration Tests**: Comprehensive test suite validating module functionality and integration
- **Documentation**: Module documentation with usage examples and API specifications
- **Quality Validation**: Confirmation of quality standards and project compliance

### File Organization Benefits
- **Module Focus**: Specific module development with clear boundaries and responsibilities
- **Project Integration**: Module implementation aligned with project architecture and standards
- **Quality Assurance**: Built-in testing and validation ensuring production readiness
- **Team Handoff**: Clear deliverables ready for integration and deployment

## Specialist Implementers

You are the **Autonomous Implementation Coordinator** managing 5 specialist implementers to transform comprehensive module PRPs into working, tested implementations with strategic thinking optimization.

### Enhanced Multi-Agent Implementation Strategy
This command utilizes **comprehensive autonomous implementation** with progressive validation:
- **Module PRP Input**: Complete implementation blueprint with project context integration
- **Parallel Implementation**: Multiple specialists working on different aspects simultaneously
- **Progressive Validation**: Continuous testing and quality assurance throughout development
- **Quality Focus**: Single-pass implementation success through comprehensive preparation

### Implementer 1: Core Functionality Developer
**Role**: Implement the primary module functionality, business logic, and core components

**Your Task**: **Ultrathink this comprehensive core implementation** - Transform the module PRP's core functionality specifications into working, tested implementation.

**Implementation Focus**:
1. **Core Logic Implementation**:
   - Implement primary module functionality based on PRP specifications
   - Build business logic, algorithms, and core data processing
   - Create module's main classes, functions, and data structures
   - **ENFORCE**: Follow development_standards.md - max 500 lines per file, 50 per function
   - **REQUIRE**: Pydantic AI agents as module globals (never per-call instantiation)
   - Ensure implementation follows module's single responsibility principle

2. **Module Interface Development**:
   - Implement module's public API and interface contracts
   - Create clear input/output specifications and data validation
   - Build error handling and exception management for core functionality
   - Ensure interface compliance with project integration requirements

3. **Data Model Implementation**:
   - Implement module's data models, validation rules, and persistence patterns
   - Create data transformation and processing logic
   - Build caching and performance optimization strategies
   - Ensure data model integration with project architecture

4. **Core Testing Development**:
   - Write comprehensive unit tests for all core functionality
   - Create integration tests for module interfaces and data flows
   - Build performance tests and benchmarking for core algorithms
   - Ensure test coverage meets project quality standards

**Provide**:
- Working core module functionality with comprehensive testing
- Clear public interface implementation with validation and error handling
- Data model implementation with optimization and project integration
- Complete test suite validating core functionality and performance

### Implementer 2: Integration & Dependencies Developer
**Role**: Implement module's integration points, external dependencies, and cross-module communication

**Your Task**: **Think hard about integration implementation complexity** - Build all module integration points and dependency management based on PRP specifications.

**Implementation Focus**:
1. **External Integration Implementation**:
   - Implement connections to external services, APIs, and third-party libraries
   - Build authentication, authorization, and secure communication patterns
   - Create retry logic, error handling, and fallback mechanisms for external dependencies
   - **APPLY**: Configuration factory pattern from development_standards.md for environment respect
   - Ensure integration follows project security and performance standards

2. **Cross-Module Communication**:
   - Implement interfaces with other project modules and shared components
   - Build data exchange patterns and communication protocols
   - Create event handling and message passing mechanisms
   - Ensure communication follows project architecture patterns

3. **Dependency Management**:
   - Implement dependency injection and configuration management
   - Build environment-specific configuration and deployment settings
   - Create development, testing, and production environment adaptations
   - Ensure dependency management follows project standards

4. **Integration Testing Development**:
   - Write integration tests for all external service connections
   - Create cross-module integration tests and communication validation
   - Build end-to-end testing for complete integration workflows
   - Ensure integration testing covers failure scenarios and recovery

**Provide**:
- Working integration implementation with external services and project modules
- Comprehensive dependency management and configuration systems
- Integration test suite validating all connection points and communication
- Environment-specific deployment and configuration management

### Implementer 3: Quality Assurance & Testing Specialist
**Role**: Implement comprehensive testing strategies, quality validation, and performance optimization

**Your Task**: **Think hard about comprehensive quality assurance implementation** - Build complete testing and quality validation systems based on PRP quality framework.

**Implementation Focus**:
1. **Testing Framework Implementation**:
   - Implement comprehensive test suite covering unit, integration, and system testing
   - Build automated testing pipeline and continuous validation
   - Create test data management and mock service implementations
   - **ENFORCE**: Co-located testing pattern from development_standards.md
   - **VALIDATE**: File length limits and type safety requirements
   - Ensure testing framework integration with project quality tools

2. **Quality Standards Implementation**:
   - Implement code quality validation and standards enforcement
   - Build performance monitoring and optimization validation
   - Create security testing and vulnerability assessment
   - Ensure quality standards compliance with project requirements

3. **Performance Optimization Implementation**:
   - Implement performance monitoring and metrics collection
   - Build caching strategies and resource optimization
   - Create load testing and performance benchmarking
   - Ensure performance targets meet project specifications

4. **Quality Validation Automation**:
   - Implement automated quality gates and validation checkpoints
   - Build continuous integration testing and quality reporting
   - Create quality metrics dashboard and monitoring
   - Ensure quality validation enables autonomous deployment

**Provide**:
- Comprehensive testing framework with automated validation and quality gates
- Performance optimization implementation with monitoring and metrics
- Quality assurance automation with continuous validation and reporting
- Complete quality validation confirming module readiness for production

### Implementer 4: Documentation & User Experience Developer
**Role**: Implement module documentation, usage examples, and developer experience optimization

**Your Task**: **Think carefully about documentation implementation completeness** - Create comprehensive documentation and usage examples based on PRP specifications.

**Implementation Focus**:
1. **API Documentation Implementation**:
   - Implement complete API documentation with examples and usage patterns
   - Build interactive documentation and testing interfaces
   - Create code examples and integration guides
   - Ensure documentation follows project standards and conventions

2. **Usage Examples Development**:
   - Implement working examples demonstrating module functionality
   - Build tutorial and getting-started guides
   - Create advanced usage patterns and best practices documentation
   - Ensure examples are tested and validated for accuracy

3. **Developer Experience Optimization**:
   - Implement development tools and debugging utilities
   - Build error messaging and troubleshooting guides
   - Create development workflow optimization and tooling
   - Ensure developer experience follows project usability standards

4. **Documentation Testing**:
   - Test all documentation examples and code snippets for accuracy
   - Validate documentation completeness against module functionality
   - Create documentation update and maintenance processes
   - Ensure documentation enables successful module adoption

**Provide**:
- Complete API documentation with working examples and integration guides
- Developer experience optimization with tools and troubleshooting resources
- Validated documentation with tested examples and maintenance processes
- Documentation framework enabling successful module adoption and integration

### Implementer 5: Deployment & Operations Specialist
**Role**: Implement deployment strategies, operational monitoring, and production readiness validation

**Your Task**: **Think carefully about deployment implementation optimization** - Build deployment and operations implementation based on PRP operational requirements.

**Implementation Focus**:
1. **Deployment Implementation**:
   - Implement deployment automation and configuration management
   - Build environment-specific deployment strategies and validation
   - Create rollback and recovery mechanisms for deployment failures
   - Ensure deployment follows project operational standards

2. **Monitoring & Observability Implementation**:
   - Implement operational monitoring and alerting systems
   - Build logging, metrics, and tracing for module operations
   - Create health checks and operational validation
   - Ensure monitoring integration with project observability framework

3. **Operations Automation**:
   - Implement automated operational procedures and maintenance
   - Build scaling and resource management automation
   - Create backup and disaster recovery procedures
   - Ensure operations automation follows project standards

4. **Production Readiness Validation**:
   - Validate module readiness for production deployment
   - Test operational procedures and monitoring effectiveness
   - Create operational runbooks and troubleshooting guides
   - Ensure production readiness meets project requirements

**Provide**:
- Complete deployment automation with environment management and validation
- Operational monitoring and observability with alerting and health checks
- Operations automation with scaling, backup, and recovery procedures
- Production readiness validation with operational runbooks and procedures

## Implementation Coordination Process

1. **PRP Analysis and Implementation Planning**:
   ```bash
   # Validate module PRP and extract implementation requirements
   MODULE_PRP_FILE="$ARGUMENTS"
   
   if [[ ! -f "$MODULE_PRP_FILE" ]]; then
       echo "❌ ERROR: Module PRP file '$MODULE_PRP_FILE' not found"
       echo "Expected: PRPs/modules/PRP-[module-name].md from /create-module-prp"
       exit 1
   fi
   
   # Extract module information and validate readiness
   MODULE_NAME=$(basename "$MODULE_PRP_FILE" .md | sed 's/^PRP-//')
   
   echo "🎯 Module: $MODULE_NAME"
   echo "📋 PRP: $MODULE_PRP_FILE"
   
   # Validate PRP implementation readiness
   if ! grep -q "Implementation Readiness.*High" "$MODULE_PRP_FILE"; then
       echo "⚠️ WARNING: Module PRP may not be ready for autonomous implementation"
       echo "Expected: Implementation readiness score ≥8/10 from /create-module-prp"
   fi
   
   # Create module implementation workspace following project structure
   # NOTE: Check for existing project structure from /create-project-plan
   if [[ -f "PROJECT-STRUCTURE/CLAUDE.md" ]]; then
       echo "📋 Using project structure from PROJECT-STRUCTURE/CLAUDE.md"
       # Integrate with existing project structure, not isolated modules
       MODULE_WORKSPACE="src/${MODULE_NAME}"
   else
       echo "⚠️ WARNING: No PROJECT-STRUCTURE/ found - using isolated module structure"
       MODULE_WORKSPACE="modules/${MODULE_NAME}"
   fi
   mkdir -p "$MODULE_WORKSPACE"
   echo "📁 Implementation workspace: $MODULE_WORKSPACE"
   ```

2. **Parallel Implementation Coordination**:
   - **Ultrathink this comprehensive autonomous implementation coordination** - Execute all 5 specialist implementers in parallel
   - Cross-validate implementations between specialists for consistency and integration
   - Ensure comprehensive coverage: core functionality, integration, quality, documentation, operations
   - Progressive validation and testing throughout implementation process

3. **Implementation Workspace Organization**:
   ```
   modules/[module-name]/
   ├── src/                    # Core implementation from Core Functionality Developer
   ├── integrations/           # Integration code from Integration & Dependencies Developer
   ├── tests/                  # Comprehensive test suite from Quality Assurance Specialist
   ├── docs/                   # Documentation from Documentation & User Experience Developer
   ├── deployment/             # Deployment configs from Deployment & Operations Specialist
   └── README.md               # Module overview and usage guide
   ```

4. **Progressive Validation Gates**:
   - **Phase 1 Validation**: Core functionality implementation and unit testing
   - **Phase 2 Validation**: Integration implementation and cross-module testing
   - **Phase 3 Validation**: Quality assurance and performance testing
   - **Phase 4 Validation**: Documentation validation and example testing
   - **Phase 5 Validation**: Deployment readiness and operational validation

## Implementation Success Criteria

### **Module Implementation Completion**
- [ ] **Core Functionality**: All PRP-specified functionality implemented and tested
- [ ] **Integration Points**: All external and cross-module integrations working
- [ ] **Quality Standards**: All quality gates passed and performance targets met
- [ ] **Documentation**: Complete documentation with tested examples
- [ ] **Operational Readiness**: Deployment and monitoring systems implemented

### **Project Integration Validation**
- [ ] **Architecture Compliance**: Module implementation follows project architecture
- [ ] **Standards Compliance**: Implementation follows project coding and quality standards
- [ ] **Interface Compatibility**: Module interfaces work correctly with project components
- [ ] **Performance Integration**: Module performance meets project requirements

### **Autonomous Implementation Success**
- [ ] **PRP Compliance**: All PRP requirements successfully implemented
- [ ] **Quality Validation**: Comprehensive testing and quality assurance complete
- [ ] **Integration Testing**: Cross-module and system integration validated
- [ ] **Production Readiness**: Module ready for deployment and operation

## Output Report: Working Module Implementation

### **Implementation Summary**
```markdown
MODULE IMPLEMENTATION COMPLETE: [Module Name]

IMPLEMENTATION RESULTS:
├── Core Functionality: ✅ [Component details with test results]
├── Integration & Dependencies: ✅ [Integration details with test results]
├── Quality Assurance: ✅ [Quality validation results and performance metrics]
├── Documentation & UX: ✅ [Documentation completeness and example validation]
└── Deployment & Operations: ✅ [Operational readiness and deployment validation]

QUALITY METRICS:
- Unit Tests: [X/X passing]
- Integration Tests: [X/X passing]
- Performance Tests: [X/X passing]
- Documentation Examples: [X/X working]
- Quality Gates: [X/X passed]

PRP IMPLEMENTATION COMPLIANCE:
✅ All PRP requirements successfully implemented
✅ Quality standards met and validated
✅ Integration requirements satisfied
✅ Production readiness confirmed

AUTONOMOUS IMPLEMENTATION SUCCESS: ✅ COMPLETE
Single-pass implementation success confirmed with comprehensive validation.
```

### **Module Deliverables**
- **Working Module**: `modules/[module-name]/` - Complete implementation
- **Test Suite**: Comprehensive testing with quality validation
- **Documentation**: API docs, usage examples, and integration guides
- **Deployment**: Production-ready deployment and operational configuration

### **Integration Status**
- **Project Compatibility**: Module ready for project integration
- **Quality Compliance**: All project standards met and validated
- **Performance Validation**: Module performance targets achieved
- **Operational Readiness**: Monitoring and deployment systems operational

## Success Criteria for Module Implementation

### **Technical Implementation Success**
- All PRP functionality requirements implemented and working
- **Development Standards Compliance**: All development_standards.md requirements met
  - File length limits enforced (500 lines max)
  - AI agent patterns implemented correctly (module-global reuse)
  - Configuration factory patterns applied
  - Type safety validated with MyPy
- Comprehensive test suite with high coverage and quality validation
- Integration points validated and working with project components
- Performance targets met and operational monitoring implemented

### **Quality Assurance Success**
- Code quality standards met and validated
- Security requirements implemented and tested
- Documentation complete with working examples
- Production readiness confirmed through comprehensive validation

### **Project Integration Success**
- Module follows project architecture and standards
- Integration with other modules validated and working
- Team handoff complete with operational documentation
- Module ready for production deployment and operation

## Anti-Patterns to Avoid

- ❌ **Incomplete Implementation**: Partial implementation that doesn't meet PRP requirements
- ❌ **Poor Integration**: Module doesn't integrate properly with project components
- ❌ **Inadequate Testing**: Insufficient testing or quality validation
- ❌ **Missing Documentation**: Incomplete or untested documentation and examples
- ❌ **Operational Gaps**: Module not ready for production deployment and operation
- ❌ **Quality Shortcuts**: Reduced quality standards in pursuit of speed

## Enhanced Implementation Benefits

- ✅ **Comprehensive Implementation**: Complete module development with all requirements satisfied
- ✅ **Quality Assurance**: Built-in testing and validation ensuring production readiness
- ✅ **Project Integration**: Module implementation aligned with project architecture and standards
- ✅ **Autonomous Success**: Single-pass implementation capability through comprehensive PRP preparation
- ✅ **Operational Readiness**: Module ready for deployment with monitoring and maintenance procedures
- ✅ **Team Handoff**: Clear deliverables and documentation enabling smooth team integration

The module implementation transforms comprehensive PRPs into working, tested, production-ready modules through parallel specialist development and progressive validation, completing the autonomous implementation pipeline.
