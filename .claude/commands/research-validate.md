# Universal Template Validation for PRP Readiness (Multi-Agent Enhanced)

Validate template: $ARGUMENTS

Use independent subagents to investigate specific validation questions and assess template readiness for single-pass PRP implementation success with persistent validation reporting.

## Prerequisites
- You must have a completed template from research phase:
  - Enhanced initial: `1-enhanced-initial-[feature].md` or `0-enhanced-initial-[feature].md`
  - PRD template: `1-product-requirements-[feature].md`
- Template should be output from enhancement or PRD creation commands
- Works with both simple and complex workflow templates

## Output Generated
- **Validation Report**: Automatically saved to appropriate analysis folder
  - Complex workflow: `PRPs/project-planning/[feature]/analysis/X-validation-report-[feature].md`
  - Simple workflow: `PRPs/simple-workflow/X-validation-report-[feature].md`
- **Quality Assessment**: Comprehensive validation scores and improvement recommendations
- **Iteration Tracking**: Appends to existing validation reports for improvement history
- **Readiness Confirmation**: Clear go/no-go decision for proceeding to next step

## Multi-Agent Validation Process

### Phase 1: Initial Template Analysis (Lead Agent)
**Objective**: Understand template type, structure, and setup output location

1. **Template Type Detection and Output Setup**
   ```bash
   # Detect template type and read content
   TEMPLATE_FILE="$ARGUMENTS"
   cat "$TEMPLATE_FILE"
   
   # Extract feature name and determine workflow type
   if [[ "$TEMPLATE_FILE" == *"1-enhanced-initial"* ]] || [[ "$TEMPLATE_FILE" == *"0-enhanced-initial"* ]]; then
       echo "Enhanced INITIAL template detected - Simple workflow"
       TEMPLATE_TYPE="enhanced_initial"
       WORKFLOW_TYPE="simple"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^[0-9]-enhanced-initial-//')
       OUTPUT_DIR="PRPs/simple-workflow"
       NEXT_STEP="/simple-3-create-prp"
   elif [[ "$TEMPLATE_FILE" == *"1-product-requirements"* ]]; then
       echo "PRD template detected - Complex workflow"  
       TEMPLATE_TYPE="prd"
       WORKFLOW_TYPE="complex"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^1-product-requirements-//')
       OUTPUT_DIR="PRPs/project-planning/${FEATURE_NAME}/analysis"
       NEXT_STEP="/complex-3-consolidate-architecture"
   elif grep -q "## FEATURE:" "$TEMPLATE_FILE"; then
       echo "Legacy enhanced INITIAL template detected"
       TEMPLATE_TYPE="enhanced_initial"
       WORKFLOW_TYPE="simple"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^enhanced-//')
       OUTPUT_DIR="PRPs/simple-workflow"
       NEXT_STEP="/simple-3-create-prp"
   elif grep -q "## Problem Statement\|## Executive Summary" "$TEMPLATE_FILE"; then
       echo "Legacy PRD template detected"
       TEMPLATE_TYPE="prd"
       WORKFLOW_TYPE="complex"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^PRD-//')
       OUTPUT_DIR="PRPs/project-planning/${FEATURE_NAME}/analysis"
       NEXT_STEP="/complex-3-consolidate-architecture"
   else
       echo "❌ ERROR: Unknown template type in: $TEMPLATE_FILE"
       echo "Expected: enhanced-initial or PRD template"
       exit 1
   fi
   
   # Create output directory and set validation report path
   mkdir -p "$OUTPUT_DIR"
   VALIDATION_REPORT="${OUTPUT_DIR}/validation-report-${FEATURE_NAME}.md"
   echo "🎯 Validation report will be saved to: $VALIDATION_REPORT"
   ```

2. **Validation History Check**
   - Check for existing validation reports
   - Prepare iteration tracking
   - Plan improvement assessment based on previous validations

### Phase 2: Parallel Subagent Validation

**Think hard about comprehensive validation assessment** - Deploy independent subagents to investigate specific validation questions:

**Implementation Readiness Investigator**
- Objective: Investigate whether this template provides sufficient context for autonomous implementation success
- Task: "**Think hard about implementation readiness assessment**: Based on this template, investigate: Does it contain enough specific information for autonomous coding? Are the requirements clear and unambiguous? Can implementation proceed without additional clarification? Assess the clarity of specifications, adequacy of examples, and completeness of implementation guidance. Consider single-pass success probability."
- Output format: Implementation readiness score (1-10) with specific assessment of what enables or prevents autonomous success
- Enhanced focus: Risk assessment for implementation failures, resource requirements, timeline estimates
- Boundaries: Focus on implementation feasibility and clarity, not technical accuracy details

**Technical Accuracy Investigator** 
- Objective: Investigate the technical quality, accuracy, and relevance of examples, documentation, and specifications
- Task: "**Think carefully about technical accuracy validation**: Based on this template, investigate: Are the technical specifications accurate and relevant? Do the examples actually exist and apply to the requirements? Are the documentation links valid and useful? Check codebase patterns for accuracy. Assess technical correctness and relevance of all technical content."
- Tools: Use file verification, pattern checking, codebase analysis, and technical assessment
- Output format: Technical accuracy score (1-10) with specific assessment of technical quality and relevance
- Enhanced focus: Codebase pattern verification, dependency analysis, technology stack validation
- Boundaries: Focus on technical correctness and relevance, not implementation readiness

**Completeness & Gap Investigator**
- Objective: Investigate what critical information might be missing and identify gaps that could cause implementation failure
- Task: "**Think carefully about completeness gap identification**: Based on this template, investigate: What critical information is missing? What gaps could cause implementation problems? Are there edge cases, gotchas, or requirements not covered? Identify specific areas where additional information is needed. Prioritize gaps by implementation impact."
- Output format: Completeness score (1-10) with specific identification of gaps and missing critical information
- Enhanced focus: Gap prioritization (Critical/High/Medium/Low), implementation impact assessment, specific artifact requirements
- Boundaries: Focus on identifying missing information and gaps, not technical accuracy or implementation methods

**Quality & Innovation Investigator** (NEW)
- Objective: Assess overall quality, innovation potential, and alignment with framework standards
- Task: "**Think hard about quality and innovation assessment**: Based on this template, investigate: Does this represent high-quality requirements gathering? Are there innovative approaches or patterns? How does it compare to framework quality standards? What's the potential for creating exceptional implementation results?"
- Output format: Quality score (1-10) with assessment of innovation potential and framework alignment
- Enhanced focus: Quality benchmarking, innovation identification, framework standards compliance
- Boundaries: Focus on quality assessment and innovation potential, not gap identification

### Phase 3: Enhanced Multi-Agent Synthesis and Scoring

**Template Type-Specific Validation**:

**For Enhanced INITIAL Templates** - Synthesize subagent findings:
- FEATURE section assessment from Implementation Readiness + Technical Accuracy
- EXAMPLES section assessment from Technical Accuracy + Completeness Gap findings
- DOCUMENTATION section assessment from Technical Accuracy + Gap identification
- OTHER CONSIDERATIONS assessment from all subagent findings
- Innovation and quality assessment from Quality Investigator

**For PRD Templates** - Synthesize subagent findings:
- Executive Summary assessment from Implementation Readiness + Completeness
- Technical Architecture assessment from Technical Accuracy + Implementation Readiness
- User Stories assessment from Completeness + Implementation clarity
- Implementation Plan assessment from all subagent perspectives
- Innovation and framework alignment from Quality Investigator

**Enhanced Scoring Matrix**:
- Implementation Readiness: [1-10] from specialist investigation
- Technical Accuracy: [1-10] from specialist investigation  
- Completeness: [1-10] from specialist investigation
- Quality & Innovation: [1-10] from specialist investigation
- **Overall Score**: Weighted average (Implementation 30%, Technical 25%, Completeness 25%, Quality 20%)
- **Risk Assessment**: Implementation risk level (Low/Medium/High)
- **Success Probability**: Single-pass success percentage

### Phase 4: Enhanced Validation Results and Persistent Reporting

**Generate Comprehensive Validation Report**:
```markdown
# VALIDATION REPORT: [Template Name]

**Generated**: [Current date and time]
**Template**: [Template file path]
**Workflow**: [Simple/Complex]
**Validation Iteration**: [Number]

## EXECUTIVE SUMMARY

**Overall Validation Score**: [X.X/10]
**Implementation Risk**: [Low/Medium/High]
**Single-Pass Success Probability**: [XX%]
**Recommendation**: [Ready/Needs Improvement/Requires Rework]

## DETAILED SUBAGENT ASSESSMENTS

### 🚀 Implementation Readiness: [X.X/10]
**Autonomous Implementation Feasibility Assessment**:

**Strengths**:
- [Specific enablers for autonomous success]
- [Clear specifications and guidance areas]
- [Implementation-ready components]

**Implementation Blockers**:
- [Critical gaps preventing autonomous implementation]
- [Ambiguous requirements needing clarification]
- [Missing implementation artifacts]

**Resource Requirements**:
- **Estimated Development Time**: [X hours/days]
- **Complexity Level**: [Low/Medium/High]
- **Technical Skill Requirements**: [Specific skills needed]

**Autonomous Success Probability**: [XX%] - [Detailed reasoning]

### 🔧 Technical Accuracy: [X.X/10]
**Technical Quality and Relevance Assessment**:

**Verified Technical Components**:
- [Confirmed codebase patterns and examples]
- [Validated documentation resources]
- [Accurate technical specifications]

**Technical Issues Identified**:
- [Inaccurate or outdated technical information]
- [Missing or broken documentation links]
- [Inconsistent technical approaches]

**Codebase Integration Assessment**:
- **Pattern Compliance**: [Excellent/Good/Needs Work]
- **Dependency Analysis**: [All dependencies identified/Some missing/Major gaps]
- **Technology Stack Alignment**: [Perfect fit/Good alignment/Mismatched]

### 📋 Completeness & Gaps: [X.X/10]
**Missing Information and Gap Analysis**:

**Critical Gaps** (Block Implementation):
- [Specific missing components that prevent implementation]
- [Essential artifacts not provided]

**High-Priority Gaps** (Impact Quality):
- [Important missing elements affecting quality]
- [Incomplete specifications in key areas]

**Medium-Priority Gaps** (Optimization Opportunities):
- [Additional information that would improve results]
- [Enhancement opportunities]

**Low-Priority Gaps** (Nice-to-Have):
- [Optional improvements]
- [Future enhancement considerations]

**Gap Impact Analysis**:
- **Implementation Blocking**: [X gaps]
- **Quality Impacting**: [X gaps]  
- **Optimization Opportunities**: [X gaps]

### ⭐ Quality & Innovation: [X.X/10]
**Quality Assessment and Innovation Potential**:

**Quality Indicators**:
- **Requirements Clarity**: [Excellent/Good/Needs Work]
- **Research Depth**: [Comprehensive/Adequate/Insufficient]
- **Framework Alignment**: [Perfect/Good/Needs Improvement]

**Innovation Assessment**:
- **Innovative Approaches**: [Identified innovative patterns or solutions]
- **Best Practices**: [Exceptional practices worth highlighting]
- **Learning Opportunities**: [Knowledge gaps turned into learning opportunities]

**Framework Standards Compliance**:
- **Documentation Standards**: [Meets/Partially meets/Below standards]
- **Implementation Patterns**: [Follows/Partially follows/Deviates from standards]
- **Quality Gates**: [All met/Most met/Several missing]

## SYNTHESIS AND RECOMMENDATIONS

### Overall Assessment
**Primary Strengths**:
- [Key areas where template excels based on all subagent findings]
- [Exceptional quality indicators]
- [Strong implementation enablers]

**Critical Issues**:
- [Key problems identified across subagent investigations]
- [Blocking factors for implementation]
- [Quality concerns requiring attention]

### Risk Assessment
**Implementation Risks**:
- **Technical Risk**: [Low/Medium/High] - [Specific technical challenges]
- **Completeness Risk**: [Low/Medium/High] - [Missing information impact]
- **Timeline Risk**: [Low/Medium/High] - [Schedule impact of gaps]
- **Quality Risk**: [Low/Medium/High] - [Potential quality issues]

**Risk Mitigation Strategies**:
- [Specific approaches to address identified risks]
- [Contingency planning recommendations]
- [Quality assurance enhancements]

## PRIORITIZED IMPROVEMENT ACTIONS

### 🔴 CRITICAL (Implementation Blocking)
**Must address before proceeding**:
1. [Specific critical fix with implementation guidance]
2. [Essential missing component with creation guidance]

**Expected Impact**: [Specific impact on implementation success]
**Effort Required**: [Time/complexity estimate]

### 🟡 HIGH PRIORITY (Quality Impact)
**Address for optimal implementation quality**:
1. [Important improvement with quality impact]
2. [Enhancement that significantly improves outcomes]

**Expected Impact**: [Quality improvement potential]
**Effort Required**: [Time/complexity estimate]

### 🟢 MEDIUM PRIORITY (Optimization)
**Consider for enhanced results**:
1. [Optimization opportunity with moderate impact]
2. [Enhancement for better user experience]

### 🔵 LOW PRIORITY (Future Enhancement)
**Nice-to-have improvements**:
1. [Future consideration items]
2. [Long-term enhancement opportunities]

## READINESS ASSESSMENT

**Current Readiness Level**:
- [Score 9-10]: ✅ **EXCELLENT - READY FOR IMPLEMENTATION**
- [Score 7-8]: ✅ **GOOD - READY WITH MINOR IMPROVEMENTS**
- [Score 5-6]: ⚠️ **ADEQUATE - PROCEED WITH CAUTION**
- [Score 3-4]: ⚠️ **NEEDS IMPROVEMENT - ADDRESS CRITICAL GAPS**
- [Score 1-2]: ❌ **INADEQUATE - REQUIRES SIGNIFICANT REWORK**

### Decision Framework

**[Based on Overall Score - Specific Recommendation]**

**For Scores 8+**:
```
✅ MULTI-AGENT VALIDATION PASSED

Template: [Name]
Overall Score: [X.X/10]
Implementation Probability: [XX%]
Quality Level: [Excellent/High]

READY FOR NEXT STEP:
Run: [Next command based on workflow type]
Expected Success Rate: [XX%]

CONFIDENCE METRICS:
- Implementation Feasibility: [Very High/High]
- Technical Quality: [Very High/High]
- Information Completeness: [Very High/High]
- Innovation Potential: [High/Medium/Low]
```

**For Scores 6-7**:
```
⚠️ VALIDATION PASSED WITH RECOMMENDATIONS

Template: [Name]
Overall Score: [X.X/10]
Implementation Probability: [XX%]
Quality Level: [Good/Adequate]

RECOMMENDED ACTIONS:
1. Address [X] high-priority improvements
2. Consider [X] critical fixes for optimal results

PROCEED OPTIONS:
- Immediate: Continue with noted limitations
- Recommended: Address key improvements first
- Optimal: Complete all high-priority items

Next Command: [Next step with confidence level]
```

**For Scores <6**:
```
❌ VALIDATION REQUIRES IMPROVEMENT

Template: [Name]
Overall Score: [X.X/10]
Implementation Risk: [High]
Quality Level: [Below Standards]

REQUIRED ACTIONS BEFORE PROCEEDING:
1. [Critical improvement #1]
2. [Critical improvement #2]
3. [Critical improvement #3]

IMPROVEMENT WORKFLOW:
1. Address critical and high-priority items
2. Re-run: /research-validate [template-file]
3. Target score ≥7.0 for safe implementation
4. Once validated, proceed with: [next command]

ESTIMATED IMPROVEMENT TIME: [Time estimate based on gap analysis]
```

## ITERATION TRACKING

**Validation History**:
- **Previous Score**: [If available]
- **Improvement**: [Score change from last validation]
- **Addressed Issues**: [Items fixed since last validation]
- **Remaining Issues**: [Persistent issues across validations]

**Progress Metrics**:
- **Implementation Readiness Trend**: [Improving/Stable/Declining]
- **Quality Trend**: [Improving/Stable/Declining]
- **Gap Closure Rate**: [XX% of gaps addressed since last validation]

## NEXT STEPS

### Immediate Actions
**Based on validation score [X.X/10]**:
- **If ≥8.0**: Proceed to [next command] with high confidence
- **If 6.0-7.9**: Address recommended improvements, then proceed
- **If <6.0**: Complete critical improvements, re-validate

### Quality Improvement Cycle
1. **Address Priority Issues**: Focus on critical and high-priority items
2. **Re-validate**: Run `/research-validate [template-file]` after improvements
3. **Track Progress**: Monitor score improvements and gap closure
4. **Optimize**: Continue iterating until target quality achieved

### Success Metrics
- **Target Score**: ≥8.0 for optimal implementation success
- **Minimum Score**: ≥6.0 for acceptable implementation risk
- **Success Probability**: ≥80% for high-confidence implementation

---
**Validation Report Complete**
**Framework**: PRP Multi-Agent Enhanced Validation v2.0
**Quality Assurance**: [Comprehensive/Standard] validation completed
**Report Saved**: [File path]
```

## Enhanced Quality Gates and Recommendations

### Excellent Templates (Score 8.5-10)
```
🎯 EXCEPTIONAL VALIDATION RESULTS

Template: [Name]
Validation Score: [X.X/10]
Innovation Level: [High/Very High]
Implementation Confidence: [Very High]

EXCEPTIONAL INDICATORS:
✅ Implementation-ready with minimal risk
✅ Technical accuracy verified and excellent
✅ Comprehensive information with innovation
✅ Framework standards exceeded

READY FOR PREMIUM IMPLEMENTATION:
Next Step: [Command] with premium success probability
Expected Results: Exceptional implementation quality
Single-Pass Success: 90-95%

QUALITY BENCHMARKS ACHIEVED:
- Requirements clarity: Exceptional
- Technical depth: Comprehensive  
- Implementation guidance: Complete
- Innovation factor: Present
```

### High-Quality Templates (Score 7-8.4)
```
✅ HIGH-QUALITY VALIDATION PASSED

Template: [Name]
Validation Score: [X.X/10]
Implementation Confidence: [High]

QUALITY INDICATORS:
✅ Strong implementation readiness
✅ Good technical accuracy and relevance
✅ Adequate completeness with minor gaps
✅ Framework standards met

READY FOR STANDARD IMPLEMENTATION:
Next Step: [Command] with good success probability
Expected Results: High-quality implementation
Single-Pass Success: 80-90%

MINOR IMPROVEMENTS RECOMMENDED:
- [1-2 specific optimizations for enhanced results]
```

### Templates Needing Targeted Improvement (Score 5-6.9)
```
⚠️ VALIDATION REQUIRES FOCUSED IMPROVEMENT

Template: [Name]  
Validation Score: [X.X/10]
Implementation Risk: [Medium/High]

IMPROVEMENT AREAS IDENTIFIED:
📊 Subagent Findings Summary:
├── Implementation Issues: [X critical, Y high-priority]
├── Technical Problems: [X accuracy issues, Y outdated refs]
├── Completeness Gaps: [X blocking, Y impacting]
└── Quality Concerns: [X standards issues, Y improvements needed]

FOCUSED IMPROVEMENT PLAN:
🔴 Phase 1 (Critical - 2-4 hours):
1. [Specific critical fix with guidance]
2. [Essential missing component]

🟡 Phase 2 (High Priority - 1-2 hours):
1. [Important quality improvement]
2. [Key enhancement for success]

📈 Expected Outcome After Improvements:
- Target Score: 7.5-8.5/10
- Implementation Success: 80-85%
- Quality Level: High

IMPROVEMENT TRACKING:
- Re-validate after Phase 1: Target ≥6.5/10
- Re-validate after Phase 2: Target ≥7.5/10
- Final validation: Target ≥8.0/10
```

### Templates Requiring Significant Rework (Score <5)
```
❌ VALIDATION INDICATES SIGNIFICANT IMPROVEMENT NEEDED

Template: [Name]  
Validation Score: [X.X/10]
Implementation Risk: [High/Very High]
Quality Level: [Below Framework Standards]

COMPREHENSIVE IMPROVEMENT REQUIRED:
🚨 Critical Issues Identified:
- Implementation Blockers: [X major issues]
- Technical Problems: [Y accuracy/relevance issues]
- Completeness Gaps: [Z missing essential components]
- Quality Standards: [Multiple framework violations]

REWORK STRATEGY:
📋 Phase 1 - Foundation Repair (4-8 hours):
1. [Address fundamental structural issues]
2. [Fix critical technical inaccuracies]
3. [Add essential missing components]

📋 Phase 2 - Quality Enhancement (2-4 hours):
1. [Improve technical specifications]
2. [Enhance implementation guidance]
3. [Add comprehensive examples]

📋 Phase 3 - Standards Compliance (1-2 hours):
1. [Ensure framework standards compliance]
2. [Add quality documentation]
3. [Final technical verification]

MILESTONE VALIDATION:
- After Phase 1: Target ≥4.0/10 (Foundation solid)
- After Phase 2: Target ≥6.0/10 (Quality adequate)  
- After Phase 3: Target ≥7.5/10 (Framework compliant)

ALTERNATIVE APPROACH:
Consider restarting with enhanced initial research if improvement effort exceeds 8-10 hours
```

## Key Multi-Agent Benefits Enhanced

- **Independent validation perspectives** from 4 specialist angles with enhanced focus areas
- **Comprehensive gap identification** through focused investigation with prioritization
- **Higher confidence scoring** through cross-verification and enhanced metrics
- **Specific improvement guidance** based on specialist findings with effort estimates
- **Persistent validation reports** enabling iteration tracking and improvement cycles
- **Risk assessment integration** providing implementation confidence levels
- **Quality benchmarking** against framework standards with innovation recognition
- **Success probability calculation** enabling informed decision-making

## Innovation Features

### Advanced Analytics
- **Trend Analysis**: Track validation score improvements over time
- **Gap Closure Metrics**: Measure improvement effectiveness
- **Quality Benchmarking**: Compare against framework excellence standards
- **Risk Prediction**: Assess implementation failure probability

### Workflow Intelligence  
- **Auto-Detection**: Automatically determine workflow type and next steps
- **Context Preservation**: Maintain validation history across iterations
- **Integration Ready**: Seamless integration with both simple and complex workflows
- **Framework Evolution**: Support for future framework enhancements

Enhanced validation ensuring template readiness for successful PRP generation and autonomous implementation with comprehensive quality assurance and continuous improvement support.