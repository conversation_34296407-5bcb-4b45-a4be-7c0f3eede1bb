# Complex Workflow Step 6: Create Module PRP

## Command Purpose
Transform enhanced module specifications into comprehensive, implementation-ready PRPs for autonomous single-module development with complete project context preservation.

## Development Standards Integration
**CRITICAL**: All module PRP creation must strictly follow the comprehensive coding standards defined in:
**File Reference**: `PRPs/ai_docs/development_standards.md`

**Application Instructions**: Read and apply ALL standards from the referenced file when creating module PRPs. Key enforcement areas include:
- File and function length limits (500/50 lines)
- AI agent patterns (module-global reuse only)
- Configuration factory patterns for environment variables
- Type safety, package management, and testing strategies
- Code quality tools (<PERSON><PERSON>, MyPy) and project organization standards

**Validation Requirement**: Each investigator must validate their PRP creation against the complete standards document.
**Project Integration**: Must integrate with PROJECT-STRUCTURE/CLAUDE.md when available.

## Usage
```
/complex-6-create-module-prp PRPs/project-planning/[feature]/MODULE-SPECS/[module-spec].md
```

### Examples
```bash
/complex-6-create-module-prp PRPs/project-planning/memory-orchestrator/MODULE-SPECS/F1-vector-storage-foundation.md
/complex-6-create-module-prp PRPs/project-planning/ai-assistant/MODULE-SPECS/C1-conversation-manager-core.md  
/complex-6-create-module-prp PRPs/project-planning/data-pipeline/MODULE-SPECS/I1-external-api-integration.md
```

## Auto-Detection Features
When provided with a module specification, the command automatically:
- **Detects project context**: Reads 5-project-plan and analysis folder for complete project understanding
- **Detects project structure**: Reads PROJECT-STRUCTURE/CLAUDE.md for development standards and architecture
- **Preserves module focus**: Maintains specific module boundaries while integrating project context
- **Sets output location**: Creates module PRP in `PRPs/modules/` with clear naming convention: `6-module-prp-[module].md`
- **Context synthesis**: Combines module specification with project architecture and implementation research

## Input Requirements
- **Primary Input**: `PRPs/project-planning/[feature]/MODULE-SPECS/[module-spec].md` (enhanced module specification)
- **Auto-Detected Files**: 
  - `PRPs/project-planning/[feature]/5-project-plan-[feature].md` (project context)
  - `PRPs/project-planning/[feature]/PROJECT-STRUCTURE/CLAUDE.md` (development standards and structure)
  - `PRPs/project-planning/[feature]/analysis/` (complete research context)
  - Other `MODULE-SPECS/` files (integration context)
- **Validation**: Module specification must be from enhanced project planning process

## Output Generated
- **Module PRP**: `PRPs/modules/6-module-prp-[module-name].md` (comprehensive implementation PRP)
- **Implementation Readiness**: Single-pass autonomous implementation capability
- **Quality Assurance**: Built-in validation and testing strategies

### File Organization Benefits
- **Module Focus**: Specific module PRP while maintaining project context
- **Clean Organization**: Module PRPs organized in dedicated `PRPs/modules/` directory
- **Context Preservation**: All project and research context integrated with module focus
- **Implementation Ready**: Complete PRPs for autonomous module development

## Specialist Investigators

You are the **Module PRP Coordinator** managing 5 specialist investigators to transform enhanced module specifications into implementation-ready PRPs with complete project context.

### Enhanced Module-Focused Strategy
This command utilizes **comprehensive context synthesis** for focused module implementation:
- **Module Specification Input**: Enhanced module spec with rich implementation guidance
- **Project Context Input**: Complete project plan and research context  
- **Integration Context**: Other module specifications for interface understanding
- **Synthesis Goal**: Autonomous implementation-ready PRP for specific module

### Investigator 1: Module Context Investigator
**Role**: Extract and synthesize comprehensive module-specific requirements with project context integration

**Your Task**: **Think hard about module context synthesis** - Analyze the specific module specification and integrate it with complete project context to create focused implementation requirements.

**Analysis Focus**:
1. **Module Requirement Synthesis**:
   - Extract specific module responsibilities and boundaries from specification
   - Integrate module requirements with overall project architecture
   - **ENFORCE**: development_standards.md requirements (file/function limits, AI agent patterns)
   - Synthesize module-specific technology requirements with project technology stack
   - Preserve rich implementation context from enhanced module specification

2. **Project Context Integration**:
   - Connect module specification to overall project plan and architecture
   - Understand module's role within the broader system design
   - Identify project-level constraints and standards that apply to this module
   - Ensure module implementation aligns with project-wide patterns and conventions

3. **Technology Context Specialization**:
   - Focus project's technology stack on module-specific implementation needs
   - Identify module-specific frameworks, libraries, and tools required
   - Extract relevant patterns and examples from project research that apply to this module
   - Specify technology-specific implementation approaches for module boundaries

4. **Interface and Dependency Analysis**:
   - Analyze module's interfaces with other project modules based on complete MODULE-SPECS
   - Identify external dependencies specific to this module
   - Map data flows and communication patterns relevant to module implementation
   - Ensure module integration points are clearly defined and implementable

**Provide**:
- Focused module requirements with complete project context
- Technology-specific implementation needs for the module
- Clear interface specifications and dependency mappings
- Integration strategy with project architecture and other modules

### Investigator 2: Project Integration Investigator
**Role**: Ensure seamless module integration within the broader project ecosystem

**Your Task**: **Think hard about project integration strategy** - Analyze how this specific module fits within the complete project architecture and ensure proper integration patterns.

**Analysis Focus**:
1. **Architectural Alignment Analysis**:
   - Verify module boundaries align with project's overall architecture
   - Ensure module responsibilities don't overlap with other project modules
   - Validate module interfaces match project's integration patterns
   - Confirm module complexity and scope align with project planning

2. **Cross-Module Integration Planning**:
   - Analyze module's communication patterns with other project modules
   - Design integration testing approaches for module boundaries
   - Plan for module's role in project's overall data flow and processing
   - Ensure module's error handling integrates with project-wide error management

3. **Project Standards Compliance**:
   - **APPLY**: development_standards.md coding standards (500/50 line limits, type safety)
   - **REQUIRE**: Pydantic AI agent module-global reuse patterns
   - **ENFORCE**: Configuration factory pattern for environment variable respect
   - Ensure module's testing strategy aligns with project's quality framework
   - Integrate module's documentation approach with project documentation standards
   - Apply project's deployment and operational patterns to module

4. **Development Workflow Integration**:
   - Plan module development within project's overall development phases
   - Ensure module's development timeline aligns with project dependencies
   - Integrate module's quality gates with project's validation framework
   - Plan module's integration checkpoints with other project components

**Provide**:
- Complete integration strategy for module within project ecosystem
- Cross-module communication and testing approaches
- Project standards application to module implementation
- Development workflow coordination with project timeline

### Investigator 3: Implementation Blueprint Investigator
**Role**: Create detailed, step-by-step implementation plan for autonomous module development

**Your Task**: **Ultrathink this comprehensive implementation blueprint** - Transform module specification and project context into executable implementation blueprint for autonomous development.

**Analysis Focus**:
1. **Implementation Phase Planning**:
   - Break module implementation into logical development phases
   - Sequence implementation tasks based on dependencies and complexity
   - **VALIDATE**: Each phase respects development_standards.md quality gates
   - Plan for iterative development with validation checkpoints
   - Identify critical path items that must be completed first

2. **Code Organization Strategy**:
   - Design file structure and organization for module implementation
   - Plan class/function architecture based on module's responsibilities
   - Organize implementation to support module's interface requirements
   - Ensure code organization aligns with project patterns and standards

3. **Technology Implementation Approach**:
   - Specify framework-specific implementation patterns for module
   - Detail library usage and configuration for module's technology needs
   - Plan for module's integration with project's technology stack
   - Include performance optimization strategies specific to module's requirements

4. **Development Automation Planning**:
   - Plan automated testing approach for module development
   - Design continuous integration pipeline for module
   - Plan for module's integration with project's build and deployment systems
   - Include development tooling and debugging strategies

**Provide**:
- Detailed implementation phases with clear task sequencing
- Complete code organization and architecture plan
- Technology-specific implementation guidance
- Development automation and tooling strategy

### Investigator 4: Testing & Validation Investigator
**Role**: Design comprehensive testing strategy and validation framework for module implementation

**Your Task**: **Think hard about comprehensive testing strategy** - Create complete testing and validation approach ensuring module quality and integration success.

**Analysis Focus**:
1. **Module-Specific Testing Strategy**:
   - Design unit testing approach based on module's functionality and complexity
   - Plan integration testing for module's interfaces with other project components
   - Create performance testing strategy based on module's performance requirements
   - Design security testing approach based on module's security responsibilities

2. **Project Integration Testing**:
   - Plan testing for module's integration with project's overall architecture
   - Design validation approach for module's compliance with project standards
   - Create testing strategy for module's role in project's end-to-end workflows
   - Plan for module's testing within project's overall quality framework

3. **Validation Framework Design**:
   - Create acceptance criteria and validation checkpoints for module development
   - Design automated validation approach for module's implementation progress
   - Plan for module's quality gates within project's development workflow
   - Create testing documentation and reporting strategy

4. **Error Handling and Edge Case Testing**:
   - Identify module-specific error conditions and edge cases
   - Plan testing approach for module's error handling and recovery
   - Design validation for module's behavior under various failure conditions
   - Create testing strategy for module's integration failure scenarios

**Provide**:
- Comprehensive testing strategy for all aspects of module implementation
- Project integration testing and validation approach
- Automated validation framework for module development
- Error handling and edge case testing strategy

### Investigator 5: Quality Assurance Investigator
**Role**: Ensure PRP quality and completeness for autonomous implementation success

**Your Task**: **Think carefully about implementation completeness validation** - Validate that the module PRP contains sufficient context and guidance for reliable autonomous implementation.

**Analysis Focus**:
1. **Implementation Completeness Validation**:
   - Verify module PRP contains all necessary context for autonomous implementation
   - Ensure implementation guidance is specific and actionable
   - Validate that technology-specific patterns and examples are included
   - Confirm that module boundaries and interfaces are clearly defined

2. **Project Context Integration Validation**:
   - Verify module PRP properly integrates project architecture and standards
   - Ensure cross-module dependencies and interfaces are clearly specified
   - Validate that project-wide patterns and conventions are properly applied
   - Confirm module's role in project workflow is well-defined

3. **Quality Standards Compliance**:
   - Verify module PRP meets framework's quality standards for autonomous implementation
   - Ensure testing and validation strategies are comprehensive and actionable
   - Validate that error handling and edge cases are adequately addressed
   - Confirm that success criteria and acceptance testing are well-defined

4. **Single-Pass Implementation Readiness**:
   - Assess module PRP's readiness for autonomous implementation without iteration
   - Identify any gaps or ambiguities that could prevent successful implementation
   - Validate that implementation guidance is sufficient for expected complexity level
   - Ensure module PRP enables high-confidence autonomous development

**Provide**:
- Complete quality assessment with implementation readiness score
- Gap analysis and recommendations for PRP improvement
- Validation of autonomous implementation capability
- Quality assurance framework for module development success

## Command Execution Process

1. **Input Validation and Auto-Detection**:
   ```bash
   # Validate module specification file
   MODULE_SPEC_FILE="$ARGUMENTS"
   
   if [[ ! -f "$MODULE_SPEC_FILE" ]]; then
       echo "❌ ERROR: Module specification file '$MODULE_SPEC_FILE' not found"
       echo "Expected: PRPs/project-planning/[feature]/MODULE-SPECS/[module-spec].md"
       exit 1
   fi
   
   # Extract project and module information
   PROJECT_DIR=$(dirname $(dirname "$MODULE_SPEC_FILE"))
   PROJECT_NAME=$(basename "$PROJECT_DIR")
   MODULE_NAME=$(basename "$MODULE_SPEC_FILE" .md)
   
   # Auto-detect project context files
   PROJECT_PLAN="${PROJECT_DIR}/5-project-plan-${PROJECT_NAME}.md"
   PROJECT_STRUCTURE_CLAUDE="${PROJECT_DIR}/PROJECT-STRUCTURE/CLAUDE.md"
   ANALYSIS_DIR="${PROJECT_DIR}/analysis"
   MODULE_SPECS_DIR="${PROJECT_DIR}/MODULE-SPECS"
   
   echo "🎯 Project: $PROJECT_NAME"
   echo "🎯 Module: $MODULE_NAME"
   echo "📁 Project directory: $PROJECT_DIR"
   echo "📁 Analysis directory: $ANALYSIS_DIR"
   
   # Load project context files
   if [[ -f "$PROJECT_PLAN" ]]; then
       echo "✅ Project plan detected: $(basename "$PROJECT_PLAN")"
       PROJECT_PLAN_CONTEXT=$(cat "$PROJECT_PLAN")
   else
       echo "⚠️ Project plan not found: $(basename "$PROJECT_PLAN")"
       PROJECT_PLAN_CONTEXT=""
   fi
   
   # Load project structure and development standards
   if [[ -f "$PROJECT_STRUCTURE_CLAUDE" ]]; then
       echo "✅ Project structure detected: PROJECT-STRUCTURE/CLAUDE.md"
       PROJECT_STRUCTURE_CONTEXT=$(cat "$PROJECT_STRUCTURE_CLAUDE")
   else
       echo "⚠️ PROJECT-STRUCTURE/CLAUDE.md not found - using default development standards"
       PROJECT_STRUCTURE_CONTEXT=""
   fi
   
   # Load analysis context if available
   if [[ -d "$ANALYSIS_DIR" ]]; then
       echo "✅ Analysis directory detected with research context"
       ANALYSIS_CONTEXT=""
       for file in "$ANALYSIS_DIR"/*.md; do
           if [[ -f "$file" ]]; then
               ANALYSIS_CONTEXT="${ANALYSIS_CONTEXT}\n$(cat "$file")"
           fi
       done
   else
       echo "⚠️ Analysis directory not found"
       ANALYSIS_CONTEXT=""
   fi
   
   # Load other module specifications for integration context
   MODULE_INTEGRATION_CONTEXT=""
   for file in "$MODULE_SPECS_DIR"/*.md; do
       if [[ -f "$file" && "$file" != "$MODULE_SPEC_FILE" ]]; then
           MODULE_INTEGRATION_CONTEXT="${MODULE_INTEGRATION_CONTEXT}\n$(cat "$file")"
       fi
   done
   ```

2. **Output Directory Setup**:
   - Create `PRPs/modules/` directory structure
   - Set up module-specific PRP organization
   - Prepare output file: `PRPs/modules/PRP-[module-name].md`

3. **Enhanced Specialist Investigation Coordination**:
   - **Ultrathink this focused module PRP creation** - Execute all 5 specialist investigators in parallel
   - Cross-validate findings between investigators for consistency
   - Ensure comprehensive coverage: module context, project integration, implementation, testing, quality
   - Synthesize multi-input context (module spec + project context + integration context)

4. **Output Generation**:
   - Generate comprehensive module PRP in `PRPs/modules/PRP-[module-name].md`
   - Validate PRP completeness and implementation readiness
   - Ensure quality standards meet autonomous implementation requirements

## Output Template: `PRPs/modules/PRP-[module-name].md`

### Complete Module PRP Structure

```markdown
# Module Implementation PRP: [Module Name]

## Executive Summary
- **Module Purpose**: [Single responsibility and core functionality]
- **Project Context**: [Role within broader project architecture]
- **Implementation Scope**: [Specific deliverables and boundaries]
- **Success Criteria**: [Measurable completion criteria]

## Module Context & Requirements

### Module Specification Summary
- **Module Type**: [Foundation/Core/Integration]
- **Complexity Score**: [X/10 with justification]
- **Project Role**: [How this module serves the overall project]
- **Technology Focus**: [Primary technologies and frameworks]

### Project Integration Context
- **Project Architecture**: [How module fits within overall system design]
- **Cross-Module Dependencies**: [Other modules this depends on or that depend on this]
- **Interface Requirements**: [APIs, data contracts, communication protocols]
- **Project Standards**: [Coding standards, patterns, and conventions to follow]

### Module-Specific Requirements
- **Functional Requirements**: [What the module must accomplish]
- **Performance Requirements**: [Response times, throughput, resource usage]
- **Security Requirements**: [Authentication, authorization, data protection]
- **Operational Requirements**: [Monitoring, logging, error handling]

## Implementation Blueprint

### Development Phases
**Phase 1: Foundation Setup**
- [Specific tasks and deliverables for module foundation]
- [Dependencies and prerequisites]
- [Validation checkpoints]

**Phase 2: Core Implementation**
- [Core functionality development tasks]
- [Interface implementation and testing]
- [Integration with project components]

**Phase 3: Integration & Validation**
- [Cross-module integration tasks]
- [Comprehensive testing and validation]
- [Performance optimization and monitoring setup]

### Code Organization Strategy
```
[module-directory]/
├── [core-implementation]/     # Main module functionality
├── [interfaces]/              # Module boundary definitions
├── [tests]/                   # Comprehensive test suite
├── [docs]/                    # Module-specific documentation
└── [config]/                  # Module configuration
```

### Technology Implementation Approach
- **Framework Integration**: [Specific framework usage patterns for this module]
- **Library Dependencies**: [Required libraries and their configuration]
- **Data Management**: [Database, storage, and data handling approaches]
- **Communication Patterns**: [How module communicates with other project components]

### Interface Implementation
- **Module APIs**: [Detailed API specifications and implementations]
- **Data Contracts**: [Input/output data structures and validation]
- **Error Handling**: [Module-specific error management and reporting]
- **Integration Points**: [How other modules interact with this module]

## Comprehensive Testing Strategy

### Unit Testing Approach
- **Test Framework**: [Testing framework and configuration for this module]
- **Test Coverage**: [Coverage targets and critical test areas]
- **Test Organization**: [Test file structure and naming conventions]
- **Mock Strategies**: [Mocking external dependencies and interfaces]

### Integration Testing Strategy
- **Cross-Module Testing**: [Testing integration with other project modules]
- **External Service Testing**: [Testing integration with external services]
- **Interface Testing**: [Testing module's API contracts and data flows]
- **Performance Testing**: [Load testing and performance validation]

### Module Validation Framework
- **Acceptance Criteria**: [Detailed criteria for module completion]
- **Quality Gates**: [Quality checkpoints throughout development]
- **Automated Validation**: [Automated testing and validation pipeline]
- **Manual Testing**: [Manual testing procedures and checklists]

## Quality Assurance Framework

### Code Quality Standards
- **Development Standards**: Follow development_standards.md requirements:
  - File length: 500 lines maximum per file
  - Function length: 50 lines maximum per function
  - AI agents: Module-global constants (never per-call instantiation)
  - Configuration: Factory pattern for environment variable respect
  - Type safety: Complete type hints for all functions
- **Documentation Requirements**: [Code documentation and module documentation standards]
- **Code Review Process**: [Review criteria and process for module development]
- **Quality Metrics**: [Metrics to track and maintain for module quality]

### Error Handling & Edge Cases
- **Error Scenarios**: [Specific error conditions and handling strategies]
- **Edge Case Management**: [Edge cases and boundary condition handling]
- **Failure Recovery**: [Recovery strategies and graceful degradation]
- **Monitoring & Alerting**: [Operational monitoring and alerting for module]

### Performance & Optimization
- **Performance Targets**: [Specific performance requirements for module]
- **Optimization Strategies**: [Performance optimization approaches]
- **Resource Management**: [Memory, CPU, and resource usage optimization]
- **Scalability Considerations**: [How module scales with increased load]

## Development Environment & Tools

### Local Development Setup
- **Environment Requirements**: [Development environment setup for module]
- **Configuration Management**: [Environment variables and configuration setup]
- **Development Tools**: [IDEs, debuggers, profilers, and development utilities]
- **Database/Service Setup**: [Local services and dependencies setup]

### Build & Deployment Pipeline
- **Build Process**: [Module build pipeline and automation]
- **Testing Automation**: [Automated testing integration with build process]
- **Deployment Strategy**: [Module deployment approach and automation]
- **Monitoring Integration**: [Operational monitoring and observability setup]

## Implementation Validation & Success Criteria

### Module Completion Criteria
- [ ] **Core Functionality**: [Specific functional requirements completed]
- [ ] **Development Standards Compliance**: All development_standards.md requirements met:
  - [ ] File length limits enforced (500 lines max)
  - [ ] Function length limits enforced (50 lines max)
  - [ ] AI agent patterns implemented (module-global reuse)
  - [ ] Configuration factory patterns applied
  - [ ] Type safety validated with comprehensive type hints
- [ ] **Interface Implementation**: [All required interfaces implemented and tested]
- [ ] **Integration Testing**: [Cross-module integration validated]
- [ ] **Performance Validation**: [Performance targets met and validated]
- [ ] **Quality Standards**: [Code quality and documentation standards met]
- [ ] **Testing Coverage**: [Comprehensive testing completed with required coverage]

### Project Integration Validation
- [ ] **Architectural Compliance**: [Module aligns with project architecture]
- [ ] **Standards Compliance**: [Module follows project coding and quality standards]
- [ ] **Interface Compatibility**: [Module interfaces work correctly with other components]
- [ ] **Performance Integration**: [Module performance integrates well with overall system]

### Autonomous Implementation Confidence
- **Implementation Readiness Score**: [X/10]
- **Single-Pass Success Probability**: [X%]
- **Risk Assessment**: [Low/Medium/High risk factors and mitigation]
- **Support Requirements**: [Any additional support or clarification needed]

## Next Steps

### Implementation Workflow
1. **Environment Setup**: Set up development environment and dependencies
2. **Foundation Development**: Implement core module structure and interfaces
3. **Functionality Implementation**: Develop module's core functionality
4. **Testing & Validation**: Comprehensive testing and quality validation
5. **Integration Testing**: Cross-module and system integration testing
6. **Documentation & Completion**: Final documentation and module completion

### Ready for Autonomous Implementation
```bash
# Execute this module implementation:
/complex-7-implement-module PRPs/modules/6-module-prp-[module-name].md
```

---
**Module PRP Status**: ✅ Complete
**Implementation Readiness**: [High/Medium/Low] confidence for autonomous development
**Quality Gate**: Ready for `/complex-7-implement-module` command
**Success Probability**: [X%] single-pass implementation success
```

## Success Criteria

- **Complete Module PRP**: Comprehensive implementation strategy focused on specific module
- **Project Context Integration**: Module PRP properly integrated with overall project context
- **Implementation Readiness**: Sufficient detail for autonomous module implementation
- **Quality Assurance**: Comprehensive testing and validation strategies
- **Technology Alignment**: Module implementation approach matches project technology stack
- **Integration Planning**: Clear module boundaries and cross-module communication strategies

## Anti-Patterns to Avoid

- ❌ **Context Loss**: Losing project context while focusing on module specifics
- ❌ **Interface Ambiguity**: Unclear module boundaries or integration points
- ❌ **Technology Mismatch**: Module implementation not aligned with project technology
- ❌ **Testing Gaps**: Insufficient testing strategy for module complexity
- ❌ **Quality Shortcuts**: Reduced quality standards due to module focus
- ❌ **Integration Assumptions**: Assuming integration will work without explicit planning

## Enhanced Success Indicators

- ✅ **Comprehensive Module Context**: Module requirements integrated with complete project understanding
- ✅ **Clear Implementation Path**: Step-by-step implementation blueprint for autonomous development
- ✅ **Project Integration Strategy**: Clear integration approach with other project modules
- ✅ **Quality Assurance Framework**: Comprehensive testing and validation strategies
- ✅ **Technology Alignment**: Module implementation aligned with project standards and patterns
- ✅ **Autonomous Implementation Readiness**: PRP enables single-pass implementation success

The module PRP creation transforms enhanced module specifications into autonomous implementation-ready context packages, enabling focused module development while maintaining complete project integration.
