# Simple Workflow Step 3: Create Base PRP

Generate a complete PRP with multi-agent research synthesis and comprehensive context organization.

## Development Standards Integration
**CRITICAL**: All PRP creation must align with the comprehensive coding standards defined in:
**File Reference**: `PRPs/ai_docs/development_standards.md`

**Application Instructions**: Read and apply ALL standards from the referenced file when creating implementation guidance. Key integration areas include:
- File and function length limits (500/50 lines) must be specified in implementation blueprints
- AI agent patterns (module-global reuse only) must be embedded in context organization
- Configuration factory patterns must be included in implementation strategies
- Type safety, package management, and testing requirements must be integrated
- Code quality tools (Ruff, MyPy) and organization standards must be referenced

**Validation Requirement**: Each specialist investigator must ensure their PRP guidance supports complete standards compliance.

## Feature: $ARGUMENTS

Use independent subagents to investigate specific PRP creation questions and synthesize all research findings into implementation-ready context for autonomous development success.

## Prerequisites
- You should have a validated template (1-enhanced-initial-[feature-name].md) from `/simple-2-validate-initial`
- <PERSON><PERSON><PERSON> should have scored 8-10/10 for implementation readiness
- All research findings should be comprehensive and verified

## Multi-Agent PRP Creation Process

### Phase 1: Input Analysis and Strategy (Lead Agent)
**Objective**: Understand input type and plan comprehensive PRP synthesis

1. **Input Type Detection and Analysis**
   ```bash
   # Determine input type and read comprehensive content
   cat "$ARGUMENTS"
   
   if [[ "$ARGUMENTS" == *"PRD-"* ]]; then
       echo "PRD input detected - complex feature PRP creation"
       INPUT_TYPE="prd"
   elif [[ "$ARGUMENTS" == *"1-enhanced-initial-"* ]]; then
       echo "Enhanced INITIAL.md detected - feature PRP creation"
       INPUT_TYPE="enhanced_initial"
   else
       echo "Basic input detected - standard PRP creation"
       INPUT_TYPE="basic"
   fi
   ```

2. **Research Context Assessment**
   - Evaluate comprehensiveness of research findings
   - Identify synthesis requirements for PRP creation
   - Plan subagent specialization for optimal context organization

### Phase 2: Parallel Subagent Investigation

Deploy specialist subagents for comprehensive PRP synthesis:

**Research Synthesis Investigator**
- Objective: Investigate and synthesize all research findings into comprehensive implementation context
- Task: "Based on the input template and research findings, investigate: What are all the research insights that need to be synthesized? Combine market research, technical findings, architectural decisions, and implementation requirements into unified context. Ensure all external research (documentation, examples, competitive analysis) is properly integrated."
- Tools: Use analysis and synthesis to combine all research findings
- Output format: Comprehensive research synthesis with all findings integrated and cross-referenced
- Boundaries: Focus on research integration and synthesis, not implementation planning

**Implementation Blueprint Investigator** 
- Objective: Investigate optimal implementation approach and create detailed autonomous development roadmap
- Task: "Based on the synthesized research, investigate: What is the optimal implementation approach? Create detailed step-by-step blueprint for autonomous development. Define clear tasks, dependencies, and validation checkpoints. Plan error handling and edge case management strategies."
- Tools: Use codebase analysis and pattern recognition for implementation planning
- Output format: Detailed implementation blueprint with task sequence, dependencies, and validation gates
- Boundaries: Focus on implementation roadmap and task planning, not context organization

**Context Organization Investigator**
- Objective: Investigate context needs and organize all information for maximum autonomous implementation success
- Task: "Based on all research and implementation requirements, investigate: How should context be organized for autonomous success? Structure documentation references, code examples, gotchas, and patterns for optimal AI agent consumption. Ensure critical context is comprehensive and accessible."
- Tools: Use information architecture and context optimization techniques
- Output format: Optimally organized context structure with comprehensive information accessibility
- Boundaries: Focus on context organization and information architecture, not validation

**PRP Quality Validation Investigator**
- Objective: Investigate PRP completeness and validate for single-pass implementation readiness
- Task: "Based on the developing PRP components, investigate: Is the PRP complete for single-pass autonomous implementation? Validate context completeness, implementation clarity, validation gate effectiveness, and success probability. Identify any gaps or improvements needed."
- Output format: PRP quality assessment with specific completeness validation and success probability scoring
- Boundaries: Focus on quality validation and completeness assessment, not content creation

### Phase 3: PRP Synthesis and Generation

**Synthesize all subagent findings using `PRPs/templates/prp_base_multiagent.md`:**

**Comprehensive Context Integration**:
```markdown
## Context & Research Findings
[From Research Synthesis Investigator - all research integrated]

### Documentation References
[Verified URLs with specific sections from research synthesis]

### Code Examples & Patterns  
[Real codebase snippets and patterns from context organization]

### Implementation Insights
[Technical findings, gotchas, and best practices from synthesis]
```

**Implementation Blueprint Creation**:
```markdown
## Implementation Approach
[From Implementation Blueprint Investigator - detailed roadmap]

### Task Sequence
[Step-by-step implementation tasks with dependencies]

### Validation Checkpoints
[Specific validation gates with executable commands]

### Error Handling Strategy
[Comprehensive error management approach]
```

**Context Architecture Optimization**:
```markdown
## Critical Implementation Context
[From Context Organization Investigator - optimized information structure]

### Autonomous Implementation Enablers
[All context needed for single-pass success]

### Reference Architecture
[Clear patterns and examples to follow]

### Success Criteria & Validation
[Measurable outcomes and validation approaches]
```

### Phase 4: Quality Validation and Optimization

**Multi-Agent Quality Assessment**:
- Validate research synthesis completeness from all previous work
- Confirm implementation blueprint enables autonomous development
- Verify context organization supports single-pass success
- Assess overall PRP readiness through specialist validation

**PRP Quality Matrix**:
- Research Integration: [1-10] from synthesis specialist
- Implementation Clarity: [1-10] from blueprint specialist  
- Context Organization: [1-10] from context specialist
- Overall Readiness: [1-10] from validation specialist

## Enhanced PRP Template Generation

**Complete PRP Structure** using multi-agent synthesis:

```markdown
# [Feature Name] - Implementation PRP

## Executive Summary
[Synthesized problem statement and solution approach]

## Comprehensive Context
[All research findings integrated and organized]

## Implementation Blueprint  
[Detailed autonomous development roadmap]

## Validation Framework
[Executable validation gates and success criteria]

## Quality Assurance
[Error handling, edge cases, and completion verification]
```

## Output and Quality Assessment

### PRP Location
Save as: `PRPs/3-base-prp-[feature-name].md`

### Multi-Agent PRP Creation Summary
```
MULTI-AGENT PRP CREATION COMPLETE: [Feature Name]

SUBAGENT SYNTHESIS RESULTS:
├── Research Synthesis: [Comprehensive integration of X research findings]
├── Implementation Blueprint: [Y-step detailed roadmap with dependencies]
├── Context Organization: [Optimized structure for autonomous consumption]
└── Quality Validation: [Z/10 single-pass implementation confidence]

PRP QUALITY METRICS:
- Research Integration Score: [X/10]
- Implementation Clarity Score: [X/10]  
- Context Organization Score: [X/10]
- Overall Readiness Score: [X/10]

AUTONOMOUS IMPLEMENTATION CONFIDENCE: [8-10]/10
Expected single-pass success probability: [85-95%]

READY FOR EXECUTION: ✅
Next step: /simple-4-implement-feature PRPs/3-base-prp-[feature-name].md
```

### Enhanced Quality Checklist
- [ ] **Research Synthesis**: All multi-agent findings integrated comprehensively
- [ ] **Implementation Blueprint**: Clear autonomous development roadmap with dependencies
- [ ] **Context Organization**: Information structured for optimal AI agent consumption  
- [ ] **Validation Framework**: Executable gates enabling continuous quality assurance
- [ ] **Quality Validation**: Multi-agent assessment confirms single-pass readiness

## Key Multi-Agent Benefits

- **Comprehensive research integration** from all previous multi-agent work
- **Specialist synthesis** of different PRP components  
- **Optimized context organization** for autonomous implementation success
- **Independent quality validation** ensuring PRP completeness
- **Higher success probability** through multi-perspective PRP creation

Enhanced PRP ready for autonomous implementation with maximum context and clarity.
