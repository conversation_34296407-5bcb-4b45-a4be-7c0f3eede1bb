# INITIAL.md Scope & Complexity Analysis (Multi-Agent Enhanced)

Analyze the filled-out INITIAL.md: $ARGUMENTS

Use independent subagents to investigate scope requirements and complexity questions to provide comprehensive analysis for workflow path determination.

## Prerequisites
You must have a filled-out INITIAL.md file to analyze. If you don't have one:
1. Copy the INITIAL.md template: `cp INITIAL.md my-[feature-name].md`
2. Fill out all sections with your current understanding
3. Then run this analysis command

## Multi-Agent Analysis Process

### 1. Initial Assessment & Scope Detection (Lead Agent)
Read and perform initial evaluation of the filled-out template:

```bash
# Read the INITIAL.md file
cat "$ARGUMENTS"
```

**Scope Detection**:
- Check for SCOPE field in the INITIAL.md file
- Identify user's development approach: prototype vs production
- Note scope implications for workflow routing

**Initial Complexity Assessment** (if production scope):
- Feature scope and functionality description
- Number of examples and patterns referenced
- Documentation domains and technologies listed
- Integration points and considerations mentioned

### 2. Routing Decision Logic

**Scope-Based Routing**:
- **SCOPE: prototype** → Route directly to prototype workflow (complexity analysis not needed)
- **SCOPE: production** → Proceed with multi-agent complexity analysis for production routing

### 3. Multi-Agent Complexity Analysis (Production Scope Only)

Deploy independent subagents to investigate specific complexity questions:

**Technical Implementation Investigator**
- Objective: Investigate the technical implementation complexity
- Task: "Analyze the FEATURE section and verify technical complexity. Consider: How many different technologies are involved? Are the requirements technically straightforward or do they involve complex integrations? What's the implementation scope?"
- Output format: Technical complexity assessment (Low/Medium/High) with specific reasoning

**Architectural Impact Investigator** 
- Objective: Verify architectural and system design impacts
- Task: "Examine the EXAMPLES and OTHER CONSIDERATIONS sections. Investigate: Does this require new architectural patterns? How many existing systems will be affected? Are there significant design decisions needed?"
- Output format: Architectural impact assessment (Minimal/Moderate/Significant) with specific reasoning

**Integration Complexity Investigator**
- Objective: Investigate cross-system integration requirements
- Task: "Analyze the DOCUMENTATION and OTHER CONSIDERATIONS sections. Verify: How many external systems or APIs are involved? What's the integration complexity? Are there dependency risks?"
- Output format: Integration complexity assessment (Simple/Moderate/Complex) with specific reasoning

### 4. Synthesis and Determination

**For Production Scope**: Combine subagent findings with initial assessment to determine:

**Complexity Scoring Matrix**:
- Technical: Low/Medium/High
- Architectural: Minimal/Moderate/Significant  
- Integration: Simple/Moderate/Complex

**Decision Logic**:
- **Simple Feature** (→ research-initial): Majority Low/Minimal/Simple scores
- **Complex Feature** (→ research-prd): Any High/Significant/Complex scores OR multiple Medium/Moderate scores

### 5. Enhanced Recommendation Output

**For Prototype Scope**:
```
SCOPE & COMPLEXITY ANALYSIS: [Feature Name from file]

SCOPE DETECTED: prototype
- Development Approach: Rapid concept validation with proper patterns
- Focus: Minimal working version to learn constraints and prove feasibility

RECOMMENDED PATH:
PROTOTYPE WORKFLOW:
  - Run: /research-initial-prototype "$ARGUMENTS"
  - Approach: Build minimal working version with best practices
  - Expected Output: Structured prototype with good patterns for production scaling
  - Timeline: Rapid development focused on constraint discovery

SCOPE CONFIDENCE: High - Clear prototype scope detected
```

**For Production Scope**:
```
SCOPE & COMPLEXITY ANALYSIS: [Feature Name from file]

SCOPE DETECTED: production
- Development Approach: Comprehensive production-ready system

SUBAGENT FINDINGS:
├── Technical Implementation: [Low/Medium/High] - [specific reasoning]
├── Architectural Impact: [Minimal/Moderate/Significant] - [specific reasoning]  
└── Integration Complexity: [Simple/Moderate/Complex] - [specific reasoning]

SYNTHESIS:
- Overall Complexity: [Simple/Complex]
- Primary Risk Factors: [list key risks identified]
- Implementation Scope: [estimated scope based on analysis]

RECOMMENDED PATH:
[Option A] SIMPLE PRODUCTION WORKFLOW:
  - Run: /research-initial "$ARGUMENTS"
  - Confidence from multi-agent analysis: [8-10]/10
  - Key focus areas: [based on subagent findings]

[Option B] COMPLEX PRODUCTION WORKFLOW:
  - Complexity requires comprehensive planning
  - Run: /research-prd $ARGUMENTS
  - Confidence from multi-agent analysis: [8-10]/10
  - Critical planning areas: [based on subagent findings]

MULTI-AGENT CONFIDENCE: [8-10]/10 in this complexity assessment
```

## Usage Examples

### Prototype Scope Example
```bash
# After filling out my-mcp-server.md with SCOPE: prototype:
/research-analyze my-mcp-server.md

# Output: "SCOPE: prototype detected, RECOMMENDED PATH: /research-initial-prototype"
# → Routes to prototype workflow for rapid concept validation
```

### Production Scope Example
```bash
# After filling out my-jwt-auth.md with SCOPE: production:
/research-analyze my-jwt-auth.md

# System deploys 3 subagents for parallel investigation:
# Technical: Analyzes JWT implementation complexity
# Architectural: Examines auth system integration impact  
# Integration: Investigates API, database, middleware dependencies

# Enhanced output with verified complexity assessment and production routing
```

## Key Enhancement Benefits

- **Scope-aware routing** between prototype and production workflows
- **Multiple expert perspectives** through independent subagent investigation (for production)
- **Efficient prototype routing** without unnecessary complexity analysis
- **Verification of complexity factors** rather than single-agent assumption (for production)
- **Higher confidence scores** through multi-agent validation
- **Clear workflow guidance** based on user's stated development approach

Ready to proceed with the scope-appropriate workflow path and specialist-validated analysis.
