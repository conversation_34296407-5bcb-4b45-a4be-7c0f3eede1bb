# Simple Workflow Step 2: Validate Enhanced Initial

Validate enhanced initial template: $ARGUMENTS

Use independent subagents to investigate specific validation questions and assess enhanced initial template readiness for single-pass PRP implementation success with persistent validation reporting.

## Prerequisites
- You must have a completed enhanced initial template (1-enhanced-initial-[feature].md) from `/simple-1-enhance-initial`
- Template should be the output from the simple workflow enhancement step
- This is step 2 in the simple workflow for feature development

## Output Generated
- **Validation Report**: `PRPs/simple-workflow/validation-report-[feature].md`
- **Quality Assessment**: Comprehensive validation scores and improvement recommendations
- **Iteration Tracking**: Appends to existing validation reports for improvement history
- **Readiness Confirmation**: Clear go/no-go decision for proceeding to PRP creation

## Multi-Agent Validation Process

### Phase 1: Enhanced Initial Analysis (Lead Agent)
**Objective**: Understand enhanced initial template structure and setup validation output

1. **Template Validation and Output Setup**
   ```bash
   # Validate input is enhanced initial template
   TEMPLATE_FILE="$ARGUMENTS"
   cat "$TEMPLATE_FILE"
   
   # Verify this is enhanced initial template
   if [[ "$TEMPLATE_FILE" == *"1-enhanced-initial"* ]]; then
       echo "✅ Enhanced INITIAL template detected - Simple workflow step 2"
       TEMPLATE_TYPE="enhanced_initial"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^1-enhanced-initial-//')
   elif grep -q "## FEATURE:" "$TEMPLATE_FILE"; then
       echo "✅ Legacy enhanced INITIAL template detected"
       TEMPLATE_TYPE="enhanced_initial"
       FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^enhanced-//')
   else
       echo "❌ ERROR: Expected enhanced initial template from simple-1-enhance-initial"
       echo "Expected: 1-enhanced-initial-[feature].md format"
       exit 1
   fi
   
   # Create output directory and set validation report path
   OUTPUT_DIR="PRPs/simple-workflow"
   mkdir -p "$OUTPUT_DIR"
   VALIDATION_REPORT="${OUTPUT_DIR}/validation-report-${FEATURE_NAME}.md"
   echo "🎯 Validation report will be saved to: $VALIDATION_REPORT"
   ```

2. **Validation History Check**
   - Check for existing validation reports
   - Prepare iteration tracking
   - Plan improvement assessment based on previous validations

### Phase 2: Parallel Subagent Validation

**Think hard about comprehensive validation assessment** - Deploy independent subagents to investigate specific validation questions:

**Implementation Readiness Investigator**
- Objective: Investigate whether this enhanced initial provides sufficient context for autonomous PRP creation and implementation success
- Task: "**Think hard about implementation readiness assessment**: Based on this enhanced initial template, investigate: Does it contain enough specific information for autonomous PRP creation and coding? Are the requirements clear and unambiguous? Can PRP creation and implementation proceed without additional clarification? Assess the clarity of specifications, adequacy of examples, and completeness of implementation guidance."
- Output format: Implementation readiness score (1-10) with specific assessment of what enables or prevents autonomous success
- Boundaries: Focus on implementation feasibility and clarity for simple workflow, not technical accuracy details

**Technical Accuracy Investigator** 
- Objective: Investigate the technical quality, accuracy, and relevance of examples, documentation, and specifications in the enhanced initial
- Task: "**Think carefully about technical accuracy validation**: Based on this enhanced initial template, investigate: Are the technical specifications accurate and relevant? Do the examples actually exist and apply to the requirements? Are the documentation links valid and useful? Assess technical correctness and relevance of all technical content for simple feature development."
- Tools: Use file verification, pattern checking, and technical assessment
- Output format: Technical accuracy score (1-10) with specific assessment of technical quality and relevance
- Boundaries: Focus on technical correctness and relevance, not implementation readiness

**Completeness & Gap Investigator**
- Objective: Investigate what critical information might be missing and identify gaps that could cause PRP creation or implementation failure
- Task: "**Think carefully about completeness gap identification**: Based on this enhanced initial template, investigate: What critical information is missing for simple workflow success? What gaps could cause PRP creation or implementation problems? Are there edge cases, gotchas, or requirements not covered? Identify specific areas where additional information is needed."
- Output format: Completeness score (1-10) with specific identification of gaps and missing critical information
- Boundaries: Focus on identifying missing information and gaps, not technical accuracy or implementation methods

**Simple Workflow Optimization Investigator**
- Objective: Investigate optimization opportunities for simple workflow success and single-pass implementation
- Task: "**Think carefully about simple workflow optimization**: Based on this enhanced initial template, investigate: How can this template be optimized for simple workflow success? What enhancements would improve single-pass implementation probability? Are there patterns or context that could be strengthened for autonomous development? Assess optimization opportunities specific to simple feature development."
- Output format: Optimization score (1-10) with specific recommendations for simple workflow enhancement
- Boundaries: Focus on simple workflow optimization and single-pass success, not complex system considerations

### Phase 3: Multi-Agent Synthesis and Scoring

**Enhanced Initial Template Validation** - Synthesize subagent findings:

**FEATURE Section Assessment**:
- Implementation Readiness + Technical Accuracy findings
- Completeness + Simple Workflow Optimization insights
- Single-pass PRP creation feasibility

**EXAMPLES Section Assessment**:
- Technical Accuracy + Completeness Gap findings
- Simple Workflow Optimization recommendations
- Example verification and relevance

**DOCUMENTATION Section Assessment**:
- Technical Accuracy + Gap identification
- Implementation Readiness + Optimization findings
- Documentation completeness and utility

**OTHER CONSIDERATIONS Assessment**:
- All subagent findings synthesis
- Simple workflow specific requirements
- Single-pass implementation enablers

**Comprehensive Scoring Matrix**:
- Implementation Readiness: [1-10] from specialist investigation
- Technical Accuracy: [1-10] from specialist investigation  
- Completeness: [1-10] from specialist investigation
- Simple Workflow Optimization: [1-10] from specialist investigation
- **Overall Score**: Weighted average with minimum threshold requirements

### Phase 4: Validation Results and Recommendations

**Multi-Agent Validation Report** - Persistent file output:
```markdown
# SIMPLE WORKFLOW VALIDATION REPORT: [Feature Name]

## Validation Summary
- **Template**: [1-enhanced-initial-[feature].md]
- **Validation Date**: [Current date and time]
- **Workflow**: Simple Feature Development
- **Validation Iteration**: [Number - track improvement across runs]

## Subagent Assessment Results

### Implementation Readiness: [X/10]
**Specialist Findings**: [Specific findings about autonomous PRP creation and implementation feasibility]
**Key Strengths**: [Areas where template excels in implementation guidance]
**Critical Gaps**: [Missing information that prevents autonomous success]

### Technical Accuracy: [X/10]
**Specialist Findings**: [Specific findings about technical quality and relevance]
**Verified Examples**: [Examples confirmed to exist and apply]
**Technical Issues**: [Accuracy problems or outdated information]

### Completeness: [X/10]
**Specialist Findings**: [Specific findings about missing information and gaps]
**Coverage Assessment**: [Areas well-covered vs gaps identified]
**Critical Missing Elements**: [Information needed for success]

### Simple Workflow Optimization: [X/10]
**Specialist Findings**: [Specific findings about simple workflow enhancement opportunities]
**Optimization Opportunities**: [Ways to improve single-pass success]
**Simple Workflow Alignment**: [How well template fits simple development approach]

## Overall Assessment
- **Overall Validation Score**: [X/10]
- **Primary Strengths**: [Areas where template excels based on all subagent findings]
- **Critical Improvement Areas**: [Key issues identified across all investigations]
- **Single-Pass Implementation Confidence**: [Probability of autonomous success]

## Readiness Decision

### [Score 8-10]: ✅ READY FOR PRP CREATION
Template validation passed - ready for autonomous PRP creation

**Next Step**: `/simple-3-create-prp [template-file]`
**Expected PRP Confidence**: [8-10/10]
**Single-Pass Success Probability**: [85-95%]

**Subagent Consensus**:
- Implementation feasibility: [High/Very High]
- Technical quality: [High/Very High]  
- Information completeness: [High/Very High]
- Simple workflow optimization: [High/Very High]

### [Score 6-7]: ⚠️ NEEDS TARGETED IMPROVEMENT
Template needs improvement before PRP creation

**Specific Improvement Actions**:
1. [High priority fix from Implementation Readiness findings]
2. [Medium priority enhancement from Technical Accuracy findings]
3. [Critical gap closure from Completeness findings]
4. [Optimization opportunity from Simple Workflow findings]

**Next Steps**:
1. Address improvement recommendations above
2. Re-run: `/simple-2-validate-initial [template-file]`
3. Once score ≥8, proceed with: `/simple-3-create-prp [template-file]`

### [Score 1-5]: ❌ REQUIRES SIGNIFICANT ENHANCEMENT
Template needs substantial improvement before PRP creation

**Critical Issues Identified**:
- **Implementation Blockers**: [Specific problems preventing autonomous success]
- **Technical Problems**: [Accuracy/relevance issues requiring resolution]
- **Completeness Gaps**: [Missing critical information]
- **Simple Workflow Misalignment**: [Template doesn't support simple development approach]

**Recommended Actions**:
1. Return to `/simple-1-enhance-initial` with additional research
2. Address all critical issues identified above
3. Re-run validation once improvements complete

## Validation History
[Track improvement across multiple validation runs]

---
**Simple Workflow Step 2 Complete**: Enhanced initial validation finished
**File Location**: [Validation report path]
**Ready for Step 3**: [Yes/No based on score]
```

## Quality Gates and Recommendations

### High-Quality Enhanced Initial Templates (Score 8-10)
```
✅ SIMPLE WORKFLOW VALIDATION PASSED

Template: [1-enhanced-initial-[feature].md]
Validation Score: [X/10]
Subagent Consensus: [High confidence in PRP creation readiness]

READY FOR PRP CREATION:
Run: /simple-3-create-prp [template-file]
Expected PRP confidence: [8-10/10]
Single-pass implementation probability: [85-95%]

SUBAGENT VALIDATION SUMMARY:
- Implementation feasibility: [High/Very High]
- Technical quality: [High/Very High]  
- Information completeness: [High/Very High]
- Simple workflow optimization: [High/Very High]
```

### Enhanced Initial Templates Needing Improvement (Score <8)
```
⚠️ SIMPLE WORKFLOW VALIDATION NEEDS IMPROVEMENT

Template: [1-enhanced-initial-[feature].md]  
Validation Score: [X/10]

SPECIFIC SUBAGENT FINDINGS:
├── Implementation Issues: [Problems preventing autonomous PRP creation]
├── Technical Problems: [Accuracy/relevance issues identified]
├── Completeness Gaps: [Missing critical information]
└── Simple Workflow Issues: [Template doesn't support simple development]

PRIORITIZED IMPROVEMENT ACTIONS:
1. [High priority fix based on Implementation Readiness findings]
2. [Medium priority enhancement based on Technical Accuracy findings]
3. [Critical gap closure based on Completeness findings]
4. [Optimization opportunity based on Simple Workflow findings]

NEXT STEPS:
1. Address improvement recommendations above
2. Re-run: /simple-2-validate-initial [template-file]
3. Once score ≥8, proceed with: /simple-3-create-prp [template-file]
```

## Integration with Simple Workflow

### Step Sequence Validation
- **Previous Step**: `/simple-1-enhance-initial` → Enhanced initial template created
- **Current Step**: `/simple-2-validate-initial` → Enhanced initial validated
- **Next Step**: `/simple-3-create-prp` → PRP created from validated enhanced initial
- **Final Step**: `/simple-4-implement-feature` → Feature implemented from PRP

### Simple Workflow Benefits
- **Single-pass optimization**: Template validated for autonomous PRP creation success
- **Quality gate enforcement**: Prevents low-quality templates from proceeding
- **Iterative improvement**: Validation history tracks template enhancement over time
- **Clear progression**: Pass/fail decision with specific next steps

## Key Multi-Agent Benefits

- **Independent validation perspectives** from four specialist angles
- **Comprehensive gap identification** through focused investigation
- **Simple workflow optimization** through specialist assessment
- **Higher confidence scoring** through cross-verification of quality
- **Specific improvement guidance** based on specialist findings
- **Persistent validation reporting** with iteration tracking
- **Single-pass implementation enablement** through quality assurance

Enhanced initial validation ensuring template readiness for successful PRP generation and autonomous implementation in simple workflow.