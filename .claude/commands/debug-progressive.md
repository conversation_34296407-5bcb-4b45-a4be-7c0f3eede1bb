# Progressive Debug Framework - PRP Edition with Multi-Agent Enhancement

Lightweight-to-comprehensive error analysis with progressive deepening based on findings, enhanced with multi-agent specialists and optional deep reasoning mode.

## Usage
```bash
# Default progressive analysis
/debug-progressive "paste your complete error message here"

# Manual override flags
/debug-progressive --deep-analysis "error message"      # Force full comprehensive analysis
/debug-progressive --specialists-only "error message"   # Force multi-agent but skip deep thinking
/debug-progressive --think-hard "error message"        # Skip specialists, just deep reasoning
/debug-progressive --fast "error message"              # Force Phase 1 only (speed mode)
```

## Flag Options
- `--deep-analysis`: Force comprehensive analysis (all phases + specialists + deep thinking)
- `--specialists-only`: Force multi-agent specialists but no deep reasoning
- `--think-hard`: Force deep reasoning mode in Phase 3
- `--fast`: Force Phase 1 only for maximum speed

---

## 🎛️ Flag Parsing & Setup

```bash
echo "=== PROGRESSIVE DEBUG FRAMEWORK ==="

# Initialize flags and parse arguments
DEEP_ANALYSIS=false
SPECIALISTS_ONLY=false
THINK_HARD=false
FAST_MODE=false
ERROR_MESSAGE="$ARGUMENTS"

# Parse flags
if [[ "$ARGUMENTS" == *"--deep-analysis"* ]]; then
    DEEP_ANALYSIS=true
    ERROR_MESSAGE=$(echo "$ARGUMENTS" | sed 's/--deep-analysis//' | xargs)
    echo "🤖 **DEEP ANALYSIS MODE ACTIVATED** - Full comprehensive debugging"
fi

if [[ "$ARGUMENTS" == *"--specialists-only"* ]]; then
    SPECIALISTS_ONLY=true
    ERROR_MESSAGE=$(echo "$ARGUMENTS" | sed 's/--specialists-only//' | xargs)
    echo "🎯 **SPECIALISTS MODE ACTIVATED** - Multi-agent analysis enabled"
fi

if [[ "$ARGUMENTS" == *"--think-hard"* ]]; then
    THINK_HARD=true
    ERROR_MESSAGE=$(echo "$ARGUMENTS" | sed 's/--think-hard//' | xargs)
    echo "🧠 **DEEP THINKING MODE ACTIVATED** - Enhanced reasoning enabled"
fi

if [[ "$ARGUMENTS" == *"--fast"* ]]; then
    FAST_MODE=true
    ERROR_MESSAGE=$(echo "$ARGUMENTS" | sed 's/--fast//' | xargs)
    echo "⚡ **FAST MODE ACTIVATED** - Phase 1 analysis only"
fi

echo "Error Input: $ERROR_MESSAGE"
echo ""
```

---

## 🎯 Phase 1: Lightweight Error Triage (Always Runs)

### Error Pattern Recognition & Quick Analysis
```bash
echo "=== PHASE 1: ERROR TRIAGE ==="

# Parse basic error information
ERROR_TYPE=$(echo "$ERROR_MESSAGE" | grep -o '[A-Za-z]*Error\|[A-Za-z]*Exception' | head -1)
ERROR_FILE=$(echo "$ERROR_MESSAGE" | grep -o 'File "[^"]*"' | sed 's/File "//; s/"//' | head -1)
ERROR_LINE=$(echo "$ERROR_MESSAGE" | grep -o 'line [0-9]*' | sed 's/line //' | head -1)

echo "🔍 **Quick Analysis:**"
echo "- Error Type: ${ERROR_TYPE:-Unknown}"
echo "- Primary File: ${ERROR_FILE:-Not specified}"
echo "- Line Number: ${ERROR_LINE:-Not specified}"
```

### Error Pattern Database
```bash
echo "📚 **Known Pattern Analysis:**"

case "$ERROR_TYPE" in
    "ImportError"|"ModuleNotFoundError")
        echo "- **Pattern:** Import/Dependency Issue"
        echo "- **Common Causes:** Missing package, wrong import path, circular imports"
        echo "- **Confidence:** HIGH (90%)"
        echo "- **Quick Fixes:** Check requirements.txt, verify package installation, check import paths"
        ANALYSIS_DEPTH="targeted"
        PHASE1_CONFIDENCE=90
        ;;
        
    "AttributeError")
        echo "- **Pattern:** Object/Attribute Access Issue"
        echo "- **Common Causes:** Typo in attribute name, None object, wrong object type"
        echo "- **Confidence:** MEDIUM (70%)"
        echo "- **Quick Fixes:** Check object type, verify attribute exists, check for None"
        ANALYSIS_DEPTH="targeted"
        PHASE1_CONFIDENCE=70
        ;;
        
    "TypeError")
        echo "- **Pattern:** Type Mismatch Issue"
        echo "- **Common Causes:** Wrong argument types, calling non-callable, string/int confusion"
        echo "- **Confidence:** MEDIUM (75%)"
        echo "- **Quick Fixes:** Check function arguments, verify types, add type conversion"
        ANALYSIS_DEPTH="targeted"
        PHASE1_CONFIDENCE=75
        ;;
        
    "KeyError"|"IndexError")
        echo "- **Pattern:** Data Access Issue"
        echo "- **Common Causes:** Missing dictionary key, list index out of range, empty data"
        echo "- **Confidence:** HIGH (85%)"
        echo "- **Quick Fixes:** Add key existence check, validate data length, add default values"
        ANALYSIS_DEPTH="light"
        PHASE1_CONFIDENCE=85
        ;;
        
    "ValueError")
        echo "- **Pattern:** Invalid Value Issue"
        echo "- **Common Causes:** Wrong value format, invalid conversion, out-of-range values"
        echo "- **Confidence:** MEDIUM (70%)"
        echo "- **Quick Fixes:** Validate input values, add range checks, improve data validation"
        ANALYSIS_DEPTH="light"
        PHASE1_CONFIDENCE=70
        ;;
        
    "FileNotFoundError"|"PermissionError")
        echo "- **Pattern:** File System Issue"
        echo "- **Common Causes:** Missing file, wrong path, insufficient permissions"
        echo "- **Confidence:** HIGH (90%)"
        echo "- **Quick Fixes:** Check file exists, verify path, check permissions"
        ANALYSIS_DEPTH="light"
        PHASE1_CONFIDENCE=90
        ;;
        
    "ValidationError")
        echo "- **Pattern:** Pydantic/Data Validation Issue"
        echo "- **Common Causes:** Invalid data format, missing required fields, type constraints"
        echo "- **Confidence:** HIGH (85%)"
        echo "- **Quick Fixes:** Check input data format, verify required fields, add validation"
        ANALYSIS_DEPTH="targeted"
        PHASE1_CONFIDENCE=85
        ;;
        
    *)
        echo "- **Pattern:** Unknown/Complex Error"
        echo "- **Common Causes:** Multiple factors, architectural issues, environment problems"
        echo "- **Confidence:** LOW (40%)"
        echo "- **Recommendation:** Proceed to targeted analysis"
        ANALYSIS_DEPTH="targeted"
        PHASE1_CONFIDENCE=40
        ;;
esac
```

### Fast Mode Exit Point
```bash
if [ "$FAST_MODE" = "true" ]; then
    echo ""
    echo "⚡ **FAST MODE - Phase 1 Only**"
    
    # Show context around error if file exists
    if [ -n "$ERROR_FILE" ] && [ -f "$ERROR_FILE" ] && [ -n "$ERROR_LINE" ]; then
        echo "📄 **Error Context:**"
        awk -v line="$ERROR_LINE" 'NR >= line-2 && NR <= line+2 {
            marker = (NR == line) ? ">>> " : "    "
            printf "%s%d: %s\n", marker, NR, $0
        }' "$ERROR_FILE"
        
        echo ""
        echo "🔧 **Quick Fix Suggestion:**"
        case "$ERROR_TYPE" in
            "KeyError"|"IndexError"|"ValueError"|"FileNotFoundError")
                echo "- Add appropriate safety checks and validation"
                echo "- Use defensive programming patterns"
                ;;
            *)
                echo "- Review error context and apply standard debugging approaches"
                echo "- Consider re-running without --fast for deeper analysis"
                ;;
        esac
    fi
    
    echo ""
    echo "✅ **Fast Mode Complete - For deeper analysis, remove --fast flag**"
    exit 0
fi
```

### Immediate Actions for Simple Cases
```bash
echo ""
echo "⚡ **Immediate Actions:**"

# Override for simple cases unless forced deeper analysis
if [ "$ANALYSIS_DEPTH" = "light" ] && [ "$DEEP_ANALYSIS" = "false" ] && [ "$SPECIALISTS_ONLY" = "false" ] && [ "$THINK_HARD" = "false" ]; then
    echo "✅ **Simple Fix Available - No Deep Analysis Needed**"
    
    # Show context around error if file exists
    if [ -n "$ERROR_FILE" ] && [ -f "$ERROR_FILE" ] && [ -n "$ERROR_LINE" ]; then
        echo ""
        echo "📄 **Error Context:**"
        awk -v line="$ERROR_LINE" 'NR >= line-2 && NR <= line+2 {
            marker = (NR == line) ? ">>> " : "    "
            printf "%s%d: %s\n", marker, NR, $0
        }' "$ERROR_FILE"
        
        echo ""
        echo "🔧 **Suggested Fix:**"
        case "$ERROR_TYPE" in
            "KeyError")
                key=$(echo "$ERROR_MESSAGE" | grep -o "'[^']*'" | head -1)
                echo "- Add key check: if $key in dict_name:"
                echo "- Or use dict.get($key, default_value)"
                ;;
            "IndexError")
                echo "- Add length check: if len(list_name) > index:"
                echo "- Or use try/except IndexError"
                ;;
            "ValueError")
                echo "- Add input validation before conversion"
                echo "- Use try/except ValueError for graceful handling"
                ;;
            "FileNotFoundError")
                echo "- Verify file path: os.path.exists('$ERROR_FILE')"
                echo "- Check working directory: os.getcwd()"
                ;;
        esac
        
        echo ""
        echo "🧪 **Quick Test:**"
        echo "python -c \"# Test your fix here\""
        echo ""
        echo "✅ **Phase 1 Complete - Simple fix identified**"
        echo "💡 **For deeper analysis, use: /debug-progressive --deep-analysis \"[error]\"\""
        exit 0
    fi
fi
```

---

## 🔍 Phase 2: Targeted Analysis with Multi-Agent Enhancement

### Decision Point for Phase 2
```bash
echo ""
echo "=== PHASE 2 DECISION POINT ==="
echo "Analysis Depth Needed: $ANALYSIS_DEPTH"
echo "Phase 1 Confidence: ${PHASE1_CONFIDENCE:-50}%"

# Skip Phase 2 unless needed or forced
if [ "$ANALYSIS_DEPTH" != "targeted" ] && [ "$DEEP_ANALYSIS" = "false" ] && [ "$SPECIALISTS_ONLY" = "false" ]; then
    echo "Skipping Phase 2 - Issue resolved in Phase 1"
    echo "💡 **For deeper analysis, use: /debug-progressive --deep-analysis \"[error]\"\""
    exit 0
fi

echo "🎯 **Proceeding to Targeted Analysis**"
echo ""
```

### Import/Dependency Focused Analysis
```bash
if [[ "$ERROR_TYPE" == *"Import"* ]] || [[ "$ERROR_TYPE" == *"Module"* ]]; then
    echo "=== IMPORT/DEPENDENCY ANALYSIS ==="
    
    # Extract module name from error
    MODULE_NAME=$(echo "$ERROR_MESSAGE" | grep -o "No module named '[^']*'" | sed "s/No module named '//; s/'//")
    
    echo "🔍 **Dependency Investigation:**"
    echo "- Missing Module: ${MODULE_NAME:-Unknown}"
    
    # Check if it's in requirements
    if [ -f "requirements.txt" ]; then
        if grep -q "$MODULE_NAME" requirements.txt; then
            echo "- ✅ Found in requirements.txt"
            echo "- 🔧 Fix: pip install -r requirements.txt"
        else
            echo "- ❌ Not in requirements.txt"
            echo "- 🔧 Fix: Add '$MODULE_NAME' to requirements.txt"
        fi
    fi
    
    # Check pyproject.toml for dependencies
    if [ -f "pyproject.toml" ]; then
        if grep -q "$MODULE_NAME" pyproject.toml; then
            echo "- ✅ Found in pyproject.toml"
            echo "- 🔧 Fix: pip install -e ."
        else
            echo "- ❌ Not in pyproject.toml dependencies"
            echo "- 🔧 Fix: Add to [project.dependencies] or [tool.uv.dependencies]"
        fi
    fi
    
    # Check for typos in imports
    if [ -n "$ERROR_FILE" ] && [ -f "$ERROR_FILE" ]; then
        echo "- 📄 Checking import statements in $ERROR_FILE:"
        grep -n "import\|from" "$ERROR_FILE" | head -5
    fi
    
    # Check for circular imports
    echo "- 🔄 Checking for circular import patterns:"
    if [ -n "$MODULE_NAME" ]; then
        grep -r "from $MODULE_NAME\|import $MODULE_NAME" . --include="*.py" | head -3
    fi
    
    CONFIDENCE_SCORE=85
fi
```

### Pydantic/Validation Error Analysis
```bash
if [[ "$ERROR_TYPE" == "ValidationError" ]] || echo "$ERROR_MESSAGE" | grep -q "pydantic"; then
    echo "=== PYDANTIC/VALIDATION ANALYSIS ==="
    
    echo "🔍 **Validation Investigation:**"
    
    # Extract field information from validation error
    FIELD_ERROR=$(echo "$ERROR_MESSAGE" | grep -o "Field '[^']*'" | head -1)
    echo "- Problem Field: ${FIELD_ERROR:-Check error details}"
    
    # Look for model definition
    if [ -n "$ERROR_FILE" ] && [ -f "$ERROR_FILE" ]; then
        echo "- 📄 Checking model definitions in $ERROR_FILE:"
        grep -n "class.*BaseModel\|Field\|validator" "$ERROR_FILE" | head -5
    fi
    
    # Common validation fixes
    echo "- 🔧 **Common Pydantic Fixes:**"
    echo "  1. Check required fields are provided"
    echo "  2. Verify data types match model definition"
    echo "  3. Add Optional[] for nullable fields"
    echo "  4. Use Field(default=...) for default values"
    echo "  5. Check custom validators"
    
    CONFIDENCE_SCORE=80
fi
```

### Attribute/Type Error Analysis
```bash
if [[ "$ERROR_TYPE" == "AttributeError" ]] || [[ "$ERROR_TYPE" == "TypeError" ]]; then
    echo "=== ATTRIBUTE/TYPE ANALYSIS ==="
    
    # Extract object and attribute from error
    OBJECT_ATTR=$(echo "$ERROR_MESSAGE" | grep -o "'[^']*' object has no attribute '[^']*'" | head -1)
    
    echo "🔍 **Object Analysis:**"
    echo "- Error Detail: $OBJECT_ATTR"
    
    if [ -n "$ERROR_FILE" ] && [ -f "$ERROR_FILE" ] && [ -n "$ERROR_LINE" ]; then
        echo "- 📄 Code Context:"
        awk -v line="$ERROR_LINE" 'NR >= line-1 && NR <= line+1 {
            marker = (NR == line) ? ">>> " : "    "
            printf "%s%d: %s\n", marker, NR, $0
        }' "$ERROR_FILE"
        
        # Look for variable assignments nearby
        echo "- 🔍 Variable assignments around line $ERROR_LINE:"
        awk -v line="$ERROR_LINE" 'NR >= line-5 && NR <= line && /=/ {
            printf "    %d: %s\n", NR, $0
        }' "$ERROR_FILE" | head -3
    fi
    
    CONFIDENCE_SCORE=75
fi
```

### Configuration Analysis
```bash
# Check if error might be config-related
if echo "$ERROR_MESSAGE" | grep -q -E "settings|config|env|KEY|TOKEN|LOGFIRE"; then
    echo "=== CONFIGURATION ANALYSIS ==="
    
    echo "🔍 **Configuration Investigation:**"
    
    # Look for environment variables
    echo "- 🌍 Environment variables mentioned:"
    echo "$ERROR_MESSAGE" | grep -o '[A-Z_][A-Z0-9_]*' | head -5
    
    # Check config files
    echo "- 📁 Configuration files found:"
    find . -maxdepth 2 -name "*.env*" -o -name "config.*" -o -name "settings.*" | head -5
    
    # Check for missing env vars
    if [ -f ".env.example" ] && [ ! -f ".env" ]; then
        echo "- ⚠️  .env.example exists but .env missing"
        echo "- 🔧 Fix: cp .env.example .env and configure values"
    fi
    
    # Check for Logfire configuration (project-specific)
    if echo "$ERROR_MESSAGE" | grep -q "LOGFIRE"; then
        echo "- 📊 Logfire-specific checks:"
        echo "  - Verify LOGFIRE_TOKEN is set"
        echo "  - Check logfire.configure() is called"
        echo "  - Ensure logfire package is installed"
    fi
    
    CONFIDENCE_SCORE=80
fi
```

### Multi-Agent Specialist Analysis (Enhanced Phase 2)
```bash
echo ""
echo "📊 **Phase 2 Results:**"
echo "- Confidence Score: ${CONFIDENCE_SCORE:-50}%"
echo "- Analysis Quality: $([ ${CONFIDENCE_SCORE:-50} -gt 70 ] && echo "GOOD" || echo "NEEDS_ENHANCEMENT")"

# Deploy specialists if confidence is low OR if forced by flags
if [ ${CONFIDENCE_SCORE:-50} -lt 60 ] || [ "$DEEP_ANALYSIS" = "true" ] || [ "$SPECIALISTS_ONLY" = "true" ]; then
    echo ""
    echo "🤖 **DEPLOYING MULTI-AGENT SPECIALISTS**"
    echo "Parallel specialist investigation for enhanced analysis..."
    echo ""
    
    echo "=== SPECIALIST AGENT DEPLOYMENT ==="
    
    echo "**Error Pattern Specialist Investigation:**"
    echo "Task: Deep pattern analysis of '$ERROR_TYPE' with context '$ERROR_MESSAGE'"
    echo "Focus Areas:"
    echo "- Sophisticated error patterns beyond basic classification"
    echo "- Edge cases and subtle variations of this error type"
    echo "- Known gotchas and advanced solutions for this pattern"
    echo "- Production vs development manifestation differences"
    echo ""
    echo "**Specialist Findings:**"
    
    # Error Pattern Specialist Logic
    case "$ERROR_TYPE" in
        *"Import"*|*"Module"*)
            echo "- **Advanced Pattern**: Circular import detection needed"
            echo "- **Edge Case**: Dynamic imports, __init__.py issues, namespace packages"
            echo "- **Production Issue**: Different Python paths, missing dependencies in deployment"
            echo "- **Confidence Enhancement**: +15% (sophisticated import analysis)"
            SPECIALIST_BONUS=15
            ;;
        "AttributeError"|"TypeError")
            echo "- **Advanced Pattern**: Dynamic attribute access, metaclass issues"
            echo "- **Edge Case**: Property descriptors, __getattr__ conflicts, inheritance problems"
            echo "- **Timing Issue**: Object lifecycle, initialization order problems"
            echo "- **Confidence Enhancement**: +20% (object model analysis)"
            SPECIALIST_BONUS=20
            ;;
        "ValidationError")
            echo "- **Advanced Pattern**: Nested model validation, custom validator conflicts"
            echo "- **Edge Case**: Serialization context, field validation order"
            echo "- **Data Issue**: Input format variations, encoding problems"
            echo "- **Confidence Enhancement**: +15% (validation model analysis)"
            SPECIALIST_BONUS=15
            ;;
        *)
            echo "- **Advanced Pattern**: Multi-factor error combinations detected"
            echo "- **Edge Case**: Timing, state, or environmental interactions"
            echo "- **Complex Issue**: Requires architectural analysis"
            echo "- **Confidence Enhancement**: +10% (general pattern analysis)"
            SPECIALIST_BONUS=10
            ;;
    esac
    
    echo ""
    echo "**Codebase Architecture Specialist Investigation:**"
    echo "Task: Analyze project structure and dependency relationships"
    echo "Focus Areas:"
    echo "- Architectural patterns contributing to this error"
    echo "- Dependency injection, inheritance, module coupling analysis"
    echo "- Hidden dependencies and architectural anti-patterns"
    echo ""
    
    # Architecture Analysis
    echo "**Architecture Findings:**"
    python -c "
import os
import ast
from collections import defaultdict

def analyze_architecture():
    file_imports = defaultdict(list)
    large_files = []
    
    for root, dirs, files in os.walk('.'):
        # Skip common ignore patterns
        if any(skip in root for skip in ['.git', '__pycache__', '.venv', 'node_modules']):
            continue
        if root.count(os.sep) > 3:  # Limit depth
            continue
            
        for file in files:
            if file.endswith('.py'):
                path = os.path.join(root, file)
                try:
                    with open(path, 'r') as f:
                        lines = f.readlines()
                        line_count = len(lines)
                        
                    if line_count > 200:
                        large_files.append((path, line_count))
                        
                    # Count imports
                    import_count = sum(1 for line in lines if 'import' in line)
                    if import_count > 10:
                        file_imports[path] = import_count
                        
                except Exception:
                    continue
    
    print('- **Large Files (>200 lines):**')
    for path, lines in sorted(large_files, key=lambda x: x[1], reverse=True)[:3]:
        print(f'  {path}: {lines} lines (potential complexity)')
    
    print('- **Import Heavy Files (>10 imports):**')
    for path, imports in sorted(file_imports.items(), key=lambda x: x[1], reverse=True)[:3]:
        print(f'  {path}: {imports} imports (potential coupling)')
    
    if large_files or file_imports:
        print('- **Confidence Enhancement**: +10% (architectural analysis)')
        return 10
    else:
        print('- **Architecture**: Clean structure detected')
        return 5

arch_bonus = analyze_architecture()
"
    
    echo ""
    echo "**Environment & Configuration Specialist Investigation:**"
    echo "Task: Deep environment and setup analysis"
    echo "Focus Areas:"
    echo "- Environment variables, package versions, configuration conflicts"
    echo "- Python version compatibility, virtual environment issues"
    echo "- Deployment differences, system dependencies"
    echo ""
    
    # Environment Analysis
    echo "**Environment Findings:**"
    if [ -f "pyproject.toml" ]; then
        echo "- **Project Structure**: Modern Python project with pyproject.toml"
        echo "- **Dependency Management**: $(grep -q 'uv' pyproject.toml && echo 'uv-based' || echo 'pip-based')"
    fi
    
    if [ -f ".python-version" ]; then
        python_version=$(cat .python-version)
        echo "- **Python Version**: $python_version (pyenv managed)"
    fi
    
    # Check virtual environment
    if [ -n "$VIRTUAL_ENV" ]; then
        echo "- **Virtual Environment**: Active ($VIRTUAL_ENV)"
        echo "- **Confidence Enhancement**: +5% (environment analysis)"
        ENV_BONUS=5
    else
        echo "- **Virtual Environment**: None detected (potential issue)"
        echo "- **Confidence Penalty**: -5% (environment risk)"
        ENV_BONUS=-5
    fi
    
    # Calculate enhanced confidence
    ENHANCED_CONFIDENCE=$((${CONFIDENCE_SCORE:-50} + ${SPECIALIST_BONUS:-0} + ${ENV_BONUS:-0}))
    
    echo ""
    echo "🎯 **MULTI-AGENT SYNTHESIS:**"
    echo "- **Original Confidence**: ${CONFIDENCE_SCORE:-50}%"
    echo "- **Specialist Enhancement**: +${SPECIALIST_BONUS:-0}%"
    echo "- **Environment Analysis**: ${ENV_BONUS:-0}%"
    echo "- **Enhanced Confidence**: ${ENHANCED_CONFIDENCE}%"
    echo "- **Analysis Quality**: $([ $ENHANCED_CONFIDENCE -gt 80 ] && echo "EXCELLENT" || [ $ENHANCED_CONFIDENCE -gt 60 ] && echo "GOOD" || echo "REQUIRES_DEEP_ANALYSIS")"
    
    CONFIDENCE_SCORE=$ENHANCED_CONFIDENCE
fi

# Update decision logic with enhanced confidence
if [ ${CONFIDENCE_SCORE:-50} -gt 70 ]; then
    echo "✅ **Enhanced analysis sufficient - proceeding to fix planning**"
    SKIP_PHASE_3=true
else
    echo "⚠️  **Enhanced confidence still low - Phase 3 deep analysis recommended**"
    SKIP_PHASE_3=false
fi
```

---

## 🧠 Phase 3: Deep Analysis with Enhanced Reasoning

```bash
echo ""
echo "=== PHASE 3 DECISION POINT ==="

# Force Phase 3 if think-hard or deep-analysis flags are set
if [ "$SKIP_PHASE_3" = "true" ] && [ "$DEEP_ANALYSIS" = "false" ] && [ "$THINK_HARD" = "false" ]; then
    echo "Skipping Phase 3 - Enhanced analysis was sufficient"
    echo "💡 **For deep reasoning, use: /debug-progressive --think-hard \"[error]\"\""
else
    echo "🧠 **PROCEEDING TO DEEP ANALYSIS WITH ENHANCED REASONING**"
    echo ""
    
    if [ "$DEEP_ANALYSIS" = "true" ] || [ "$THINK_HARD" = "true" ]; then
        echo "🤖 **ENHANCED REASONING MODE ACTIVATED**"
        echo "Deploying advanced analytical capabilities for complex error investigation..."
        echo ""
    fi
    
    echo "=== COMPREHENSIVE CODEBASE ANALYSIS ==="
    
    # Enhanced deep analysis
    echo "🗺️ **Advanced Dependency Mapping:**"
    python -c "
import ast
import os
from collections import defaultdict, deque

def deep_dependency_analysis():
    imports_graph = defaultdict(set)
    circular_imports = []
    module_complexity = {}
    
    print('Analyzing codebase architecture...')
    
    for root, dirs, files in os.walk('.'):
        # Skip common ignore patterns
        if any(skip in root for skip in ['.git', '__pycache__', '.venv', 'node_modules']):
            continue
        if root.count(os.sep) > 4:  # Increased depth for deep analysis
            continue
            
        for file in files:
            if file.endswith('.py'):
                path = os.path.join(root, file)
                try:
                    with open(path, 'r') as f:
                        content = f.read()
                        
                    # Parse AST for sophisticated analysis
                    tree = ast.parse(content)
                    imports = []
                    functions = 0
                    classes = 0
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                imports.append(alias.name)
                        elif isinstance(node, ast.ImportFrom) and node.module:
                            imports.append(node.module)
                        elif isinstance(node, ast.FunctionDef):
                            functions += 1
                        elif isinstance(node, ast.ClassDef):
                            classes += 1
                    
                    module_name = path.replace('.py', '').replace('/', '.').replace('\\\\', '.')
                    imports_graph[module_name] = set(imports)
                    module_complexity[module_name] = {
                        'imports': len(imports),
                        'functions': functions,
                        'classes': classes,
                        'lines': len(content.split('\\n'))
                    }
                    
                except Exception as e:
                    continue
    
    # Find potential circular dependencies
    for module, deps in imports_graph.items():
        for dep in deps:
            if dep in imports_graph and module in imports_graph[dep]:
                circular_imports.append((module, dep))
    
    # Report findings
    print('- **Module Complexity Analysis:**')
    complex_modules = sorted(module_complexity.items(), 
                           key=lambda x: x[1]['imports'] + x[1]['functions'] + x[1]['classes'], 
                           reverse=True)[:3]
    
    for module, stats in complex_modules:
        print(f'  {module}: {stats[\"imports\"]} imports, {stats[\"functions\"]} functions, {stats[\"classes\"]} classes')
    
    if circular_imports:
        print('- **Circular Dependencies Detected:**')
        for mod1, mod2 in circular_imports[:3]:
            print(f'  {mod1} ↔ {mod2}')
        print('  ^ This could cause import issues!')
    else:
        print('- **Circular Dependencies**: None detected')
    
    return len(circular_imports) > 0

has_circular = deep_dependency_analysis()
"
    
    echo ""
    echo "🧠 **DEEP REASONING ANALYSIS:**"
    echo "Think hard about this complex debugging scenario:"
    echo ""
    echo "**System-Level Interaction Analysis:**"
    echo "- **Error Context**: $ERROR_TYPE in complex system architecture"
    echo "- **Timing Considerations**: Could this be related to execution order or race conditions?"
    echo "- **State Dependencies**: Are there hidden state requirements or initialization sequences?"
    echo "- **Framework Interactions**: How do different parts of the system interact during this error?"
    echo ""
    
    echo "**Advanced Hypothesis Generation:**"
    case "$ERROR_TYPE" in
        *"Import"*|*"Module"*)
            echo "- **Hypothesis 1**: Circular import created by recent code changes"
            echo "- **Hypothesis 2**: Python path or PYTHONPATH issues in current environment"
            echo "- **Hypothesis 3**: Package installation incomplete or virtual environment mismatch"
            echo "- **Investigation**: Check import order, sys.path, and dependency installation timing"
            ;;
        "AttributeError"|"TypeError")
            echo "- **Hypothesis 1**: Object lifecycle issue - attribute accessed before initialization"
            echo "- **Hypothesis 2**: Inheritance or mixin conflict causing attribute masking"
            echo "- **Hypothesis 3**: Dynamic attribute creation/deletion timing problem"
            echo "- **Investigation**: Check object construction order, inheritance chain, and dynamic attributes"
            ;;
        "ValidationError")
            echo "- **Hypothesis 1**: Data serialization context affecting validation rules"
            echo "- **Hypothesis 2**: Nested model validation order causing constraint conflicts"
            echo "- **Hypothesis 3**: Input data encoding or format variation not handled"
            echo "- **Investigation**: Check validation context, input data source, and model inheritance"
            ;;
        *)
            echo "- **Hypothesis 1**: Multi-factor interaction between unrelated system components"
            echo "- **Hypothesis 2**: Environmental factor (permissions, resources, external services)"
            echo "- **Hypothesis 3**: Hidden dependency or side effect from recent changes"
            echo "- **Investigation**: Check system resources, external dependencies, and recent modifications"
            ;;
    esac
    
    echo ""
    echo "**Environmental Subtleties Analysis:**"
    echo "- **Python Environment**: $(python --version 2>&1) in $([ -n "$VIRTUAL_ENV" ] && echo "virtual env" || echo "system Python")"
    echo "- **Working Directory**: $(pwd)"
    echo "- **Recent Changes**: $(git log --oneline -3 2>/dev/null | head -3 || echo "No git history available")"
    echo "- **System Resources**: Check available memory, disk space, network connectivity"
    echo ""
    
    # Enhanced confidence from deep analysis
    DEEP_ANALYSIS_BONUS=15
    FINAL_CONFIDENCE=$((${CONFIDENCE_SCORE:-50} + $DEEP_ANALYSIS_BONUS))
    
    echo "🎯 **DEEP ANALYSIS RESULTS:**"
    echo "- **Enhanced Reasoning Bonus**: +${DEEP_ANALYSIS_BONUS}%"
    echo "- **Final Confidence**: ${FINAL_CONFIDENCE}%"
    echo "- **Analysis Depth**: Comprehensive (All 3 phases completed)"
    
    CONFIDENCE_SCORE=$FINAL_CONFIDENCE
fi
```

---

## 🔧 Enhanced Fix Planning & Implementation

```bash
echo ""
echo "=== ENHANCED FIX PLANNING PHASE ==="
echo "📋 **Comprehensive Implementation Plan:**"

# Generate specific fix based on enhanced analysis
echo "### Immediate Actions (Priority 1):"

case "$ERROR_TYPE" in
    *"Import"*|*"Module"*)
        echo "1. **Install missing dependency:**"
        echo "   \`pip install ${MODULE_NAME:-[module_name]}\`"
        echo "   Or if using uv: \`uv add ${MODULE_NAME:-[module_name]}\`"
        echo "2. **Add to dependencies:**"
        if [ -f "pyproject.toml" ]; then
            echo "   Add to pyproject.toml [project.dependencies]"
        else
            echo "   \`echo '${MODULE_NAME:-[module_name]}' >> requirements.txt\`"
        fi
        echo "3. **Verify import path and check for circular imports:**"
        echo "   \`python -c \"import ${MODULE_NAME:-[module_name]}; print('Import successful')\"\`"
        
        # Enhanced recommendations from specialist analysis
        if [ ${CONFIDENCE_SCORE:-50} -gt 80 ]; then
            echo ""
            echo "### Enhanced Recommendations (From Specialist Analysis):"
            echo "- Check for circular import patterns in affected modules"
            echo "- Verify virtual environment activation: \`which python\`"
            echo "- Consider import order dependencies in __init__.py files"
        fi
        ;;
        
    "ValidationError")
        echo "1. **Debug input data format:**"
        echo "   \`print(f'Input data: {input_data}')\` before model creation"
        echo "2. **Update model definition:**"
        echo "   Add Field(default=...) or Optional[] as needed"
        echo "3. **Add comprehensive validation debugging:**"
        echo "   Use model.model_validate() with detailed error catching"
        
        # Enhanced recommendations
        if [ ${CONFIDENCE_SCORE:-50} -gt 80 ]; then
            echo ""
            echo "### Enhanced Recommendations (From Specialist Analysis):"
            echo "- Check nested model validation order and dependencies"
            echo "- Verify input data encoding and format consistency"
            echo "- Consider validation context and custom validator conflicts"
        fi
        ;;
        
    "AttributeError"|"TypeError")
        echo "1. **Add comprehensive type and None checking:**"
        echo "   \`if obj is not None and hasattr(obj, 'attribute_name'):\`"
        echo "2. **Debug object state and lifecycle:**"
        echo "   \`print(f'Object type: {type(obj)}, dir: {dir(obj)}')\`"
        echo "3. **Verify object initialization sequence:**"
        echo "   Check constructor calls and initialization order"
        
        # Enhanced recommendations
        if [ ${CONFIDENCE_SCORE:-50} -gt 80 ]; then
            echo ""
            echo "### Enhanced Recommendations (From Specialist Analysis):"
            echo "- Investigate object lifecycle and initialization timing"
            echo "- Check for inheritance conflicts or mixin attribute masking"
            echo "- Consider dynamic attribute creation/deletion patterns"
        fi
        ;;
        
    *)
        echo "1. **Implement comprehensive error handling:**"
        echo "   Add try/except blocks with detailed logging"
        echo "2. **Add defensive programming patterns:**"
        echo "   Validate inputs, check preconditions, handle edge cases"
        echo "3. **Enhance debugging capabilities:**"
        echo "   Add structured logging with correlation IDs"
        
        # Enhanced recommendations
        if [ ${CONFIDENCE_SCORE:-50} -gt 80 ]; then
            echo ""
            echo "### Enhanced Recommendations (From Deep Analysis):"
            echo "- Consider multi-factor interactions between system components"
            echo "- Check environmental factors and external dependencies"
            echo "- Investigate timing, state, or resource-related issues"
        fi
        ;;
esac

echo ""
echo "### Advanced Validation Strategy:"
echo "1. **Comprehensive Testing:**"
echo "   \`python -c \"# Test specific error scenario with various inputs\"\`"
echo "2. **Integration Testing:**"
echo "   \`python -m pytest tests/ -v -k \"$(echo $ERROR_TYPE | tr '[:upper:]' '[:lower:]')\"\`"
echo "3. **Regression Testing:**"
echo "   \`python -m py_compile \$(find . -name '*.py' | head -20)\`"
echo "4. **Environment Verification:**"
echo "   \`pip list | grep $(echo ${MODULE_NAME:-main} | head -c 10)\`"

echo ""
echo "### Prevention & Monitoring (Priority 2):"
echo "- **Error Handling**: Add specific exception handling for this error pattern"
echo "- **Unit Testing**: Create test case reproducing this exact scenario"
echo "- **Logging Enhancement**: Add structured logging at key decision points"
echo "- **Documentation**: Update code comments explaining the fix rationale"
echo "- **Monitoring**: Consider adding health checks if this is a recurring pattern"

# Enhanced prevention based on analysis depth
if [ ${CONFIDENCE_SCORE:-50} -gt 80 ]; then
    echo ""
    echo "### Long-term Improvements (From Enhanced Analysis):"
    echo "- **Architecture**: Consider refactoring if circular dependencies detected"
    echo "- **Code Quality**: Add type hints and static analysis (mypy, pylint)"
    echo "- **Testing Strategy**: Implement property-based testing for data validation"
    echo "- **CI/CD Enhancement**: Add dependency validation and import checking"
fi
```

## 📊 Comprehensive Summary & Next Steps

```bash
echo ""
echo "=== COMPREHENSIVE DEBUG SUMMARY ==="
echo "🎯 **Enhanced Error Analysis Complete**"
echo "- **Error Type:** $ERROR_TYPE"
echo "- **Analysis Mode:** $([ "$DEEP_ANALYSIS" = "true" ] && echo "Deep Analysis" || [ "$SPECIALISTS_ONLY" = "true" ] && echo "Specialists" || [ "$THINK_HARD" = "true" ] && echo "Deep Thinking" || [ "$FAST_MODE" = "true" ] && echo "Fast Mode" || echo "Progressive")"
echo "- **Final Confidence Level:** ${CONFIDENCE_SCORE:-50}%"
echo "- **Analysis Phases Completed:** $([ "$FAST_MODE" = "true" ] && echo "Phase 1 only" || [ "$ANALYSIS_DEPTH" = "light" ] && echo "Phase 1 only" || [ "$SKIP_PHASE_3" = "true" ] && echo "Phase 1 + 2" || echo "All 3 phases")"
echo "- **Specialist Enhancement:** $([ ${SPECIALIST_BONUS:-0} -gt 0 ] && echo "Applied (+${SPECIALIST_BONUS:-0}%)" || echo "Not needed")"
echo "- **Fix Complexity:** $([ ${CONFIDENCE_SCORE:-50} -gt 80 ] && echo "Well-understood" || [ ${CONFIDENCE_SCORE:-50} -gt 60 ] && echo "Moderate complexity" || echo "Complex/Unknown")"

echo ""
echo "🚀 **Prioritized Next Steps:**"
echo "1. **Implement Priority 1 fixes** (immediate actions above)"
echo "2. **Execute validation strategy** (comprehensive testing)"
echo "3. **Verify no regressions** (integration testing)"
echo "4. **Apply prevention measures** (error handling, testing, monitoring)"

echo ""
echo "💡 **Advanced Usage Options:**"
echo "- **Force deeper analysis**: \`/debug-progressive --deep-analysis \"[error]\"\`"
echo "- **Get specialist insights**: \`/debug-progressive --specialists-only \"[error]\"\`"
echo "- **Enable deep reasoning**: \`/debug-progressive --think-hard \"[error]\"\`"
echo "- **Speed mode for simple errors**: \`/debug-progressive --fast \"[error]\"\`"

echo ""
echo "🔄 **Fallback Strategy:**"
echo "If the suggested fixes don't resolve the issue:"
echo "1. **Escalate analysis**: Use \`--deep-analysis\` flag for maximum thoroughness"
echo "2. **Environment debugging**: Check Python version, virtual env, and system setup"
echo "3. **Historical analysis**: \`git log --oneline -10\` and \`git bisect\` if needed"
echo "4. **Community resources**: Search for this specific error pattern online"
echo "5. **Expert consultation**: Share the comprehensive analysis results with your team"

echo ""
echo "📈 **Success Criteria Checklist:**"
echo "- [ ] **Primary Issue Resolved**: Original error no longer occurs"
echo "- [ ] **No Regressions**: Existing functionality preserved"
echo "- [ ] **Testing Coverage**: Error scenario covered by tests"
echo "- [ ] **Prevention Measures**: Error handling and monitoring in place"
echo "- [ ] **Documentation Updated**: Fix rationale and learnings documented"
echo "- [ ] **Knowledge Sharing**: Results shared with team for collective learning"

echo ""
echo "🎯 **Analysis Quality Assessment:**"
if [ ${CONFIDENCE_SCORE:-50} -gt 90 ]; then
    echo "**EXCELLENT** - High confidence in analysis and fix recommendations"
elif [ ${CONFIDENCE_SCORE:-50} -gt 75 ]; then
    echo "**GOOD** - Solid analysis with reliable fix recommendations"
elif [ ${CONFIDENCE_SCORE:-50} -gt 60 ]; then
    echo "**MODERATE** - Analysis complete but consider additional validation"
else
    echo "**NEEDS_ATTENTION** - Complex issue requiring careful implementation and testing"
fi
```

---

## 🎛️ Enhanced Progressive Framework Features

### **Multi-Tier Analysis Options**
- **Fast Mode** (`--fast`): Phase 1 only for maximum speed
- **Progressive Mode** (default): Intelligent escalation based on confidence
- **Specialist Mode** (`--specialists-only`): Force multi-agent analysis
- **Deep Analysis** (`--deep-analysis`): Full comprehensive analysis
- **Think Hard** (`--think-hard`): Enhanced reasoning mode

### **Multi-Agent Specialist System**
- **Error Pattern Specialist**: Advanced pattern recognition and edge case analysis
- **Codebase Architecture Specialist**: Dependency analysis and architectural insights
- **Environment & Configuration Specialist**: Setup and deployment analysis
- **Parallel Investigation**: Independent analysis with confidence synthesis

### **Enhanced Reasoning Capabilities**
- **Deep Thinking Mode**: Advanced hypothesis generation and system-level analysis
- **Environmental Analysis**: Subtle factor investigation and context awareness
- **Temporal Considerations**: Timing, state, and lifecycle issue detection
- **Novel Hypothesis Generation**: Beyond standard debugging approaches

### **Adaptive Intelligence**
- **Confidence-Driven Escalation**: Automatic depth adjustment based on analysis quality
- **Manual Override Controls**: Force any analysis level when needed
- **Context Window Optimization**: Efficient analysis scaling with smart depth management
- **Learning Integration**: Pattern recognition improvement through usage

### **Project-Specific Intelligence**
- **Tech Stack Awareness**: Pydantic, Logfire, uv, MCP framework recognition
- **Dependency Management**: Both requirements.txt and pyproject.toml support
- **Environment Detection**: Virtual environment, Python version, and setup analysis
- **Framework-Specific Patterns**: Validation errors, async issues, configuration problems

## Usage Guidelines

### **Choose Your Analysis Depth:**
- **Simple/Known errors**: Use default progressive mode
- **Production mysteries**: Use `--deep-analysis` for comprehensive investigation
- **Learning mode**: Use `--specialists-only` to understand error patterns
- **Time pressure**: Use `--fast` for immediate basic guidance
- **Complex issues**: Use `--think-hard` for enhanced reasoning

### **Interpretation Tips:**
1. **Confidence >80%**: High reliability, implement suggested fixes
2. **Confidence 60-80%**: Good analysis, validate before implementing
3. **Confidence <60%**: Consider manual investigation or team consultation
4. **Specialist Enhancement**: Indicates complex patterns detected
5. **Deep Analysis Results**: Comprehensive investigation completed

This enhanced progressive framework provides the full spectrum from lightning-fast basic debugging to comprehensive multi-agent analysis with deep reasoning capabilities, all controlled through simple flags.
