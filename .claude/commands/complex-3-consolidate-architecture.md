# Complex Workflow Step 3: Consolidate System Architecture

Extract and synthesize architectural research findings from validated PRD templates to prepare consolidated data for modular development planning.

## Development Standards Integration
**CRITICAL**: All architectural analysis and modular decomposition must align with the comprehensive coding standards defined in:
**File Reference**: `PRPs/ai_docs/development_standards.md`

**Application Instructions**: Read and apply ALL standards from the referenced file when analyzing complexity and planning modular boundaries. Key alignment areas include:
- Module complexity scoring based on file and function length limits (500/50 lines)
- Architectural patterns supporting AI agent module-global reuse
- Configuration patterns enabling factory pattern compliance
- Quality frameworks supporting type safety and testing requirements
- Code organization standards for maintainable project structure

**Validation Requirement**: Each specialist investigator must ensure their architectural recommendations support the complete standards framework.

## Technology-Agnostic Design Philosophy

**This command is designed to work with ANY technology stack and project type.** The architectural analysis patterns are generic and adapt to whatever technologies, frameworks, and approaches are actually specified in your research template:

- **Web Applications**: React, Vue, Django, Rails, FastAPI, etc.
- **Mobile Applications**: React Native, Flutter, Swift, Kotlin, etc.
- **Desktop Applications**: Electron, Qt, .NET, etc.
- **Data Processing**: Pandas, Spark, ETL pipelines, etc.
- **AI/ML Systems**: TensorFlow, PyTorch, Scikit-learn, etc.
- **APIs and Services**: REST, GraphQL, gRPC, microservices, etc.
- **Databases**: SQL, NoSQL, Graph, Time-series, etc.
- **Integration Patterns**: Message queues, event streams, file processing, etc.

**The investigators analyze YOUR specific project requirements and technology choices, not pre-assumed technologies.**

## Prerequisites
- You must have a completed and validated PRD template (2-product-requirements-[feature].md) from complex-2-create-prd
- Template should have been created from validated enhanced initial from `/complex-1-validate-initial`
- This is the fourth step in the complex workflow for multi-component systems
- Original INITIAL.md should be available for feature context

## Usage
```bash
/complex-3-consolidate-architecture [validated-prd-file]
```

Examples:
```bash
/complex-3-consolidate-architecture PRPs/project-planning/web-dashboard/analysis/2-product-requirements-web-dashboard.md
# → PRPs/project-planning/web-dashboard/analysis/3-system-architecture-web-dashboard.md

/complex-3-consolidate-architecture PRPs/project-planning/data-pipeline/analysis/2-product-requirements-data-pipeline.md
# → PRPs/project-planning/data-pipeline/analysis/3-system-architecture-data-pipeline.md

/complex-3-consolidate-architecture PRPs/project-planning/mobile-app/analysis/2-product-requirements-mobile-app.md
# → PRPs/project-planning/mobile-app/analysis/3-system-architecture-mobile-app.md
```

## Multi-Agent Architectural Extraction Process

### Phase 1: Research Template Analysis (Lead Agent)
**Objective**: Understand template type, structure, and complexity for architectural investigation planning

1. **Template Type Detection**
   ```bash
   # Detect template type and read content
   TEMPLATE_FILE="$ARGUMENTS"
   
   if [[ ! -f "$TEMPLATE_FILE" ]]; then
       echo "❌ ERROR: Template file '$TEMPLATE_FILE' not found"
       echo "Expected: 1-product-requirements-[feature].md from complex-2-validate-prd"
       exit 1
   fi
   
   # Determine template type
   if grep -q "## Problem Statement\|## Executive Summary" "$TEMPLATE_FILE"; then
       echo "📋 PRD template detected: $TEMPLATE_FILE"  
       TEMPLATE_TYPE="prd"
   else
       echo "❓ Unknown template type in: $TEMPLATE_FILE"
       echo "Expected 2-product-requirements-[feature].md format from complex workflow"
       exit 1
   fi
   
   # Extract feature name and setup analysis directory structure
   FEATURE_NAME=$(basename "$TEMPLATE_FILE" .md | sed 's/^2-product-requirements-//')
   
   # Create PRPs/project-planning/[feature]/analysis/ directory structure
   ANALYSIS_DIR="PRPs/project-planning/${FEATURE_NAME}/analysis"
   mkdir -p "$ANALYSIS_DIR"
   
   OUTPUT_FILE="${ANALYSIS_DIR}/3-system-architecture-${FEATURE_NAME}.md"
   echo "🎯 Will generate: $OUTPUT_FILE"
   echo "📁 Analysis directory: $ANALYSIS_DIR"
   ```

2. **Initial Complexity Assessment**
   - Analyze overall system scope and architectural complexity
   - Identify potential major components and integration points
   - Plan specialist investigation areas based on template content

### Phase 2: Parallel Architectural Investigation

**Ultrathink this comprehensive architectural decomposition** - Deploy independent specialist subagents to extract architectural insights:

**System Architecture Investigator**
- Objective: Investigate and identify system components, relationships, and integration patterns regardless of technology stack
- Task: "**Think hard about the system architecture**: Based on this research template, investigate the system architecture: What are the major system components (services, modules, classes, functions, data stores, external integrations)? How do they interact? What are the primary data flows and communication patterns? Map the overall system architecture including all integration points with external systems."
- Focus: Component identification, relationship mapping, data flow analysis, integration architecture
- Output format: System component breakdown with relationships and complexity scoring
- Boundaries: Focus on architectural structure and component interactions, technology-agnostic analysis

**Infrastructure Dependencies Investigator** 
- Objective: Investigate all infrastructure, deployment, and environmental dependencies required for the system
- Task: "**Think hard about infrastructure requirements**: Based on this research template, investigate infrastructure requirements: What databases, message queues, external services, or infrastructure components need to be deployed? What local development environment setup is required? What external services need local equivalents for development? What deployment, hosting, monitoring, or operational infrastructure is needed?"
- Focus: Infrastructure identification, deployment requirements, development environment setup, operational dependencies
- Output format: Infrastructure component breakdown with deployment requirements and local development setup
- Boundaries: Focus on infrastructure and deployment needs, not business logic or application code

**Technology Integration Investigator** 
- Objective: Investigate the specific technology requirements, dependencies, and implementation patterns for THIS project
- Task: "**Think hard about technology integration**: Based on this research template, investigate the actual technology stack being used: What are the specific frameworks, libraries, databases, APIs, and tools? How do they integrate? What are the technology-specific implementation considerations, setup requirements, and known gotchas for these particular technologies?"
- Focus: Project-specific technology integration patterns, compatibility requirements, implementation considerations
- Output format: Technology stack analysis with integration requirements and implementation guidance
- Boundaries: Focus on the actual technologies mentioned in the research, not hypothetical or generic technology advice

**Complexity Distribution Investigator**
- Objective: Investigate complexity distribution across system components for modular boundary identification
- Task: "**Think hard about complexity distribution and modular boundaries**: Based on this research template, investigate complexity distribution: Which parts of the system are most complex? What are the simple foundational elements? How should the system be broken down for incremental development? Identify optimal module boundaries and build order strategy."
- Focus: Complexity assessment, modular boundary identification, dependency analysis, build sequencing
- Output format: Complexity scoring per component with module boundary recommendations
- Boundaries: Focus on complexity analysis and modular decomposition, not technical implementation approaches

**Codebase Integration Investigator**
- Objective: Investigate integration with existing codebase patterns, standards, and architectural requirements
- Task: "**Think carefully about codebase integration patterns**: Based on this research template and existing codebase patterns referenced, investigate: How should this system integrate with existing code? What patterns should be followed? What are the specific file structures, coding standards, and integration requirements to maintain consistency?"
- Focus: Existing pattern compliance, integration with current codebase, organizational standards
- Output format: Pattern application strategy with integration specifications and compliance requirements
- Boundaries: Focus on codebase integration and standards compliance, not new architectural design

### Phase 3: Architectural Synthesis and Module Planning

**Ultrathink this critical architectural synthesis** - **Cross-Specialist Integration Analysis**:
Synthesize findings from all five investigators to create comprehensive architectural understanding:

1. **Component Architecture Synthesis**
   - Integrate component identification from System Architecture Investigator
   - Include infrastructure components from Infrastructure Dependencies Investigator
   - Apply technology constraints from Technology Integration Investigator  
   - Consider complexity factors from Complexity Distribution Investigator
   - Ensure pattern compliance from Codebase Integration Investigator

2. **Modular Boundary Determination**
   ```
   FOR each major_component IN architecture_analysis:
       complexity_score = get_complexity_score(component)
       dependencies = get_dependency_analysis(component)
       technology_requirements = get_tech_requirements(component)
       infrastructure_needs = get_infrastructure_requirements(component)
       
       IF complexity_score <= 6 AND dependencies.external_only:
           → Foundation Module (Phase 1)
       ELIF complexity_score <= 7 AND dependencies.internal_count <= 2:
           → Core Module (Phase 2)
       ELSE:
           → Integration Module (Phase 3) OR further_decomposition_needed
   
   GOAL: Each module complexity_score <= 6/10 for reliable single-PRP success
   ```

3. **Build Strategy Development**
   - Phase 1: Foundation modules (external dependencies only, parallel development)
   - Phase 2: Core logic modules (depend on foundations, sequential after Phase 1)
   - Phase 3: Integration modules (depend on multiple cores, sequential after Phase 2)

### Phase 4: Consolidated Research Report Generation

**Multi-Agent Architectural Report**:
```markdown
ARCHITECTURAL CONSOLIDATION REPORT: [Feature Name]

SPECIALIST INVESTIGATIONS SYNTHESIS:
├── System Architecture: [Component breakdown and relationships]
├── Infrastructure Dependencies: [Deployment and environment requirements]
├── Technology Integration: [Tech stack requirements and dependencies]
├── Complexity Distribution: [Module boundary recommendations]
└── Codebase Integration: [Pattern compliance and standards]

MODULAR DECOMPOSITION:
- Foundation Modules: [X modules, Y complexity average, external deps only]
- Core Logic Modules: [X modules, Y complexity average, internal deps]
- Integration Modules: [X modules, Y complexity average, multi-deps]

BUILD STRATEGY:
- Phase 1 Duration: [X days] - Foundation setup
- Phase 2 Duration: [Y days] - Core logic implementation
- Phase 3 Duration: [Z days] - Integration and system assembly

ARCHITECTURAL CONFIDENCE:
[Score 8-10]: ✅ READY FOR MODULAR DEVELOPMENT
[Score 6-7]: ⚠️ NEEDS ARCHITECTURAL REFINEMENT
[Score 1-5]: ❌ REQUIRES SIGNIFICANT REDESIGN
```

## Output File Structure: `PRPs/project-planning/[feature]/analysis/3-system-architecture-[feature].md`

### File Organization Benefits
- **Clean Research Phase Separation**: All research documents organized in `analysis/` folder
- **Project-Specific Directories**: Each feature gets dedicated directory structure
- **Execution Phase Preparation**: Parent directory ready for project plan and module specs
- **Team Collaboration**: Clear handoff from research team to implementation team

### Complete Research Consolidation Template

```markdown
# Consolidated Research: [Feature Name]

## Research Synthesis Summary
- **Template Source**: [1-product-requirements-[feature].md]
- **Original Requirements**: [Reference to INITIAL.md FEATURE section]
- **Research Validation Score**: [Score from /research-validate if available]
- **Template Type**: [Enhanced Initial / PRD]
- **System Complexity**: [Overall complexity assessment from specialists]
- **Component Count**: [Number of major components identified]
- **Infrastructure Count**: [Number of infrastructure dependencies identified]
- **Modular Decomposition Confidence**: [X/10]

## System Architecture Analysis

### Major System Components (System Architecture Investigator)
**Component Identification Results**:

#### Component 1: [Component Name]
- **Purpose**: [Single responsibility description]
- **Technology Stack**: [Primary technologies required]
- **Complexity Score**: [1-10/10]
- **Dependencies**: [External/Internal dependency analysis]
- **Data Interfaces**: [Input/output specifications]
- **Integration Points**: [Connections with other components]

#### Component 2: [Component Name]
- **Purpose**: [Single responsibility description]
- **Technology Stack**: [Primary technologies required]
- **Complexity Score**: [1-10/10]
- **Dependencies**: [External/Internal dependency analysis]
- **Data Interfaces**: [Input/output specifications]
- **Integration Points**: [Connections with other components]

[Continue for all major components...]

### Component Relationship Architecture
**Data Flow Patterns** (System Architecture Analysis):
```
[Component A] --[data_type]--> [Component B]
[Component B] --[api_call]--> [Component C]
[Component C] --[event]--> [Component A]
```

**Communication Interfaces**:
- **Internal APIs**: [Between system components - specifications]
- **External APIs**: [Third-party integrations - requirements]
- **Database Interfaces**: [Data persistence patterns and access methods]
- **Event/Message Patterns**: [Async communication approaches]

### Integration Architecture Analysis
**External Integration Points** (System Architecture Investigation):
- **[External System 1]**: [Integration method, API requirements, data flow]
- **[External System 2]**: [Integration approach, protocol specifications, communication patterns]
- **[Third-party Services]**: [Service communication protocols, authentication requirements]
- **[Data Sources]**: [Database/file/API integrations, access patterns]

**Cross-System Communication Patterns**:
- **[Protocol/API Type]**: [Specifications, compliance requirements, implementation approach]
- **Data Access Patterns**: [Query approaches, transaction handling, performance considerations]
- **File/Storage Integration**: [Data persistence strategies, organization requirements]
- **Event/Message Patterns**: [Async communication, event handling, state coordination]

## Infrastructure Dependencies Analysis

### Infrastructure Components (Infrastructure Dependencies Investigator)
**Infrastructure Identification Results**:

#### Infrastructure 1: [Infrastructure Component Name]
- **Purpose**: [Infrastructure responsibility - database, message queue, etc.]
- **Technology**: [Specific technology - PostgreSQL, Redis, RabbitMQ, etc.]
- **Deployment Requirements**: [Local development setup needs]
- **Complexity Score**: [1-10/10]
- **Dependencies**: [External dependencies only for infrastructure]
- **Configuration**: [Setup and configuration requirements]
- **Local Development**: [How to run locally for development]

#### Infrastructure 2: [Infrastructure Component Name]
- **Purpose**: [Infrastructure responsibility]
- **Technology**: [Specific technology]
- **Deployment Requirements**: [Setup and hosting needs]
- **Complexity Score**: [1-10/10]
- **Dependencies**: [External dependencies]
- **Configuration**: [Configuration specifications]
- **Local Development**: [Local development approach]

[Continue for all infrastructure components...]

### Development Environment Setup (Infrastructure Analysis)
**Local Development Requirements**:
- **Development Tools**: [Required tools and frameworks for local development]
- **Database Setup**: [Local database installation and configuration]
- **Service Dependencies**: [External services that need local equivalents]
- **Environment Configuration**: [Environment variables and configuration setup]
- **Testing Infrastructure**: [Testing database, mock services, etc.]

**Deployment Infrastructure**:
- **Production Hosting**: [Hosting requirements and deployment strategy]
- **Database Hosting**: [Database deployment and hosting needs]
- **Monitoring Setup**: [Monitoring and observability infrastructure]
- **Security Infrastructure**: [Authentication, authorization, security services]

## Technology Stack Integration Analysis

### Core Technology Requirements (Technology Integration Investigator)
**Primary Technology Integration** (Project-Specific):
- **[Primary Framework/Library]**:
  - **Implementation Patterns**: [Specific patterns identified from research]
  - **Configuration Requirements**: [Setup needs and configuration specifications]
  - **Integration Considerations**: [Framework-specific implementation requirements]
  - **Performance Characteristics**: [Known performance patterns and optimization approaches]

- **[Database/Storage Technology]** (if applicable):
  - **Data Access Patterns**: [Query optimization, indexing strategies]
  - **Schema/Structure Design**: [Data modeling requirements and relationships]
  - **Transaction Management**: [Consistency and reliability patterns]
  - **Performance Considerations**: [Scaling and optimization approaches]

- **[Integration Technology]** (if applicable):
  - **Protocol Specifications**: [Required interfaces and communication patterns]
  - **Compliance Requirements**: [Standard adherence and validation requirements]
  - **Integration Interfaces**: [Connection patterns with external systems]
  - **Error Handling**: [Protocol-level error management strategies]

### Technology Integration Dependencies (Technology Integration Analysis)
**Integration Complexity Matrix** (Based on Actual Project Technologies):
- **[Tech A] + [Tech B]**: [Integration patterns, data flow, performance considerations]
- **[External APIs] + [Internal Components]**: [Authentication, rate limiting, error handling]
- **[Data Layer] + [Business Logic]**: [Access patterns, transaction handling, caching strategies]
- **[Async Components]** (if applicable): [Event handling, state coordination, reliability patterns]

**Technology-Specific Implementation Guidance** (Project-Specific):
- **Performance Considerations**: [Bottlenecks, optimization strategies, scaling approaches for chosen tech]
- **Security Requirements**: [Authentication, authorization, data protection strategies for stack]
- **Error Handling Patterns**: [Technology-specific error management and recovery approaches]
- **Development Environment**: [Setup requirements, testing strategies, debugging approaches for chosen tools]

**Known Technology Gotchas** (From Research Analysis):
- **[Technology 1] Gotchas**: [Common pitfalls and mitigation strategies specific to chosen tech]
- **[Technology 2] Integration Issues**: [Known problems and solutions for selected stack]
- **[Cross-Technology] Compatibility**: [Version requirements, dependency conflicts for actual dependencies]

## Modular Decomposition Analysis

### Complexity Distribution Assessment (Complexity Distribution Investigator)
**Component Complexity Breakdown**:
- **High Complexity Components** (8-10/10): [Components requiring further decomposition]
  - [Component Name]: [Complexity reasons, decomposition recommendations]
  - [Component Name]: [Complexity factors, simplification strategies]

- **Medium Complexity Components** (5-7/10): [Potential single modules or 2-module splits]
  - [Component Name]: [Complexity assessment, module boundary recommendations]
  - [Component Name]: [Scope considerations, dependency analysis]

- **Low Complexity Components** (1-4/10): [Foundation module candidates]
  - [Component Name]: [Foundation suitability, external dependency confirmation]
  - [Component Name]: [Simple implementation scope, build priority]

- **Infrastructure Components** (Varies): [Infrastructure setup modules]
  - [Infrastructure Name]: [Infrastructure setup complexity, deployment approach]
  - [Infrastructure Name]: [Configuration complexity, local development setup]

### Recommended Module Structure

#### Foundation Modules (Phase 1 - Parallel Development)
**Criteria**: External dependencies only, complexity ≤ 6/10, no internal component dependencies

**Module F1: [Foundation Module Name]**
- **Purpose**: [Single responsibility focus]
- **Scope**: [Specific deliverables and boundaries]
- **Dependencies**: External only ([list external dependencies])
- **Complexity Score**: [X/10]
- **Technology Focus**: [Primary tech stack]
- **Estimated Development Time**: [1-3 days]
- **Success Criteria**: [Measurable completion criteria]
- **Integration Points**: [How other modules will use this foundation]

**Module F2: [Infrastructure Module Name]**
- **Purpose**: [Infrastructure setup and configuration]
- **Scope**: [Database setup, environment configuration, local development]
- **Dependencies**: External only ([Docker, database software, etc.])
- **Complexity Score**: [X/10]
- **Technology Focus**: [Infrastructure technologies]
- **Estimated Development Time**: [1-3 days]
- **Success Criteria**: [Infrastructure running locally and in production]
- **Integration Points**: [How application modules connect to infrastructure]

[Continue for all Foundation modules...]

#### Core Logic Modules (Phase 2 - Sequential after Phase 1)
**Criteria**: Depends on foundation modules, complexity ≤ 7/10, core business logic focus

**Module C1: [Core Module Name]**
- **Purpose**: [Core business logic responsibility]
- **Scope**: [Business logic deliverables and boundaries]
- **Dependencies**: [Foundation modules required: F1, F2, etc.]
- **Complexity Score**: [X/10]
- **Technology Focus**: [Primary tech stack and integrations]
- **Estimated Development Time**: [2-4 days]
- **Success Criteria**: [Business logic validation criteria]
- **Interface Specifications**: [APIs/contracts with other modules]
- **Testing Requirements**: [Business logic validation approaches]

[Continue for all Core modules...]

#### Integration Modules (Phase 3 - Sequential after Phase 2)
**Criteria**: Depends on multiple core modules, handles system integration, orchestration focus

**Module I1: [Integration Module Name]**
- **Purpose**: [System integration and orchestration responsibility]
- **Scope**: [Integration deliverables and system assembly]
- **Dependencies**: [Core modules required: C1, C2, etc.]
- **Complexity Score**: [X/10]
- **Technology Focus**: [Integration technologies and orchestration]
- **Estimated Development Time**: [2-5 days]
- **Success Criteria**: [System integration validation criteria]
- **Integration Testing**: [End-to-end system validation approaches]
- **Performance Requirements**: [System-level performance benchmarks]

[Continue for all Integration modules...]

## Build Strategy & Implementation Planning

### Recommended Development Phases
**Phase 1: Foundation Setup** (Parallel Development Possible)
- **Modules**: [F1, F2, F3, etc.]
- **Total Duration**: [X days]
- **Risk Level**: Low (external dependencies only)
- **Parallel Development**: ✅ All foundation modules can be built simultaneously
- **Validation Checkpoint**: Foundation modules integrate successfully
- **Success Criteria**: [All external integrations working, module interfaces defined]

**Phase 2: Core Logic Implementation** (Sequential after Phase 1)
- **Modules**: [C1, C2, C3, etc.]
- **Total Duration**: [Y days]
- **Risk Level**: Medium (internal integration complexity)
- **Sequential Requirements**: Foundation modules must be complete
- **Validation Checkpoint**: Core business logic functioning correctly
- **Success Criteria**: [Business logic validated, module communication established]

**Phase 3: System Integration & Assembly** (Sequential after Phase 2)
- **Modules**: [I1, I2, etc.]
- **Total Duration**: [Z days]
- **Risk Level**: High (full system integration complexity)
- **Sequential Requirements**: Core modules must be complete and stable
- **Validation Checkpoint**: Full system functionality validated
- **Success Criteria**: [End-to-end system working, performance targets met]

### Critical Path Analysis
**Blocking Dependencies**:
- **Phase 1 → Phase 2**: [Foundation Module X] must complete before [Core Module Y]
- **Phase 2 → Phase 3**: [Core Module A] and [Core Module B] must complete before [Integration Module Z]
- **Cross-Module Dependencies**: [Specific module interdependencies that affect build order]
- **Infrastructure Dependencies**: [Infrastructure modules that must complete before dependent modules]

**Parallel Development Opportunities**:
- **Foundation Phase**: All F1, F2, F3... modules can be built simultaneously
- **Core Phase**: [Core Module A] and [Core Module C] can be built in parallel (no shared dependencies)
- **Integration Phase**: [Integration Module 1] can start once [specific core modules] complete

**Integration Risk Points**:
- **Foundation → Core Integration**: [Risk of interface mismatches, validation approach]
- **Core → Integration Assembly**: [Risk of system-level integration failures, testing strategy]
- **External System Integration**: [Risk of third-party API changes, fallback strategies]
- **Infrastructure Integration**: [Risk of deployment configuration issues, environment setup problems]

## Codebase Integration Specifications

### Existing Pattern Compliance (Codebase Integration Investigator)
**Validated Existing Patterns** (From Research Template Analysis):
- **Pattern 1**: [File path] → [Pattern description] → [Application to specific modules]
  - **Foundation Modules**: [How F1, F2 should follow this pattern]
  - **Core Modules**: [How C1, C2 should adapt this pattern]
  - **Integration Modules**: [How I1 should implement this pattern]

- **Pattern 2**: [File path] → [Pattern description] → [Application to specific modules]
  - **Shared Across Modules**: [Common implementation approach]
  - **Module-Specific Adaptations**: [Variations needed per module type]

**Pattern Application Strategy per Module**:
- **Shared Patterns**: [Patterns all modules must follow for consistency]
- **Foundation-Specific Patterns**: [Patterns specific to infrastructure modules]
- **Core Logic Patterns**: [Patterns specific to business logic modules]
- **Integration Patterns**: [Patterns specific to system integration modules]

### Code Organization Requirements
**File Structure Standards** (From Codebase Analysis - Project-Specific):
```
[project_root]/
├── [foundation_dir]/        # Foundation modules (Phase 1)
│   ├── [module_f1]/        # Foundation module directory structure
│   │   ├── [entry_point]   # Module interface (main.py, __init__.py, index.js, etc.)
│   │   ├── [core_impl]     # Main implementation files
│   │   └── [test_dir]/     # Module-specific tests
│   └── [module_f2]/        # Additional foundation modules
├── [core_dir]/             # Core logic modules (Phase 2)
│   ├── [module_c1]/        # Core module directory structure
│   │   ├── [entry_point]   # Module interface
│   │   ├── [business_impl] # Business logic implementation
│   │   ├── [interfaces]    # Module boundary definitions (if applicable)
│   │   └── [test_dir]/     # Module-specific tests
├── [integration_dir]/      # Integration modules (Phase 3)
│   ├── [module_i1]/        # Integration module structure
│   │   ├── [entry_point]   # Module interface
│   │   ├── [orchestrator]  # System integration logic
│   │   └── [test_dir]/     # Integration tests
├── [infrastructure_dir]/   # Infrastructure setup (Phase 1)
│   ├── [database_setup]/   # Database configuration and initialization
│   ├── [deployment]/       # Deployment configuration files
│   └── [monitoring]/       # Monitoring and observability setup
└── [shared_dir]/           # Shared utilities and interfaces
    ├── [interfaces]        # Cross-module interface definitions
    ├── [utilities]         # Common utilities
    └── [config]            # Shared configuration
```

**Naming Conventions** (From Existing Codebase Patterns):
- **Module Names**: [Naming pattern requirements from existing project]
- **File Names**: [File naming standards to follow from codebase]
- **Class/Component Names**: [Naming conventions from existing patterns]
- **Function/Method Names**: [Function naming patterns from project]
- **Variable Names**: [Variable naming standards from codebase]

**Documentation Standards** (From Codebase Integration Analysis):
- **Module Documentation**: [Required module-level documentation]
- **Interface Documentation**: [API/interface documentation requirements]
- **Implementation Documentation**: [Code comment and docstring standards]
- **Infrastructure Documentation**: [Setup and deployment documentation requirements]

### Testing Integration Strategy
**Unit Testing Framework** (From Existing Patterns):
- **Testing Framework**: [Actual framework used - pytest, jest, junit, etc. - from codebase analysis]
- **Test Organization**: [Test file structure and naming conventions from existing project]
- **Coverage Requirements**: [Minimum test coverage expectations from project standards]
- **Module-Specific Testing**: [Testing approaches per module type from existing patterns]

**Integration Testing Approach**:
- **Module Boundary Testing**: [How to test interfaces between modules]
- **Phase Integration Testing**: [How to validate phase completion]
- **System Integration Testing**: [End-to-end system validation]
- **Infrastructure Testing**: [How to test database setup, deployment configuration]

**Testing Phase Strategy**:
- **Phase 1 Testing**: Foundation module unit tests + external integration tests + infrastructure setup tests
- **Phase 2 Testing**: Core module unit tests + foundation integration tests
- **Phase 3 Testing**: Integration module tests + full system validation

## Quality Assurance Framework

### Module-Level Quality Gates
**Code Quality Standards** (From Codebase Integration Analysis):
- **Linting Standards**: [Code style requirements from project - eslint, flake8, rubocop, etc.]
- **Type Checking**: [Type checking tools if used - mypy, typescript, flow, etc.]
- **Documentation Requirements**: [Docstring and comment standards from existing code]
- **Import/Dependency Organization**: [Import sorting and organization standards from project]

**Performance Benchmarks per Module Type**:
- **Foundation Modules**: [Performance expectations for infrastructure]
- **Core Logic Modules**: [Business logic performance requirements]
- **Integration Modules**: [System integration performance targets]
- **Infrastructure Modules**: [Database and infrastructure performance requirements]

**Security Validation Requirements**:
- **Input Validation**: [Data validation and sanitization requirements]
- **Authentication/Authorization**: [Security implementation standards]
- **Data Protection**: [Sensitive data handling requirements]
- **Infrastructure Security**: [Database security, deployment security requirements]

### Integration Quality Gates
**Interface Compliance Validation**:
- **Module Boundary Validation**: [How to verify module interfaces work correctly]
- **Data Flow Validation**: [How to test cross-module communication]
- **API Contract Testing**: [How to validate interface agreements]
- **Infrastructure Interface Testing**: [How to test database connections, external service integrations]

**System Coherence Validation**:
- **End-to-End Functionality**: [System-level behavior validation]
- **Performance Under Load**: [System performance testing approaches]
- **Error Handling Consistency**: [System-wide error management validation]
- **Infrastructure Reliability**: [Database availability, backup/restore testing]

**Cross-Module Integration Testing**:
- **Phase 1 → Phase 2 Integration**: [Foundation to core integration validation]
- **Phase 2 → Phase 3 Integration**: [Core to integration module validation]
- **Full System Validation**: [Complete system functionality testing]
- **Infrastructure Integration**: [Database integration, deployment pipeline testing]

## Documentation & Resource Allocation

### Module-Specific Documentation Requirements
**Foundation Module Documentation**:
- **API Documentation**: [Interface and usage documentation requirements]
- **Configuration Documentation**: [Setup and configuration guides]
- **Integration Guide**: [How other modules should use foundation components]
- **Infrastructure Documentation**: [Database setup, deployment guides]

**Core Logic Module Documentation**:
- **Business Logic Documentation**: [Algorithm and logic explanation requirements]
- **Interface Documentation**: [Input/output specifications and examples]
- **Error Handling Documentation**: [Error conditions and recovery approaches]

**Integration Module Documentation**:
- **System Architecture Documentation**: [Overall system design and flow]
- **Deployment Documentation**: [System deployment and configuration guides]
- **Troubleshooting Documentation**: [Common issues and resolution approaches]

### Learning & Reference Resources (From Research Template)
**Technology-Specific Resources** (Validated by Research Phase):
- **[Technology 1] Resources**: [Specific URLs and documentation sections]
- **[Technology 2] Integration Guides**: [Cross-technology implementation resources]
- **[Framework] Best Practices**: [Implementation pattern guides and examples]

**Pattern Reference Resources** (From Codebase Integration Analysis):
- **Existing Codebase Examples**: [Specific files and patterns to follow]
- **Architecture Pattern Guides**: [Design pattern implementation approaches]
- **Integration Pattern Examples**: [Cross-module communication examples]

**Infrastructure Resources**:
- **Database Documentation**: [Setup guides, schema design patterns]
- **Deployment Guides**: [Infrastructure deployment and management resources]
- **Monitoring Resources**: [Observability and monitoring setup guides]

## Research Quality Certification

### Multi-Agent Validation Summary
- **System Architecture Investigation**: ✓ Components identified and relationships mapped
- **Infrastructure Dependencies Investigation**: ✓ Infrastructure requirements identified and deployment planned
- **Technology Integration Investigation**: ✓ Integration requirements and dependencies analyzed
- **Complexity Distribution Investigation**: ✓ Module boundaries identified and complexity assessed
- **Codebase Integration Investigation**: ✓ Existing patterns analyzed and compliance requirements defined

### Architectural Decomposition Quality Assessment
- **Module Complexity Scores**: [All modules ≤ 6/10 complexity - target achieved]
- **Infrastructure Complexity**: [Infrastructure setup complexity assessed and manageable]
- **Dependency Analysis**: [Clean dependency chains with no circular dependencies]
- **Build Strategy Validation**: [Realistic phase timeline with parallel development opportunities]
- **Integration Strategy**: [Clear module boundaries with well-defined interfaces]

### Readiness for Modular Development
- **Research Consolidation Complete**: ✓ All specialist findings synthesized
- **Architecture Decomposed**: ✓ System broken into manageable modules
- **Infrastructure Planned**: ✓ Infrastructure dependencies identified and deployment strategy defined
- **Build Strategy Defined**: ✓ Development phases and dependencies clarified
- **Quality Framework Established**: ✓ Testing and validation approaches defined
- **Codebase Integration Planned**: ✓ Existing pattern compliance ensured

---
**Consolidation Status**: ✅ Complete
**Modular Decomposition Confidence**: [X/10]
**Infrastructure Planning Confidence**: [X/10]
**Ready for Project Planning**: ✅ Architecture analyzed, infrastructure planned, modular boundaries established
**Next Step**: `/complex-4-validate-architecture PRPs/project-planning/[feature]/analysis/3-system-architecture-[feature].md`
```

## Integration Commands and Validation

### Research Consolidation Validation
```bash
# Verify consolidation completeness
grep -E "(TODO|TBD|FIXME|\[X\])" "$OUTPUT_FILE"
if [ $? -eq 0 ]; then
    echo "⚠️ WARNING: Incomplete consolidation detected - contains placeholders"
else
    echo "✅ Consolidation complete - no placeholders found"
fi

# Validate module complexity scores
python -c "
import re
with open('$OUTPUT_FILE', 'r') as f:
    content = f.read()
scores = re.findall(r'Complexity Score.*?(\d+)/10', content)
high_complexity = [s for s in scores if int(s) > 6]
if high_complexity:
    print(f'⚠️ WARNING: {len(high_complexity)} modules exceed complexity threshold (>6/10)')
    print(f'High complexity scores: {high_complexity}')
else:
    print('✅ All modules within complexity threshold (≤6/10)')
"

# Check for infrastructure dependencies
echo "🏗️ Checking infrastructure dependencies..."
python validate_infrastructure.py "$OUTPUT_FILE"

# Check for circular dependencies
echo "🔍 Checking for circular dependencies..."
python validate_dependencies.py "$OUTPUT_FILE"
```

### Quality Assurance Commands
```bash
# Generate modular development confidence score
echo "📊 Calculating modular development confidence..."
python calculate_modular_confidence.py "$OUTPUT_FILE"

# Validate specialist investigation synthesis
echo "🔬 Validating specialist research integration..."
python validate_specialist_synthesis.py "$OUTPUT_FILE"

# Check build strategy feasibility
echo "🏗️ Analyzing build strategy feasibility..."
python analyze_build_strategy.py "$OUTPUT_FILE"

# Validate infrastructure completeness
echo "🛠️ Validating infrastructure planning..."
python validate_infrastructure_planning.py "$OUTPUT_FILE"
```

## Success Criteria for Research Consolidation

### Immediate Success Criteria
- [ ] **Template Analysis Complete**: Research template successfully parsed and understood
- [ ] **Five Specialist Investigations Complete**: All architectural investigators completed analysis
- [ ] **Component Architecture Mapped**: System components identified with relationships
- [ ] **Infrastructure Dependencies Identified**: All infrastructure requirements mapped with deployment strategy
- [ ] **Technology Integration Analyzed**: Tech stack dependencies and patterns identified
- [ ] **Modular Boundaries Established**: System decomposed into manageable modules
- [ ] **Build Strategy Defined**: Development phases with realistic timelines
- [ ] **Codebase Integration Planned**: Existing patterns identified and compliance planned

### Quality Gates for Progression
- [ ] **All Modules Complexity ≤ 6/10**: Each module within manageable complexity range
- [ ] **Infrastructure Complexity Manageable**: Infrastructure setup within reasonable scope
- [ ] **No Circular Dependencies**: Clean dependency chains established
- [ ] **Clear Module Interfaces**: Well-defined boundaries between modules
- [ ] **Realistic Build Timeline**: Development phases with achievable timelines
- [ ] **Pattern Compliance Ensured**: Integration with existing codebase validated
- [ ] **Quality Framework Defined**: Testing and validation approaches established
- [ ] **Infrastructure Framework Complete**: Infrastructure deployment and local development strategy defined

### Readiness Assessment
**Consolidation Score 8-10/10**: ✅ READY FOR PROJECT PLANNING
- Research synthesis comprehensive and actionable
- Infrastructure dependencies identified and planned
- Modular decomposition achievable and well-structured
- Build strategy realistic with clear dependencies
- Quality assurance framework comprehensive

**Consolidation Score 6-7/10**: ⚠️ NEEDS REFINEMENT
- Some architectural gaps or unclear module boundaries
- Infrastructure planning incomplete or unclear
- Build strategy needs timeline or dependency refinement
- Additional specialist investigation recommended

**Consolidation Score 1-5/10**: ❌ REQUIRES ADDITIONAL RESEARCH
- Significant architectural gaps or overly complex modules
- Infrastructure requirements unclear or missing
- Build strategy unrealistic or dependencies unclear
- Return to research phase for additional investigation

## Anti-Patterns to Avoid

- ❌ **Monolithic Module Creation**: Modules with complexity >7/10 or multiple responsibilities
- ❌ **Missing Infrastructure Planning**: Forgetting database, deployment, or operational requirements
- ❌ **Circular Dependencies**: Modules that depend on each other creating build order conflicts
- ❌ **Vague Module Boundaries**: Unclear interfaces or overlapping responsibilities
- ❌ **Unrealistic Build Timelines**: Overly optimistic estimates or ignoring integration complexity
- ❌ **Pattern Inconsistency**: Ignoring existing codebase patterns and standards
- ❌ **Missing Integration Planning**: Lack of cross-module communication strategy
- ❌ **Infrastructure Assumptions**: Assuming infrastructure "just works" without explicit setup planning

## Enhanced Success Indicators

- ✅ **Comprehensive Specialist Synthesis**: All five architectural investigators provided actionable insights
- ✅ **Complete Infrastructure Planning**: All infrastructure dependencies identified with deployment strategy
- ✅ **Realistic Modular Decomposition**: System broken into manageable, focused modules
- ✅ **Clear Build Strategy**: Development phases with realistic timelines and dependencies
- ✅ **Quality Integration Planning**: Testing and validation approaches defined
- ✅ **Codebase Compliance Ensured**: Integration with existing patterns validated
- ✅ **Architecture Confidence High**: Modular development confidence ≥8/10
- ✅ **Infrastructure Confidence High**: Infrastructure planning confidence ≥8/10

The research consolidation creates the foundation for successful modular development by transforming research findings into actionable architectural blueprints with complete infrastructure planning.
