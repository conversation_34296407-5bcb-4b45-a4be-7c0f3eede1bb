# Analyze and Distribute Project Context (Universal PRP Framework)

Intelligently analyze CLAUDE.md ecosystem and implement size-aware context distribution that maintains template reusability while preserving all project knowledge through optimal organization.

## Universal Distribution Philosophy

**Size-Driven Mode Selection**: Automatically detect whether root CLAUDE.md needs reduction (>300 lines) or maintenance (<300 lines) and apply appropriate strategy.

**Information Architecture Principles**: Organize content by scope and specificity - universal patterns stay in root, specific implementations distribute to modules, detailed examples move to reference documentation.

**Template Preservation**: Maintain root CLAUDE.md as a reusable template foundation while ensuring all project-specific knowledge remains accessible through logical distribution.

## Execution Modes (Auto-Detected)

### Mode 1: Reduction Mode (Root >300 lines)
**Primary Focus**: Analyze oversized root and implement content distribution to achieve template restoration.

**Trigger Conditions**:
- Root CLAUDE.md exceeds 300 lines
- Token count indicates context inefficiency
- Template foundation obscured by project specifics

**Execution Priority**:
1. **Root Content Analysis** (30 min): Categorize every section by scope and specificity
2. **Content Migration** (25 min): Move distributable content to appropriate locations
3. **Template Restoration** (15 min): Restructure root to clean template state
4. **Validation** (10 min): Verify size reduction and content accessibility

### Mode 2: Maintenance Mode (Root <300 lines)
**Primary Focus**: Handle incremental growth and maintain existing distribution.

**Trigger Conditions**:
- Root CLAUDE.md under 300 lines
- Existing distribution structure detected
- Template state preserved

**Execution Priority**:
1. **Growth Detection** (20 min): Identify new content since last distribution
2. **Incremental Distribution** (15 min): Handle new growth appropriately
3. **Consistency Validation** (10 min): Ensure distributed files remain aligned

### Mode 3: Fresh Distribution (No existing structure)
**Primary Focus**: Create initial distribution from grown root file.

**Trigger Conditions**:
- No existing module CLAUDE.md files detected
- Root contains project-specific content
- Fresh project organization needed

## Universal Content Categorization Rules

### Template Foundation Content (STAYS in root)
**Criteria**: Universal patterns applicable to any project using this template.

**Categories**:
- **Project Mission & Scope**: High-level purpose and boundaries
- **Universal Development Standards**: Language-agnostic principles and practices
- **Team Collaboration Patterns**: Communication and workflow standards
- **Quick Navigation**: Links to distributed content and module overview
- **Emergency References**: Critical information needed immediately

**Size Target**: 150-200 lines maximum

### Distributable Content (MOVES to modules)
**Criteria**: Implementation patterns specific to particular components or technologies.

**Categories**:
- **Module-Specific Patterns**: Architecture and implementation approaches for specific components
- **Technology Configurations**: Framework-specific setup and configuration patterns
- **Integration Details**: How modules connect and communicate
- **Module Testing Strategies**: Component-specific testing approaches

**Distribution Target**: Module-specific CLAUDE.md files

### Reference Documentation (MOVES to ai_docs)
**Criteria**: Detailed examples, comprehensive guides, and supporting documentation.

**Categories**:
- **Implementation Examples**: Detailed code patterns and complete examples
- **Configuration Templates**: Full configuration files and setup scripts
- **Troubleshooting Guides**: Problem-solving patterns and debugging approaches
- **Performance Optimization**: Detailed optimization strategies and benchmarks

**Distribution Target**: PRPs/ai_docs/ structure

### Redundant Content (CONSOLIDATE or remove)
**Criteria**: Information that appears in multiple locations or duplicates existing documentation.

**Actions**:
- **Merge Similar Patterns**: Combine duplicate implementation approaches
- **Eliminate Redundancy**: Remove repeated information
- **Create Single Source**: Establish authoritative location for shared patterns

## Implementation Algorithm

### Phase 1: Ecosystem Assessment (10 minutes)
```markdown
DETECT current state:
- Measure root CLAUDE.md size (lines + characters)
- Scan for existing module CLAUDE.md files
- Identify existing ai_docs structure
- Determine execution mode based on size and structure

ANALYZE growth patterns:
- Compare against template baseline (if exists)
- Identify content categories present
- Map existing distribution (if any)
- Assess token efficiency current state
```

### Phase 2: Content Analysis & Categorization (20 minutes)
```markdown
FOR each section in root CLAUDE.md:
  ANALYZE content scope:
    - Universal applicability (template foundation)
    - Module specificity (distributable)
    - Implementation detail level (reference docs)
    - Redundancy with existing content

  CATEGORIZE using decision tree:
    IF applies to any project using this template:
      → Template Foundation (keep in root)
    ELSE IF specific to particular module/component:
      → Module Distribution (move to module CLAUDE.md)
    ELSE IF detailed implementation/example:
      → Reference Documentation (move to ai_docs)
    ELSE IF redundant with existing content:
      → Consolidation candidate (merge or remove)

  ASSIGN target location for distributable content
```

### Phase 3: Distribution Implementation (25 minutes)
```markdown
FOR each distributable content section:
  PREPARE target location:
    - Create module CLAUDE.md if missing
    - Establish ai_docs structure if needed
    - Verify target file organization

  MIGRATE content:
    - Move section to target location
    - Update cross-references and links
    - Maintain content relationships
    - Remove from root file

  UPDATE navigation:
    - Add reference links in root
    - Update module navigation
    - Ensure content remains discoverable
```

### Phase 4: Template Restoration (15 minutes)
```markdown
RESTRUCTURE root CLAUDE.md:
  TEMPLATE SECTION 1: Project Overview
    - Mission and scope (50-75 lines)
    - Key architectural decisions
    - Team standards and practices

  TEMPLATE SECTION 2: Navigation Hub
    - Module overview and links (25-50 lines)
    - Quick reference to distributed content
    - Emergency information access

  TEMPLATE SECTION 3: Universal Standards
    - Development practices (50-75 lines)
    - Quality standards
    - Collaboration patterns

TARGET SIZE: 150-250 lines total
```

### Phase 5: Validation & Success Verification (10 minutes)
```markdown
VERIFY size reduction:
  - Measure final root CLAUDE.md size
  - Calculate reduction percentage
  - Confirm target range achieved (150-250 lines)

VALIDATE content accessibility:
  - Test all navigation links
  - Verify distributed content is discoverable
  - Confirm no information loss occurred

ASSESS template reusability:
  - Check if root could serve as template for new projects
  - Verify universal applicability of remaining content
  - Confirm project-specific details properly distributed
```

## Success Metrics (Universal)

### Primary Goals
- **Template Restoration**: Root CLAUDE.md at 150-250 lines (75%+ reduction if oversized)
- **Knowledge Preservation**: 100% of project insights accessible through logical organization
- **Navigation Efficiency**: All content discoverable within 2 clicks from root
- **Template Reusability**: Root file ready for use in new projects

### Quality Indicators
- **Context Efficiency**: Module-specific content loaded only when relevant
- **Information Architecture**: Clear hierarchy from universal → specific → detailed
- **Cross-Reference Integrity**: All links functional after distribution
- **Growth Accommodation**: Structure supports future expansion without root pollution

## Multi-Run Safety & Consistency

### State Detection
- **First Run**: No distribution exists, implement fresh distribution
- **Maintenance Run**: Existing distribution detected, handle incremental changes
- **Reorganization Run**: Structural changes require redistribution
- **Recovery Run**: Inconsistencies detected, repair and rebalance

### Incremental Safety
- Always backup current state before changes
- Preserve existing distributed content organization
- Only move new growth or identified inconsistencies
- Maintain compatibility with existing workflows

### Validation Checkpoints
- Pre-run: Verify ecosystem state and backup completion
- Mid-run: Confirm content migration integrity
- Post-run: Validate size targets and accessibility

## Technology-Agnostic Implementation

### Universal Patterns Recognition
```markdown
IDENTIFY by structure, not technology:
- Configuration patterns (any language/framework)
- Testing approaches (any testing framework)
- Integration patterns (any architecture)
- Documentation structures (any project type)
```

### Content Scope Assessment
```markdown
EVALUATE universality:
- Would this apply to a Python project? Node.js project? Go project?
- Does this depend on specific frameworks or technologies?
- Is this an implementation detail or architectural principle?
```

### Distribution Logic
```markdown
ORGANIZE by information architecture:
- Scope: Universal → Project-specific → Module-specific → Implementation-specific
- Specificity: Principles → Patterns → Examples → Configuration
- Frequency: Always-needed → Context-dependent → Reference-only
```

---

**Usage**: This command automatically detects project state and applies appropriate distribution strategy. Works with any technology stack and scales from simple projects to complex enterprise architectures while maintaining PRP framework principles.