# Simple Workflow Step 4: Implement Feature

Implement a feature using the PRP file with parallel specialist development and continuous validation.

## Development Standards Integration
**CRITICAL**: All implementation must strictly follow the comprehensive coding standards defined in:
**File Reference**: `PRPs/ai_docs/development_standards.md`

**Application Instructions**: Read and apply ALL standards from the referenced file during implementation. Key enforcement areas include:
- File and function length limits (500/50 lines)
- AI agent patterns (module-global reuse only)
- Configuration factory patterns for environment variables
- Type safety, package management, and testing strategies
- Code quality tools (Ruff, MyPy) and project organization standards

**Validation Requirement**: Each specialist implementer must validate their code against the complete standards framework.

## PRP File: $ARGUMENTS

## Multi-Agent Implementation Process

### Phase 1: PRP Analysis and Planning (Lead Agent)
**Objective**: Understand requirements and orchestrate parallel implementation

1. **Load and Analyze PRP**
   ```bash
   # Read the specified PRP file completely
   cat "$ARGUMENTS"
   ```
   - Understand all context and requirements
   - Identify component boundaries for parallel development
   - Determine implementation dependencies and sequence

2. **ULTRATHINK Planning**
   - Create comprehensive implementation strategy
   - Break down into parallel component work streams
   - Identify validation checkpoints and integration points
   - Plan subagent coordination and validation schedule

### Phase 2: Parallel Subagent Implementation

Deploy specialist subagents for concurrent component development with early testing:

**Core Implementation Investigator**
- Objective: Investigate and implement the primary business logic and core functionality with immediate testing
- Task: "Based on the PRP requirements, investigate and implement: What is the core business logic and primary functionality? Build the main components, data models, and core algorithms. Write unit tests for each component immediately upon completion. Validate core functionality works in isolation."
- Implementation approach: Build component → Write unit tests → Validate locally → Report completion
- Output format: Core functionality with passing unit tests and validation confirmation
- Boundaries: Focus on primary business logic and core features, not external integrations or documentation

**Integration & Dependencies Investigator** 
- Objective: Investigate and implement system integration points and external dependencies with immediate testing
- Task: "Based on the PRP requirements, investigate and implement: What are the integration points and external dependencies? Build API connections, database integrations, and external service connections. Write integration tests immediately for each connection. Validate integrations work properly."
- Implementation approach: Build integration → Write integration tests → Validate connections → Report completion
- Output format: Working integrations with passing integration tests and connection validation
- Boundaries: Focus on external connections and system integrations, not core business logic

**Documentation & Completion Investigator**
- Objective: Investigate documentation requirements and ensure complete implementation with validated examples
- Task: "Based on the PRP requirements, investigate and implement: What documentation is needed? Create user guides, API documentation, and usage examples. Test that all documentation examples actually work. Validate completeness against PRP requirements."
- Implementation approach: Create documentation → Test examples → Validate completeness → Report completion
- Output format: Complete documentation with validated working examples
- Boundaries: Focus on documentation and completeness validation, not core implementation

**Testing & Validation Investigator**
- Objective: Investigate continuous integration needs and perform ongoing system validation as components complete
- Task: "Based on the PRP requirements and as other agents complete components, investigate: How do the components work together? Run continuous integration testing as components become available. Validate component interactions and perform end-to-end system testing. Fix integration issues between components."
- Implementation approach: Monitor component completion → Run integration validation → Test component interactions → Report system status
- Output format: Continuous validation reports and integration issue resolution
- Boundaries: Focus on integration testing and system validation, not individual component development

### Phase 3: Continuous Integration Workflow

**Parallel Development with Early Testing Timeline:**
```
Time 0: All agents begin parallel work
├── Core Agent: Builds core logic + unit tests
├── Integration Agent: Builds connections + integration tests  
├── Documentation Agent: Creates docs + tests examples
└── Validation Agent: Monitors and prepares integration tests

Time 5-10min: Core Agent completes first components
├── Core Agent: Reports component completion with passing unit tests
└── Validation Agent: Validates core components integrate properly

Time 7-12min: Integration Agent completes connections
├── Integration Agent: Reports integrations with passing tests
└── Validation Agent: Tests core + integration components together

Time 10-15min: Documentation Agent completes docs
├── Documentation Agent: Reports docs with validated examples
└── Validation Agent: Runs full system end-to-end validation

Time 15min+: System completion validation
└── Validation Agent: Confirms all PRP requirements met
```

### Phase 4: Progressive Validation Gates

**Component Completion Checkpoints:**
- [ ] **Core Logic**: Unit tests passing, business logic validated
- [ ] **Integrations**: Connection tests passing, external dependencies working
- [ ] **Documentation**: Examples tested and working, completeness validated
- [ ] **System Integration**: All components working together properly

**Continuous Validation Process:**
1. Each specialist agent validates their own components immediately
2. Validation agent runs integration tests as components complete
3. Early issue detection and resolution during development
4. Final system validation confirms complete PRP implementation

### Phase 5: Completion and Final Validation

**Multi-Agent Implementation Summary:**
```
MULTI-AGENT IMPLEMENTATION COMPLETE: [Feature Name]

COMPONENT COMPLETION STATUS:
├── Core Implementation: ✅ [Component details with test results]
├── Integration & Dependencies: ✅ [Integration details with test results]
├── Documentation & Examples: ✅ [Documentation details with validation results]
└── System Integration: ✅ [End-to-end validation results]

VALIDATION RESULTS:
- Unit Tests: [X/X passing]
- Integration Tests: [X/X passing] 
- End-to-End Tests: [X/X passing]
- Documentation Examples: [X/X working]

PRP IMPLEMENTATION: ✅ COMPLETE
All requirements from PRP successfully implemented with validated testing.
```

**Final PRP Compliance Check:**
- Re-read PRP to ensure all requirements implemented
- Validate all checklist items completed
- Confirm all validation commands pass
- Report any deviations or enhancements made

## Key Multi-Agent Benefits

- **Parallel development** of all components simultaneously
- **Early testing** - each component tested immediately upon completion
- **Continuous validation** - integration issues caught and resolved during development
- **Specialist expertise** - each agent focuses on their domain
- **Faster completion** - parallel work instead of sequential implementation
- **Higher quality** - continuous testing and validation throughout process

Enhanced implementation with early testing, continuous validation, and parallel specialist development for faster, higher-quality results.
