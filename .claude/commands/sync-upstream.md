# Universal Upstream Sync (Multi-Agent Enhanced)

Safely synchronize upstream repository changes while preserving local modifications using intelligent multi-agent coordination and universal project compatibility.

## Command: `/sync-upstream [options]`

**Universal Git upstream synchronization** that works across all project types with intelligent auto-detection and optional customization.

## Usage Examples

```bash
# Standard sync (works on any project immediately)
/sync-upstream

# Preview changes without executing
/sync-upstream --dry-run

# Force rebase instead of merge for cleaner history
/sync-upstream --force-rebase

# Skip validation (use with caution)
/sync-upstream --skip-validation
```

## Universal Compatibility

**Supported Project Types**: Python, Node.js, Go, Rust, Java, any Git repository
**Auto-Detection**: Automatically identifies project structure and available commands
**Optional Configuration**: Add `.sync-config` for custom behavior when needed
**Graceful Degradation**: Works even without project-specific tooling

## Multi-Agent Synchronization Process

### Phase 1: Universal State Analysis (Lead Agent)
**Objective**: Analyze current repository state and detect project characteristics

1. **Project Type Auto-Detection**
   ```bash
   # Universal project detection
   detect_project_type() {
       if [ -f "package.json" ]; then echo "nodejs"
       elif [ -f "pyproject.toml" ] || [ -f "requirements.txt" ]; then echo "python"
       elif [ -f "go.mod" ]; then echo "golang"
       elif [ -f "Cargo.toml" ]; then echo "rust"
       elif [ -f "pom.xml" ] || [ -f "build.gradle" ]; then echo "jvm"
       else echo "generic"
       fi
   }
   
   PROJECT_TYPE=$(detect_project_type)
   echo "🔍 Detected project type: $PROJECT_TYPE"
   ```

2. **Git Repository State Assessment**
   ```bash
   # Check current git state
   git status --porcelain
   git branch --show-current
   git remote -v
   
   # Identify uncommitted changes
   UNCOMMITTED_CHANGES=$(git status --porcelain | wc -l)
   CURRENT_BRANCH=$(git branch --show-current)
   
   echo "📊 Repository State:"
   echo "  - Current branch: $CURRENT_BRANCH"
   echo "  - Uncommitted changes: $UNCOMMITTED_CHANGES files"
   ```

3. **Remote Configuration Analysis**
   ```bash
   # Auto-detect remote relationships
   detect_remotes() {
       local remotes=$(git remote)
       local origin_remote=""
       local upstream_remote=""
       
       # Try to identify personal fork vs upstream
       for remote in $remotes; do
           local url=$(git remote get-url $remote)
           local user=$(git config user.name)
           
           if [[ $url == *"$user"* ]] || [[ $url == *"$(whoami)"* ]]; then
               origin_remote=$remote
           else
               upstream_remote=$remote
           fi
       done
       
       # Fallback to common naming conventions
       [ -z "$origin_remote" ] && origin_remote="origin"
       [ -z "$upstream_remote" ] && upstream_remote="upstream"
       
       echo "ORIGIN:$origin_remote UPSTREAM:$upstream_remote"
   }
   
   REMOTE_CONFIG=$(detect_remotes)
   ORIGIN_REMOTE=$(echo $REMOTE_CONFIG | cut -d: -f2 | cut -d' ' -f1)
   UPSTREAM_REMOTE=$(echo $REMOTE_CONFIG | cut -d: -f3)
   ```

### Phase 2: Configuration Discovery & Command Detection (Configuration Agent)
**Objective**: Load custom configuration or auto-detect available commands

1. **Configuration Loading Strategy**
   ```bash
   # Load .sync-config if it exists, otherwise auto-detect
   load_configuration() {
       if [ -f ".sync-config" ]; then
           echo "📝 Found .sync-config - loading custom configuration"
           source .sync-config
           CONFIG_SOURCE="custom"
       else
           echo "🔍 No .sync-config found - using auto-detection"
           auto_detect_commands
           CONFIG_SOURCE="auto-detected"
       fi
   }
   ```

2. **Universal Command Auto-Detection**
   ```bash
   # Auto-detect test commands based on project type
   auto_detect_commands() {
       case $PROJECT_TYPE in
           nodejs)
               TEST_COMMAND="npm test"
               BUILD_COMMAND="npm run build"
               QUALITY_COMMANDS="npm run lint || echo 'No lint script found'"
               ;;
           python)
               if [ -f "pytest.ini" ] || command -v pytest >/dev/null; then
                   TEST_COMMAND="pytest"
               else
                   TEST_COMMAND="python -m unittest discover"
               fi
               BUILD_COMMAND="python -m build || echo 'No build configured'"
               QUALITY_COMMANDS="ruff check . || flake8 . || echo 'No linter found'"
               ;;
           golang)
               TEST_COMMAND="go test ./..."
               BUILD_COMMAND="go build ."
               QUALITY_COMMANDS="go vet ./... && golint ./..."
               ;;
           rust)
               TEST_COMMAND="cargo test"
               BUILD_COMMAND="cargo build"
               QUALITY_COMMANDS="cargo clippy"
               ;;
           jvm)
               if [ -f "pom.xml" ]; then
                   TEST_COMMAND="mvn test"
                   BUILD_COMMAND="mvn compile"
                   QUALITY_COMMANDS="mvn checkstyle:check"
               else
                   TEST_COMMAND="gradle test"
                   BUILD_COMMAND="gradle build"
                   QUALITY_COMMANDS="gradle check"
               fi
               ;;
           generic)
               TEST_COMMAND="echo 'No standard test command detected'"
               BUILD_COMMAND="echo 'No standard build command detected'"
               QUALITY_COMMANDS="echo 'No standard quality checks detected'"
               ;;
       esac
       
       # Override with detected remote names
       [ -z "$UPSTREAM_REMOTE" ] && UPSTREAM_REMOTE=$(echo $REMOTE_CONFIG | cut -d: -f3)
       [ -z "$ORIGIN_REMOTE" ] && ORIGIN_REMOTE=$(echo $REMOTE_CONFIG | cut -d: -f2 | cut -d' ' -f1)
   }
   ```

3. **Configuration Validation**
   ```bash
   # Validate that detected/configured commands are available
   validate_configuration() {
       echo "🔧 Configuration Summary ($CONFIG_SOURCE):"
       echo "  - Test Command: $TEST_COMMAND"
       echo "  - Build Command: $BUILD_COMMAND"
       echo "  - Quality Commands: $QUALITY_COMMANDS"
       echo "  - Origin Remote: $ORIGIN_REMOTE"
       echo "  - Upstream Remote: $UPSTREAM_REMOTE"
       
       # Test that remotes exist
       git remote get-url "$ORIGIN_REMOTE" >/dev/null 2>&1 || echo "⚠️  Warning: $ORIGIN_REMOTE remote not found"
       git remote get-url "$UPSTREAM_REMOTE" >/dev/null 2>&1 || echo "⚠️  Warning: $UPSTREAM_REMOTE remote not found"
   }
   ```

### Phase 3: Parallel Multi-Agent Investigation

Deploy specialist agents for concurrent safety and synchronization analysis:

**Change Preservation Investigator**
- **Objective**: Investigate current local changes and create comprehensive backup strategy
- **Task**: "Analyze all local modifications and uncommitted changes. Create safety branches and backup procedures. Investigate: What local changes exist? How can they be safely preserved? What's the rollback strategy if sync fails?"
- **Safety Strategy**: Create timestamped backup branches, stash management, change cataloging
- **Output Format**: Complete backup plan with rollback procedures and change preservation confirmation
- **Boundaries**: Focus on change preservation and safety, not synchronization mechanics

**Upstream Analysis Investigator**
- **Objective**: Investigate upstream repository state and incoming changes
- **Task**: "Analyze upstream repository changes and potential conflicts. Investigate: What new commits are in upstream? Are there potential merge conflicts? What's the complexity of incoming changes? How should integration be handled?"
- **Analysis Areas**: Commit analysis, conflict prediction, change impact assessment
- **Output Format**: Upstream change summary with conflict risk assessment and integration strategy
- **Boundaries**: Focus on upstream analysis and integration planning, not local change management

**Validation Strategy Investigator**
- **Objective**: Investigate project-specific validation requirements and create testing strategy
- **Task**: "Design comprehensive validation approach for this specific project. Investigate: What tests should run? What quality checks are available? How do we verify sync success? What are the rollback triggers?"
- **Validation Design**: Test execution plan, quality gate definitions, success criteria
- **Output Format**: Complete validation framework with pass/fail criteria and rollback triggers
- **Boundaries**: Focus on validation strategy and success verification, not implementation details

### Phase 4: Synchronized Safety Implementation

**Combined Safety and Sync Workflow**:

1. **Pre-Sync Safety Measures**
   ```bash
   # Create comprehensive backup strategy
   create_safety_net() {
       local timestamp=$(date +"%Y%m%d_%H%M%S")
       local backup_branch="backup_before_sync_$timestamp"
       
       echo "🛡️  Creating safety net..."
       
       # Create backup branch from current state
       git branch "$backup_branch"
       echo "  ✅ Created backup branch: $backup_branch"
       
       # Stash any uncommitted changes
       if [ $UNCOMMITTED_CHANGES -gt 0 ]; then
           git stash push -m "Pre-sync stash $timestamp"
           echo "  ✅ Stashed $UNCOMMITTED_CHANGES uncommitted changes"
       fi
       
       # Record current state
       echo "  ✅ Current HEAD: $(git rev-parse HEAD)"
       echo "  ✅ Rollback command: git checkout $backup_branch"
   }
   ```

2. **Upstream Synchronization with Conflict Detection**
   ```bash
   # Safe upstream sync with early conflict detection
   perform_upstream_sync() {
       echo "🔄 Synchronizing with upstream..."
       
       # Fetch upstream changes
       git fetch "$UPSTREAM_REMOTE" || {
           echo "❌ Failed to fetch from $UPSTREAM_REMOTE"
           return 1
       }
       
       # Check for potential conflicts before merging
       local conflicts=$(git merge-tree $(git merge-base HEAD "$UPSTREAM_REMOTE/main") HEAD "$UPSTREAM_REMOTE/main" | grep -c "<<<<<<< ")
       
       if [ "$conflicts" -gt 0 ]; then
           echo "⚠️  Potential merge conflicts detected: $conflicts files"
           echo "   Proceeding with merge - conflicts will be marked for resolution"
       fi
       
       # Perform the sync (merge or rebase based on options)
       if [[ "$*" == *"--force-rebase"* ]]; then
           git rebase "$UPSTREAM_REMOTE/main" || {
               echo "❌ Rebase failed - use 'git rebase --abort' to rollback"
               return 1
           }
       else
           git merge "$UPSTREAM_REMOTE/main" || {
               echo "❌ Merge failed - conflicts need resolution"
               handle_merge_conflicts
               return 1
           }
       fi
       
       echo "✅ Upstream sync completed successfully"
   }
   ```

3. **Intelligent Conflict Resolution**
   ```bash
   # Universal conflict resolution assistance
   handle_merge_conflicts() {
       echo "🔍 Merge conflicts detected. Resolution options:"
       
       # List conflicted files with intelligent suggestions
       git status --porcelain | grep "^UU\|^AA" | while read status file; do
           echo "   📄 $file"
           
           # Provide context-aware suggestions
           case "$file" in
               *.md|*.txt|*.rst) echo "      💡 Documentation conflict - likely safe to manually resolve" ;;
               package.json|requirements.txt|go.mod|Cargo.toml) echo "      ⚠️  Dependency conflict - check for version compatibility" ;;
               *.py|*.js|*.go|*.rs|*.java) echo "      🔧 Code conflict - review changes carefully" ;;
               *) echo "      📝 General conflict - manual resolution needed" ;;
           esac
       done
       
       echo ""
       echo "🛠️  Resolution commands:"
       echo "   Accept upstream: git checkout --theirs <file>"
       echo "   Keep local: git checkout --ours <file>"
       echo "   Abort merge: git merge --abort"
       echo "   Continue after manual resolution: git commit"
   }
   ```

### Phase 5: Comprehensive Validation & Quality Assurance

**Multi-Level Validation Strategy**:

1. **Git Integrity Validation**
   ```bash
   # Universal git-based validation (works for all projects)
   validate_git_integrity() {
       echo "🔍 Validating git repository integrity..."
       
       # Check that we're still on a valid branch
       local current_branch=$(git branch --show-current)
       [ -n "$current_branch" ] || {
           echo "❌ Not on a valid branch after sync"
           return 1
       }
       
       # Verify remote tracking is intact
       git branch -vv | grep -q "$current_branch.*\[$ORIGIN_REMOTE" || {
           echo "⚠️  Branch tracking may need to be reset"
           echo "   Fix with: git branch --set-upstream-to=$ORIGIN_REMOTE/$current_branch"
       }
       
       # Check for lingering merge markers
       if git grep -l "^<<<<<<< \|^======= \|^>>>>>>> " 2>/dev/null; then
           echo "❌ Unresolved merge conflicts detected"
           return 1
       fi
       
       echo "✅ Git integrity validation passed"
   }
   ```

2. **Project-Specific Validation**
   ```bash
   # Adaptive validation based on project type and available tools
   run_project_validation() {
       echo "🧪 Running project-specific validation..."
       
       # Skip validation if requested
       [[ "$*" == *"--skip-validation"* ]] && {
           echo "⏩ Validation skipped by user request"
           return 0
       }
       
       local validation_failed=0
       
       # Run tests if available
       echo "  🔬 Running tests: $TEST_COMMAND"
       if ! eval "$TEST_COMMAND" 2>/dev/null; then
           echo "  ❌ Tests failed"
           validation_failed=1
       else
           echo "  ✅ Tests passed"
       fi
       
       # Run quality checks if available
       echo "  📊 Running quality checks: $QUALITY_COMMANDS"
       if ! eval "$QUALITY_COMMANDS" 2>/dev/null; then
           echo "  ⚠️  Quality checks failed (non-blocking)"
       else
           echo "  ✅ Quality checks passed"
       fi
       
       # Run build if available
       echo "  🏗️  Running build: $BUILD_COMMAND"
       if ! eval "$BUILD_COMMAND" 2>/dev/null; then
           echo "  ❌ Build failed"
           validation_failed=1
       else
           echo "  ✅ Build completed"
       fi
       
       return $validation_failed
   }
   ```

3. **Rollback and Recovery Procedures**
   ```bash
   # Comprehensive rollback system
   provide_rollback_options() {
       local timestamp=$(date +"%Y%m%d_%H%M%S")
       local backup_branch="backup_before_sync_$timestamp"
       
       echo "🔄 Rollback and Recovery Options:"
       echo ""
       echo "🛡️  Available Safety Nets:"
       echo "   1. Backup Branch: $backup_branch"
       echo "   2. Stash: $(git stash list | head -1 | cut -d: -f1 || echo 'No stash available')"
       echo "   3. Reflog: git reflog --oneline -10"
       echo ""
       echo "🔧 Rollback Commands:"
       echo "   Full rollback: git checkout $backup_branch"
       echo "   Restore stash: git stash pop"
       echo "   Reset to previous state: git reset --hard HEAD~1"
       echo "   Interactive recovery: git reflog"
       echo ""
       echo "⚠️  If sync failed, choose appropriate recovery method above"
   }
   ```

### Phase 6: Success Reporting and Next Steps

**Comprehensive Success Analysis**:

```bash
# Generate detailed sync report
generate_sync_report() {
    local sync_status=$1
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    
    echo "📊 UNIVERSAL UPSTREAM SYNC REPORT - $timestamp"
    echo "================================================================"
    echo ""
    echo "🎯 OPERATION STATUS: $([ $sync_status -eq 0 ] && echo "✅ SUCCESS" || echo "❌ FAILED")"
    echo ""
    echo "📂 PROJECT INFORMATION:"
    echo "   Project Type: $PROJECT_TYPE"
    echo "   Configuration: $CONFIG_SOURCE"
    echo "   Repository: $(git remote get-url $ORIGIN_REMOTE 2>/dev/null || echo 'Unknown')"
    echo ""
    echo "🔄 SYNCHRONIZATION DETAILS:"
    echo "   Origin Remote: $ORIGIN_REMOTE"
    echo "   Upstream Remote: $UPSTREAM_REMOTE"
    echo "   Sync Method: $([ "$*" == *"--force-rebase"* ] && echo "Rebase" || echo "Merge")"
    echo "   Changes Applied: $(git log --oneline $ORIGIN_REMOTE/main..HEAD | wc -l) commits"
    echo ""
    echo "🧪 VALIDATION RESULTS:"
    echo "   Tests: $([ $sync_status -eq 0 ] && echo "✅ Passed" || echo "❌ Failed")"
    echo "   Build: $([ $sync_status -eq 0 ] && echo "✅ Passed" || echo "❌ Failed")"
    echo "   Git Integrity: $([ $sync_status -eq 0 ] && echo "✅ Verified" || echo "❌ Issues detected")"
    echo ""
    
    if [ $sync_status -eq 0 ]; then
        echo "🎉 NEXT STEPS:"
        echo "   1. Push changes: git push $ORIGIN_REMOTE $(git branch --show-current)"
        echo "   2. Clean up: git branch -d backup_before_sync_*"
        echo "   3. Continue development with latest upstream changes"
    else
        provide_rollback_options
    fi
    
    echo ""
    echo "================================================================"
}
```

## Advanced Features

### Optional .sync-config Configuration

Create a `.sync-config` file in your project root for custom behavior:

```bash
# .sync-config example for complex project
TEST_COMMAND="docker-compose run --rm tests"
BUILD_COMMAND="make build-all"
QUALITY_COMMANDS="make lint && make security-scan"
UPSTREAM_REMOTE="enterprise-upstream"
ORIGIN_REMOTE="my-fork"

# Custom validation (optional)
CUSTOM_VALIDATION="./scripts/integration-tests.sh"
```

### Dry Run Mode

Preview what the sync would do without making changes:

```bash
# Dry run implementation
if [[ "$*" == *"--dry-run"* ]]; then
    echo "🔍 DRY RUN MODE - No changes will be made"
    echo ""
    echo "Would execute the following operations:"
    echo "1. Fetch from $UPSTREAM_REMOTE"
    echo "2. $([ "$*" == *"--force-rebase"* ] && echo "Rebase" || echo "Merge") $UPSTREAM_REMOTE/main"
    echo "3. Run validation: $TEST_COMMAND"
    echo "4. Push to $ORIGIN_REMOTE"
    echo ""
    echo "To execute: /sync-upstream (remove --dry-run)"
    exit 0
fi
```

### Error Recovery and Troubleshooting

**Common Issue Resolution**:

```bash
# Automated issue detection and resolution suggestions
diagnose_sync_issues() {
    echo "🔍 Diagnosing potential sync issues..."
    
    # Check for authentication issues
    if ! git ls-remote "$UPSTREAM_REMOTE" >/dev/null 2>&1; then
        echo "❌ Cannot access upstream remote: $UPSTREAM_REMOTE"
        echo "   Fix: Check network connection and authentication"
        echo "   Command: git remote -v"
    fi
    
    # Check for local modifications preventing merge
    if [ $(git status --porcelain | wc -l) -gt 0 ]; then
        echo "⚠️  Uncommitted changes detected"
        echo "   These will be automatically stashed during sync"
    fi
    
    # Check for detached HEAD
    if ! git symbolic-ref HEAD >/dev/null 2>&1; then
        echo "❌ Repository is in detached HEAD state"
        echo "   Fix: git checkout main (or your main branch)"
    fi
    
    # Check for missing upstream remote
    if ! git remote | grep -q "$UPSTREAM_REMOTE"; then
        echo "❌ Upstream remote not configured: $UPSTREAM_REMOTE"
        echo "   Fix: git remote add $UPSTREAM_REMOTE <upstream-url>"
    fi
}
```

## Universal Compatibility Matrix

| Project Type | Auto-Detection | Test Command | Build Command | Quality Checks |
|--------------|----------------|--------------|---------------|----------------|
| **Python** | ✅ pyproject.toml/requirements.txt | pytest/unittest | python -m build | ruff/flake8 |
| **Node.js** | ✅ package.json | npm test | npm run build | npm run lint |
| **Go** | ✅ go.mod | go test ./... | go build | go vet/golint |
| **Rust** | ✅ Cargo.toml | cargo test | cargo build | cargo clippy |
| **Java/Maven** | ✅ pom.xml | mvn test | mvn compile | mvn checkstyle:check |
| **Java/Gradle** | ✅ build.gradle | gradle test | gradle build | gradle check |
| **Generic Git** | ✅ Fallback | Configurable | Configurable | Configurable |

## Key Multi-Agent Benefits

### Safety Through Redundancy
- **Multiple backup strategies** - branches, stashes, and state tracking
- **Early conflict detection** - prevent issues before they cause problems
- **Comprehensive validation** - multiple levels of verification
- **Universal rollback procedures** - safe recovery for any failure scenario

### Intelligence Through Specialization
- **Project-aware adaptation** - handles different tech stacks appropriately
- **Configuration flexibility** - auto-detection with custom override options
- **Conflict resolution assistance** - context-aware guidance for merge issues
- **Quality assurance integration** - validation that matches project capabilities

### Universal Workflow Principles
- **Works immediately** - no setup required for standard projects
- **Graceful degradation** - functions even with missing tools
- **Customizable when needed** - .sync-config for special requirements
- **Consistent safety** - same protection regardless of project type

## Usage in Different Project Types

### Python Project Example
```bash
cd my-python-project
/sync-upstream
# Auto-detects: pytest, ruff, python -m build
# Uses: origin/upstream remotes
# Result: Safe sync with Python-specific validation
```

### Node.js Project Example
```bash
cd my-react-app
/sync-upstream --force-rebase
# Auto-detects: npm test, npm run lint, npm run build
# Uses: clean rebase for linear history
# Result: Safe sync with Node.js-specific validation
```

### Custom Project Example
```bash
cd my-complex-project
echo 'TEST_COMMAND="docker-compose run tests"' > .sync-config
/sync-upstream
# Uses: custom Docker-based testing
# Fallback: Git-based validation
# Result: Safe sync with custom validation
```

Enhanced universal upstream synchronization with multi-agent coordination, comprehensive safety nets, and intelligent adaptation to any project type while maintaining full control and customization options.