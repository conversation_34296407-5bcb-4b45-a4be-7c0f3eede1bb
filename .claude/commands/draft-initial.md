# draft-initial

Intelligently draft INITIAL.md template from basic descriptions through automatic project context discovery and lightweight research.

## Usage
```
/draft-initial <basic-description>
```

**Examples:**
- `/draft-initial "deploy my Python app with Docker for team testing"`
- `/draft-initial "add authentication to my API"`
- `/draft-initial "create automated testing pipeline"`

## Purpose

Transforms basic feature descriptions into reasonably complete INITIAL.md templates by automatically discovering project context and conducting focused research. Eliminates manual explanation rounds while providing solid foundation for enhancement pipeline.

**Duration**: 60-90 seconds
**Output**: 70% complete INITIAL.md ready for enhancement workflow

## Context Discovery Process

### Phase 1: Project Intelligence (30 seconds)
**Project Context Analyst**:
- Scan project structure (pyproject.toml, package.json, src/, requirements)
- Identify technology stack and architecture patterns
- Detect existing documentation and standards (CLAUDE.md, README.md, PRPs/)
- Classify request type: new feature vs infrastructure vs enhancement
- Determine appropriate SCOPE recommendation (prototype vs production)

**Output**: Clear understanding of project foundation and request context

### Phase 2: Focused Template Population (45 seconds)
**Feature Specification Writer**:
- Transform basic description into technical requirements using project context
- Add technology-specific considerations based on detected stack
- Include integration points with existing system architecture
- Specify performance and security baseline requirements

**Quick Pattern Finder**:
- Identify 3-4 relevant existing files from codebase scan
- Find basic documentation starting points for detected technologies
- Gather obvious gotchas for the technology stack and request type
- Note project-specific patterns from existing standards

## Template Generation Strategy

**SCOPE Population**:
- Analyze request complexity and project maturity
- Recommend prototype for exploration, production for known requirements
- Consider existing system sophistication and deployment needs

**FEATURE Section**:
- Convert description to technical specification using project context
- Include inputs, outputs, and integration points with existing system
- Add basic performance and security considerations
- Specify technology-specific implementation approaches

**EXAMPLES Section**:
- List 3-4 relevant files from automatic codebase discovery
- Brief explanation of why each example is relevant
- Note architectural patterns demonstrated
- Focus on existing precedents and standards

**DOCUMENTATION Section**:
- 4-6 authoritative documentation links for detected technologies
- Official docs, best practices guides, and implementation references
- Version-specific documentation when project dependencies are clear
- Integration and deployment documentation relevant to request

**OTHER CONSIDERATIONS**:
- Technology stack specific gotchas and best practices
- Project-specific standards and patterns from context discovery
- Basic security, performance, and deployment considerations
- Common failure modes for the detected implementation approach

## Quality Standards

**Context Completeness**: Capture enough project understanding to enable enhancement pipeline success

**Technology Awareness**: Leverage detected stack for relevant patterns and documentation

**Implementation Foundation**: Provide clear technical direction without deep architectural analysis

**Enhancement Readiness**: Create solid foundation for comprehensive research phases

## Output Format

```markdown
# Feature Request: [Descriptive Technical Name]

## SCOPE:
[prototype|production] - [Brief justification based on complexity and project context]

## FEATURE:
[Technical description enhanced with project context]
- [Specific functionality based on detected architecture]
- [Integration considerations with existing system]
- [Technology-specific implementation approach]
- [Basic performance and security requirements]

## EXAMPLES:
[3-4 relevant codebase files from automatic discovery]
- `[detected-file-1]` - [Why relevant and pattern demonstrated]
- `[existing-standard-2]` - [Architectural guidance from context]
- `[precedent-implementation-3]` - [Usage pattern to follow]

## DOCUMENTATION:
[4-6 technology-specific documentation links]
- [official-docs-url] - [Technology foundation and best practices]
- [implementation-guide-url] - [Practical implementation guidance]
- [integration-docs-url] - [System integration patterns]

## OTHER CONSIDERATIONS:
[Technology and project-specific considerations]
- [Stack-specific best practice from context discovery]
- [Project standard from existing patterns]
- [Common gotcha for detected implementation approach]
- [Security/performance consideration for technology stack]
```

## Integration with Enhancement Pipeline

**Handoff Quality**: Generated template provides sufficient context for enhancement command to add comprehensive research, validation, and detailed implementation guidance.

**Research Foundation**: Basic technology understanding and project context eliminates manual explanation rounds in later phases.

**Workflow Continuity**: Output seamlessly feeds into existing enhancement workflow and modular development pipeline.

---

**Success Criteria**: User provides basic description, receives populated INITIAL.md that captures project context and provides solid foundation for enhancement research without manual explanation rounds.