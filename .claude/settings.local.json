{"permissions": {"allow": ["Bash(tree:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(git add:*)", "Bash(git commit:*)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__list_directory", "mcp__brave-search__brave_web_search", "mcp__filesystem__read_multiple_files", "mcp__Context7__resolve-library-id", "mcp__perplexity-mcp__perplexity_search_web", "mcp__Context7__get-library-docs", "mcp__fetch__fetch", "mcp__filesystem__read_file", "mcp__filesystem__search_files", "Bash(grep:*)", "Bash(uv sync:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(uv add:*)", "<PERSON><PERSON>(uv run ruff:*)", "Bash(uv run pytest:*)", "<PERSON><PERSON>(uv run:*)", "WebFetch(domain:spec.modelcontextprotocol.io)", "WebFetch(domain:modelcontextprotocol.io)", "WebFetch(domain:ai.pydantic.dev)", "WebFetch(domain:help.getzep.com)", "WebFetch(domain:github.com)", "WebFetch(domain:neo4j.com)", "mcp__filesystem__create_directory", "mcp__filesystem__write_file", "<PERSON><PERSON>(git clone:*)", "mcp__filesystem__get_file_info", "Bash(PYTHONPATH=. uv run pytest src/config/tests/ -v --cov=src/config --cov-report=term --cov-report=html)", "Bash(PYTHONPATH=. uv run pytest src/config/tests/ -v --cov=src/config --cov-report=term -x)", "Bash(PYTHONPATH=. uv run pytest src/config/tests/ -v --cov=src/config --cov-report=term)", "Bash(PYTHONPATH=. uv run pytest src/config/tests/test_integration.py -v)"], "deny": []}}