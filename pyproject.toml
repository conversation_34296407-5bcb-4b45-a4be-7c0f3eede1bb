[project]
name = "dynamo-prp-share"
version = "0.1.0"
description = "Library of assets and prompts for Ai Engineering"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-dotenv>=1.1.1",
]

[dependency-groups]
dev = [
    "mypy>=1.17.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "ruff>=0.12.3",
]
