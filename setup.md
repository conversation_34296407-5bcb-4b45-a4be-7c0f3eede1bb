FRAMEWORK_DIR="/Users/<USER>/Desktop/agentig_coding_framework"


# Copy PRP framework
echo "📁 Copying PRP framework files..."
cp -r "$FRAMEWORK_DIR/.claude" .          # All commands and settings
cp -r "$FRAMEWORK_DIR/.devcontainer" .    # Devcontainer for YOLO mode
cp -r "$FRAMEWORK_DIR/PRPs" .             # Templates and structure  
cp "$FRAMEWORK_DIR/INITIAL.md" .          # Feature request template
cp "$FRAMEWORK_DIR/CLAUDE.md" .           # Project guidelines
cp "$FRAMEWORK_DIR/pyproject.toml" .      # Base Python dependencies

# Setup Python development environment
echo "🐍 Setting up Python environment..."
uv venv                                   # Create virtual environment

# Activate virtual environment (zsh-safe)
echo "🔄 Activating virtual environment..."
source .venv/bin/activate

# Verify activation worked
if [[ "$VIRTUAL_ENV" == *".venv"* ]]; then
    echo "✅ Virtual environment activated: $VIRTUAL_ENV"
else
    echo "❌ Virtual environment activation failed"
    exit 1
fi

uv sync                                   # Install base dependencies

# Create Python project structure
echo "📁 Creating project structure..."
mkdir -p src tests docs
touch src/__init__.py
touch tests/__init__.py
echo "# {your_project_name}" > README.md

# Setup PRP working directories
mkdir -p PRPs/completed
touch PRPs/completed/.gitkeep

# Initialize git repository
echo "🔧 Initializing git repository..."
git init

# Create .gitignore (using here-doc, zsh-safe)
cat > .gitignore << 'EOF'
# Python
.venv/
__pycache__/
*.pyc
*.pyo
*.egg-info/
dist/
build/

# PRP Framework
PRPs/completed/*.md
!PRPs/completed/.gitkeep

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
EOF

git add .
git commit -m "Initial project setup with PRP framework"

# Final verification
echo "🎯 Setup complete! Verification:"
echo "  Shell: $(ps -p $ -o comm=)"
echo "  Python: $(which python)"
echo "  Virtual env: $VIRTUAL_ENV"
echo "  Git status: $(git status --porcelain | wc -l) untracked files"