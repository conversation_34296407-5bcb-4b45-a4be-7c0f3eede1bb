# Module Implementation PRP: F3-external-mcp-interface

## Executive Summary
- **Module Purpose**: External MCP interface for cross-platform communication with <PERSON>, <PERSON>, and custom agents
- **Project Context**: Foundation Phase 1 module enabling cross-platform continuity within Intelligent Memory Orchestrator
- **Implementation Scope**: MCP server with direct Memory Agent integration (no database abstraction)
- **Success Criteria**: <100ms MCP response time, full protocol compliance, seamless cross-platform memory operations

## Module Context & Requirements

### Module Specification Summary
- **Module Type**: Foundation Phase 1 - External Interface Only
- **Complexity Score**: 4/10 with medium complexity MCP protocol implementation
- **Project Role**: Bridge between external platforms and Memory Agent for intelligent memory operations
- **Technology Focus**: FastAPI, MCP protocol, AsyncIO, WebSocket/HTTP transport

### Project Integration Context
- **Project Architecture**: Hybrid architecture with external interface (F3) + internal operations (C1 Memory Agent)
- **Cross-Module Dependencies**: F1 (Configuration), F2 (Infrastructure), direct integration with C1 (Memory Agent)
- **Interface Requirements**: MCP protocol compliance, direct Memory Agent communication, connection management
- **Project Standards**: Module-global agent reuse, configuration factory pattern, 500/50 line limits

### Module-Specific Requirements
- **Functional Requirements**: 
  - MCP server implementation with tool registration
  - Direct Memory Agent operation translation (no database abstraction)
  - Cross-platform client connection management
  - Protocol compliance for Claude Desktop, Claude Code, custom agents
- **Performance Requirements**: 
  - <100ms MCP tool discovery and routing
  - <50ms Memory Agent call overhead
  - <200ms connection establishment
  - Support 100+ concurrent external connections
- **Security Requirements**: 
  - Input validation for all MCP operations
  - Connection rate limiting and authentication
  - Secure error handling without information disclosure
- **Operational Requirements**: 
  - Graceful degradation on Memory Agent failures
  - Comprehensive monitoring and logging
  - Connection health management

## Implementation Blueprint

### Development Phases

**Phase 1: Foundation Setup (Day 1)**
- MCP server initialization with FastAPI
- Basic protocol handler implementation
- Memory Agent integration patterns
- Development environment setup
- **Validation Checkpoint**: MCP server starts and accepts connections

**Phase 2: Core Implementation (Days 2-3)**
- Memory operation tool registration
- Direct Memory Agent communication layer
- Connection management implementation
- Error handling and validation
- **Validation Checkpoint**: Memory operations work via MCP protocol

**Phase 3: Integration & Validation (Day 4)**
- Cross-platform client testing
- Performance optimization and monitoring
- Error handling refinement
- Documentation and deployment preparation
- **Validation Checkpoint**: Full MCP protocol compliance and performance targets met

### Code Organization Strategy

```
src/mcp/
├── __init__.py              # Module exports with global interfaces
├── mcp_server.py            # MCP server implementation (<300 lines)
├── memory_interface.py      # Memory Agent translation layer (<250 lines)
├── protocol_handler.py      # MCP protocol handling (<200 lines)
├── connection_manager.py    # External connection management (<250 lines)
├── models.py                # MCP-specific models (<200 lines)
└── tests/
    ├── test_mcp_server.py   # MCP server tests (<300 lines)
    ├── test_memory_interface.py # Memory Agent integration tests (<250 lines)
    ├── test_protocol_handler.py # Protocol compliance tests (<200 lines)
    └── test_connection_manager.py # Connection management tests (<250 lines)
```

### Technology Implementation Approach

**MCP Server Implementation**:
```python
from fastapi import FastAPI, WebSocket, HTTPException
from mcp import Server, Tool
from typing import Dict, Any, List
import asyncio

class MCPMemoryInterface:
    """External MCP interface for memory operations."""
    
    def __init__(self, memory_agent_deps: MemoryAgentDeps):
        self.memory_agent_deps = memory_agent_deps
        self.mcp_server = Server("memory-orchestrator")
        self.app = FastAPI(title="Memory MCP Interface")
        self._register_tools()
        self._setup_routes()
    
    def _register_tools(self):
        """Register MCP tools for memory operations."""
        
        @self.mcp_server.tool("store_memory")
        async def store_memory(
            conversation_context: Dict[str, Any],
            content: str,
            storage_priority: str = "medium"
        ) -> Dict[str, Any]:
            """Store memory via direct Memory Agent operation."""
            try:
                # Direct Memory Agent evaluation
                relevance_result = await memory_agent.run(
                    "evaluate_memory_relevance",
                    conversation_context=ConversationContext(**conversation_context),
                    content=content,
                    deps=self.memory_agent_deps
                )
                
                if relevance_result.should_store:
                    # Direct Memory Agent storage
                    success = await memory_agent.run(
                        "store_memory_episode",
                        conversation_context=ConversationContext(**conversation_context),
                        content=content,
                        storage_priority=storage_priority,
                        deps=self.memory_agent_deps
                    )
                    
                    return {
                        "success": success,
                        "message": "Memory stored successfully" if success else "Storage failed",
                        "confidence": relevance_result.confidence,
                        "reasoning": relevance_result.reasoning
                    }
                else:
                    return {
                        "success": False,
                        "message": "Content not relevant for storage",
                        "confidence": relevance_result.confidence,
                        "reasoning": relevance_result.reasoning
                    }
            except Exception as e:
                return {
                    "success": False,
                    "message": f"Storage error: {str(e)}",
                    "error": str(e)
                }
        
        @self.mcp_server.tool("retrieve_memory")
        async def retrieve_memory(
            query: str,
            conversation_context: Dict[str, Any],
            max_results: int = 5
        ) -> Dict[str, Any]:
            """Retrieve memory via direct Memory Agent operation."""
            try:
                # Direct Memory Agent query structuring
                structured_query = await memory_agent.run(
                    "structure_memory_query",
                    query_context=QueryContext(**conversation_context),
                    search_intent=query,
                    deps=self.memory_agent_deps
                )
                
                # Direct Memory Agent search
                results = await memory_agent.run(
                    "search_memory_graph",
                    query=structured_query.search_terms,
                    num_results=min(max_results, structured_query.max_results),
                    deps=self.memory_agent_deps
                )
                
                # Direct Memory Agent applicability assessment
                applicability_result = await memory_agent.run(
                    "assess_context_applicability",
                    retrieved_memory=results,
                    current_context=ConversationContext(**conversation_context),
                    deps=self.memory_agent_deps
                )
                
                return {
                    "success": True,
                    "query": structured_query.model_dump(),
                    "results": [result.model_dump() for result in applicability_result.applicable_items],
                    "relevance_scores": applicability_result.relevance_scores,
                    "confidence": applicability_result.confidence,
                    "reasoning": applicability_result.reasoning
                }
            except Exception as e:
                return {
                    "success": False,
                    "message": f"Retrieval error: {str(e)}",
                    "error": str(e)
                }
```

**Connection Management Implementation**:
```python
class MCPConnectionManager:
    """Manage external MCP client connections."""
    
    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.active_connections: Dict[str, MCPConnection] = {}
        self.connection_pool = asyncio.Queue(maxsize=max_connections)
    
    async def handle_connection(self, client_id: str, transport: Transport):
        """Handle new MCP client connection."""
        if len(self.active_connections) >= self.max_connections:
            await transport.close("Connection limit reached")
            return
        
        connection = MCPConnection(client_id, transport)
        self.active_connections[client_id] = connection
        
        try:
            await connection.handle_requests()
        except Exception as e:
            logger.error(f"Connection error for {client_id}: {e}")
        finally:
            self.active_connections.pop(client_id, None)
            await connection.close()
    
    async def broadcast_memory_update(self, update: MemoryUpdate):
        """Broadcast memory updates to relevant connections."""
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_update(update)
            except Exception as e:
                logger.warning(f"Failed to send update to {client_id}: {e}")
```

### Interface Implementation

**MCP Protocol Compliance**:
```python
class MCPProtocolHandler:
    """Handle MCP protocol operations."""
    
    def __init__(self, mcp_server: Server):
        self.mcp_server = mcp_server
    
    async def handle_tool_discovery(self) -> List[Tool]:
        """Handle MCP tool discovery requests."""
        return [
            Tool(
                name="store_memory",
                description="Store memory content via Memory Agent",
                parameters={
                    "conversation_context": {"type": "object", "required": True},
                    "content": {"type": "string", "required": True},
                    "storage_priority": {"type": "string", "default": "medium"}
                }
            ),
            Tool(
                name="retrieve_memory",
                description="Retrieve memory content via Memory Agent",
                parameters={
                    "query": {"type": "string", "required": True},
                    "conversation_context": {"type": "object", "required": True},
                    "max_results": {"type": "integer", "default": 5}
                }
            )
        ]
    
    async def handle_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle MCP tool calls with validation."""
        if tool_name not in self.mcp_server.tools:
            raise MCPError(f"Unknown tool: {tool_name}")
        
        tool = self.mcp_server.tools[tool_name]
        
        # Validate parameters
        validated_params = self._validate_parameters(tool, parameters)
        
        # Execute tool
        try:
            result = await tool.execute(**validated_params)
            return {
                "success": True,
                "result": result,
                "tool": tool_name
            }
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name
            }
```

## Comprehensive Testing Strategy

### Unit Testing Approach

**Test Framework**: pytest with asyncio support for async MCP operations
**Test Coverage**: 85%+ coverage target for all MCP components
**Test Organization**: Co-located tests in src/mcp/tests/ directories
**Mock Strategies**: Mock Memory Agent operations for isolated F3 testing

**MCP Server Testing**:
```python
import pytest
from unittest.mock import AsyncMock, patch
from src.mcp.mcp_server import MCPMemoryInterface
from src.mcp.models import ConversationContext, MemoryDecision

class TestMCPMemoryInterface:
    """Test MCP server functionality."""
    
    @pytest.fixture
    def mcp_interface(self):
        """Create MCP interface for testing."""
        mock_deps = AsyncMock()
        return MCPMemoryInterface(mock_deps)
    
    @pytest.mark.asyncio
    async def test_store_memory_tool(self, mcp_interface):
        """Test memory storage tool."""
        # Mock Memory Agent response
        mock_relevance = MemoryDecision(
            should_store=True,
            confidence=0.9,
            reasoning="High relevance content",
            storage_priority="high"
        )
        
        with patch('memory_agent.run') as mock_run:
            mock_run.side_effect = [mock_relevance, True]
            
            result = await mcp_interface.store_memory(
                conversation_context={"user_id": "test", "platform": "claude_desktop"},
                content="Important user preference"
            )
            
            assert result["success"] is True
            assert result["confidence"] == 0.9
            assert "reasoning" in result
    
    @pytest.mark.asyncio
    async def test_retrieve_memory_tool(self, mcp_interface):
        """Test memory retrieval tool."""
        mock_structured_query = StructuredQuery(
            search_terms=["coding", "preference"],
            max_results=5
        )
        mock_results = [MemoryResult(content="Prefers functional programming")]
        mock_applicability = ApplicabilityResult(
            applicable_items=mock_results,
            relevance_scores=[0.8],
            confidence=0.8
        )
        
        with patch('memory_agent.run') as mock_run:
            mock_run.side_effect = [mock_structured_query, mock_results, mock_applicability]
            
            result = await mcp_interface.retrieve_memory(
                query="coding preferences",
                conversation_context={"user_id": "test", "platform": "claude_code"}
            )
            
            assert result["success"] is True
            assert len(result["results"]) == 1
            assert result["confidence"] == 0.8
```

### Integration Testing Strategy

**Cross-Module Testing**: Test F3 integration with Memory Agent (C1)
**External Service Testing**: Test MCP protocol compliance with mock clients
**Interface Testing**: Test F3 API contracts and data flows
**Performance Testing**: Load testing and response time validation

### Module Validation Framework

**Acceptance Criteria**: MCP protocol compliance, Memory Agent integration, performance targets
**Quality Gates**: Test coverage >85%, performance benchmarks met, error handling validated
**Automated Validation**: CI/CD pipeline with MCP client testing
**Manual Testing**: Cross-platform client validation procedures

## Quality Assurance Framework

### Code Quality Standards

**Development Standards Compliance**:
- **File Length**: <300 lines per file (target), <500 lines maximum (enforced)
- **Function Length**: <40 lines per function (target), <50 lines maximum (enforced)
- **AI Agents**: F3 contains no AI agents but integrates with Memory Agent (C1)
- **Configuration**: Factory pattern for environment variable respect
- **Type Safety**: Complete type hints for all functions and classes

### Error Handling & Edge Cases

**Error Scenarios**: MCP protocol errors, Memory Agent failures, connection timeouts
**Edge Case Management**: Connection limit reached, malformed requests, authentication failures
**Failure Recovery**: Graceful degradation, retry mechanisms, connection restoration
**Monitoring & Alerting**: Comprehensive error tracking with Logfire integration

### Performance & Optimization

**Performance Targets**:
- **MCP Response Time**: <100ms for tool discovery and routing
- **Memory Operation Translation**: <50ms overhead for Memory Agent calls
- **Connection Management**: <200ms for connection establishment
- **Error Recovery**: <500ms for graceful degradation and retry
- **Throughput**: Support 100+ concurrent external connections

## Development Environment & Tools

### Local Development Setup

**Environment Requirements**:
- Python 3.12+ with UV package manager
- Neo4j database for Memory Agent integration
- Redis for caching and performance optimization
- Docker for containerized development
- Logfire account for monitoring

**Development Configuration**:
```bash
# F3 Module Environment Variables
MCP_HOST=localhost
MCP_PORT=8000
MCP_MAX_CONNECTIONS=100
MCP_TIMEOUT=30
MCP_LOG_LEVEL=INFO
MCP_ENABLE_MONITORING=true
MCP_CACHE_TTL=300

# Memory Agent Integration
MEMORY_AGENT_MODEL=claude-3-sonnet-********
MEMORY_AGENT_TEMPERATURE=0.1
MEMORY_AGENT_MAX_TOKENS=4000

# Infrastructure Dependencies
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password
REDIS_URL=redis://localhost:6379
LOGFIRE_TOKEN=your_logfire_token
```

### Build & Deployment Pipeline

**Build Process**: UV-based dependency management with containerized deployment
**Testing Automation**: Pytest with asyncio support and MCP protocol validation
**Deployment Strategy**: Docker containers with health checks and monitoring
**Monitoring Integration**: Logfire distributed tracing and performance metrics

## Implementation Validation & Success Criteria

### Module Completion Criteria

- [ ] **Core Functionality**: MCP server operational with registered memory operation tools
- [ ] **Development Standards Compliance**: All development_standards.md requirements met:
  - [ ] File length limits enforced (<300 lines per file target)
  - [ ] Function length limits enforced (<40 lines per function target)
  - [ ] No AI agents in F3 (only Memory Agent integration)
  - [ ] Configuration factory patterns applied
  - [ ] Type safety validated with comprehensive type hints
- [ ] **Interface Implementation**: All MCP protocol tools implemented and tested
- [ ] **Integration Testing**: Cross-platform client integration validated
- [ ] **Performance Validation**: Response time targets met (<100ms MCP, <50ms Memory Agent overhead)
- [ ] **Quality Standards**: Code quality and documentation standards met
- [ ] **Testing Coverage**: 85%+ testing coverage with comprehensive test suite

### Project Integration Validation

- [ ] **Architectural Compliance**: F3 aligns with hybrid architecture (external interface only)
- [ ] **Standards Compliance**: F3 follows all project coding and quality standards
- [ ] **Interface Compatibility**: F3 interfaces work correctly with Memory Agent (C1)
- [ ] **Performance Integration**: F3 performance integrates well with 500ms-2s system targets
- [ ] **Cross-Platform Compatibility**: Successful operation with Claude Desktop, Claude Code, custom agents
- [ ] **Error Handling**: Comprehensive error handling with graceful degradation
- [ ] **Monitoring Integration**: Logfire monitoring operational with performance tracking

### Autonomous Implementation Confidence

**Implementation Readiness Score**: 9/10
**Single-Pass Success Probability**: 85%
**Risk Assessment**: Low risk - well-defined interfaces, clear requirements, proven technology stack
**Support Requirements**: Minimal - comprehensive specification with detailed implementation guidance

## Next Steps

### Implementation Workflow

1. **Environment Setup**: Set up MCP development environment with FastAPI and dependencies
2. **Foundation Development**: Implement core MCP server structure and protocol handling
3. **Memory Agent Integration**: Develop direct Memory Agent communication layer
4. **Connection Management**: Implement external client connection management
5. **Testing & Validation**: Comprehensive testing and MCP protocol compliance validation
6. **Performance Optimization**: Optimize response times and connection handling
7. **Documentation & Completion**: Final documentation and deployment preparation

### Ready for Autonomous Implementation

```bash
# Execute this module implementation:
/complex-7-implement-module PRPs/modules/6-module-prp-F3-external-mcp-interface.md
```

---

**Module PRP Status**: ✅ Complete
**Implementation Readiness**: High confidence for autonomous development
**Quality Gate**: Ready for `/complex-7-implement-module` command
**Success Probability**: 85% single-pass implementation success

This F3-external-mcp-interface module PRP provides comprehensive implementation guidance for creating the external MCP interface that bridges Claude Desktop, Claude Code, and custom agents with the Intelligent Memory Orchestrator's Memory Agent through direct integration patterns and full MCP protocol compliance.