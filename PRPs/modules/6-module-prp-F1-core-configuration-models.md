# Module Implementation PRP: F1-core-configuration-models

## Executive Summary
- **Module Purpose**: Configuration management and shared data models for the entire Intelligent Memory Orchestrator system
- **Project Context**: Foundation module for two-agent Pydantic AI system with hybrid architecture (external MCP + internal direct Graphiti)
- **Implementation Scope**: Environment-based configuration, shared Pydantic models, configuration factories, and custom exceptions
- **Success Criteria**: All system modules successfully consume F1 configuration with <50ms loading time and 95%+ test coverage

## Module Context & Requirements

### Module Specification Summary
- **Module Type**: Foundation Phase 1
- **Complexity Score**: 3/10 - Low complexity foundation module with clear requirements
- **Project Role**: Provides configuration infrastructure for all other modules (F2, F3, C1, C2, I1, I2)
- **Technology Focus**: Pydantic v2, environment variables, configuration factory pattern

### Project Integration Context
- **Project Architecture**: Supports hybrid architecture with external MCP interface and internal direct Graphiti operations
- **Cross-Module Dependencies**: F2 (infrastructure), F3 (MCP interface), C1 (Memory Agent), C2 (Conversation Harvester), I1 (orchestration), I2 (platform integration)
- **Interface Requirements**: Configuration factories, shared models, environment settings, exception definitions
- **Project Standards**: 500-line file limit, 50-line function limit, configuration factory pattern (CRITICAL)

### Module-Specific Requirements
- **Functional Requirements**: Environment-based configuration with validation, shared Pydantic models, configuration factories with environment authority
- **Performance Requirements**: <50ms configuration loading, <10ms model validation, <5ms factory creation, <10MB memory usage
- **Security Requirements**: Secure environment variable handling, input validation, no hardcoded secrets
- **Operational Requirements**: Comprehensive logging, error handling, graceful degradation

## Implementation Blueprint

### Development Phases
**Phase 1: Foundation Setup (Days 1-2)**
- Environment variable catalog definition with all system requirements
- Basic Pydantic model structure setup for shared data types
- Configuration factory pattern implementation framework
- Development environment and UV package setup

**Phase 2: Core Implementation (Days 3-4)**
- Complete settings management with environment variable validation
- Shared Pydantic models for all system components (database, agents, monitoring)
- Configuration factory implementations with environment authority
- Custom exception definitions for system-wide error handling

**Phase 3: Integration & Validation (Days 5-6)**
- Cross-module integration testing with F2, F3, C1, C2, I1, I2
- Performance validation and optimization (<50ms loading target)
- Comprehensive testing suite with 95%+ coverage
- Documentation and API reference completion

### Code Organization Strategy
```
src/config/
├── __init__.py              # Module exports (50 lines)
├── settings.py              # Environment settings (200 lines)
├── models.py                # Shared Pydantic models (200 lines)
├── factories.py             # Configuration factories (150 lines)
├── exceptions.py            # Custom exceptions (100 lines)
└── tests/
    ├── test_settings.py     # Settings validation tests (200 lines)
    ├── test_models.py       # Model validation tests (200 lines)
    ├── test_factories.py    # Factory pattern tests (150 lines)
    └── test_exceptions.py   # Exception handling tests (100 lines)
```

### Technology Implementation Approach
- **Framework Integration**: Pydantic v2 with BaseSettings for environment integration
- **Library Dependencies**: python-dotenv for environment loading, UV for package management
- **Data Management**: Immutable configuration models with validation
- **Communication Patterns**: Configuration factory pattern with dependency injection

### Interface Implementation
- **Module APIs**: Configuration factories for all system components
- **Data Contracts**: Shared Pydantic models with complete type safety
- **Error Handling**: Custom exceptions with meaningful error messages
- **Integration Points**: Clean interfaces for all consuming modules

## Comprehensive Testing Strategy

### Unit Testing Approach
- **Test Framework**: pytest with comprehensive fixtures and async support
- **Test Coverage**: 95%+ coverage for all configuration components
- **Test Organization**: Co-located tests in src/config/tests/ directory
- **Mock Strategies**: Environment variable mocking, configuration override testing

### Integration Testing Strategy
- **Cross-Module Testing**: Validation of F1 consumption by F2, F3, C1, C2, I1, I2
- **External Service Testing**: Environment variable validation and loading
- **Interface Testing**: Configuration factory API contracts and data flows
- **Performance Testing**: Configuration loading performance validation

### Module Validation Framework
- **Acceptance Criteria**: All consuming modules successfully use F1 configuration
- **Quality Gates**: 95%+ test coverage, performance targets met, type safety validated
- **Automated Validation**: Pre-commit hooks, CI/CD pipeline integration
- **Manual Testing**: Configuration scenarios and error handling validation

## Quality Assurance Framework

### Code Quality Standards
- **Development Standards**: Follow development_standards.md requirements:
  - File length: 500 lines maximum per file
  - Function length: 50 lines maximum per function
  - Configuration factory pattern: REQUIRED for environment variable respect
  - Type safety: Complete type hints for all functions
- **Documentation Requirements**: Google-style docstrings for all public interfaces
- **Code Review Process**: Configuration factory pattern validation, type safety review
- **Quality Metrics**: MyPy strict mode, Ruff linting, comprehensive testing

### Error Handling & Edge Cases
- **Error Scenarios**: Invalid environment variables, missing configuration, validation failures
- **Edge Case Management**: Empty configuration, malformed environment variables, permission issues
- **Failure Recovery**: Graceful degradation, meaningful error messages, fallback mechanisms
- **Monitoring & Alerting**: Configuration loading monitoring, error tracking, performance alerts

### Performance & Optimization
- **Performance Targets**: Configuration loading <50ms, model validation <10ms, factory creation <5ms
- **Optimization Strategies**: LRU caching for configuration objects, lazy loading patterns
- **Resource Management**: Memory usage optimization, connection pooling for database config
- **Scalability Considerations**: Configuration caching, efficient model validation

## Development Environment & Tools

### Local Development Setup
- **Environment Requirements**: Python 3.12+, UV package manager, development .env file
- **Configuration Management**: Environment variable template, validation on startup
- **Development Tools**: pytest, ruff, mypy, coverage reporting
- **Database/Service Setup**: Local Neo4j and Redis for configuration validation

### Build & Deployment Pipeline
- **Build Process**: UV sync, dependency validation, configuration validation
- **Testing Automation**: pytest with coverage reporting, type checking with MyPy
- **Deployment Strategy**: Environment variable validation, configuration deployment
- **Monitoring Integration**: Logfire integration for configuration monitoring

## Implementation Validation & Success Criteria

### Module Completion Criteria
- [ ] **Core Functionality**: Environment-based configuration with validation functional
- [ ] **Development Standards Compliance**: All development_standards.md requirements met:
  - [ ] File length limits enforced (500 lines max)
  - [ ] Function length limits enforced (50 lines max)
  - [ ] Configuration factory pattern implemented (REQUIRED)
  - [ ] Type safety validated with comprehensive type hints
- [ ] **Shared Models**: All system components have required Pydantic models
- [ ] **Integration Testing**: All consuming modules (F2, F3, C1, C2, I1, I2) successfully use F1
- [ ] **Performance Validation**: All performance targets met (<50ms loading, <10ms validation, <5ms factory creation)
- [ ] **Quality Standards**: 95%+ test coverage, MyPy strict mode, Ruff linting passed
- [ ] **Error Handling**: Comprehensive error handling with meaningful messages

### Project Integration Validation
- [ ] **Architectural Compliance**: F1 aligns with project's hybrid architecture
- [ ] **Standards Compliance**: F1 follows project coding and quality standards
- [ ] **Interface Compatibility**: F1 interfaces work correctly with all consuming modules
- [ ] **Performance Integration**: F1 configuration loading meets system performance targets

### Autonomous Implementation Confidence
- **Implementation Readiness Score**: 8.5/10
- **Single-Pass Success Probability**: 85%
- **Risk Assessment**: Low risk factors with comprehensive requirements and clear implementation guidance
- **Support Requirements**: No additional support needed - complete implementation guidance provided

## Detailed Implementation Guide

### Environment Variable Catalog
```python
# Required environment variables for all system components
REQUIRED_ENV_VARS = [
    # Database Configuration
    "NEO4J_URI",
    "NEO4J_USER", 
    "NEO4J_PASSWORD",
    
    # Redis Configuration
    "REDIS_URL",
    "REDIS_PASSWORD",  # Optional
    
    # AI Agent Configuration
    "CLAUDE_API_KEY",
    "MEMORY_CONFIDENCE_THRESHOLD",
    "MEMORY_STORAGE_PRIORITY",
    
    # Monitoring Configuration
    "LOGFIRE_TOKEN",  # Optional
    
    # Application Configuration
    "APP_ENV",
    "DEBUG_MODE",
    "LOG_LEVEL"
]
```

### Configuration Factory Pattern Implementation
```python
# src/config/factories.py
from pydantic import BaseModel, Field
from typing import Protocol, Optional
from .settings import AppSettings

class ConfigProtocol(Protocol):
    NEO4J_URI: str
    NEO4J_USER: str
    NEO4J_PASSWORD: str
    REDIS_URL: str
    REDIS_PASSWORD: Optional[str]
    CLAUDE_API_KEY: str
    MEMORY_CONFIDENCE_THRESHOLD: float
    LOGFIRE_TOKEN: Optional[str]

class DatabaseConfig(BaseModel):
    """Database configuration model without hardcoded defaults."""
    uri: str = Field(description="Neo4j database URI")
    user: str = Field(description="Database username")
    password: str = Field(description="Database password")
    
    class Config:
        frozen = True

class DatabaseConfigFactory:
    """Factory for creating database configuration from environment."""
    
    def __init__(self, config: ConfigProtocol):
        self.config = config
    
    def create_database_config(self, **overrides) -> DatabaseConfig:
        """Create database config with environment defaults and optional overrides."""
        return DatabaseConfig(
            uri=overrides.get('uri', self.config.NEO4J_URI),
            user=overrides.get('user', self.config.NEO4J_USER),
            password=overrides.get('password', self.config.NEO4J_PASSWORD),
            **{k: v for k, v in overrides.items() 
               if k not in ['uri', 'user', 'password']}
        )

class AgentConfig(BaseModel):
    """AI agent configuration model."""
    api_key: str = Field(description="Claude API key")
    model: str = Field(description="AI model identifier")
    confidence_threshold: float = Field(description="Memory confidence threshold")
    
    class Config:
        frozen = True

class AgentConfigFactory:
    """Factory for creating agent configuration from environment."""
    
    def __init__(self, config: ConfigProtocol):
        self.config = config
    
    def create_agent_config(self, **overrides) -> AgentConfig:
        """Create agent config with environment defaults and optional overrides."""
        return AgentConfig(
            api_key=overrides.get('api_key', self.config.CLAUDE_API_KEY),
            model=overrides.get('model', 'claude-3-sonnet-20240229'),
            confidence_threshold=overrides.get('confidence_threshold', 
                                             self.config.MEMORY_CONFIDENCE_THRESHOLD),
            **{k: v for k, v in overrides.items() 
               if k not in ['api_key', 'model', 'confidence_threshold']}
        )
```

### Settings Implementation
```python
# src/config/settings.py
from pydantic_settings import BaseSettings
from typing import Optional
from functools import lru_cache

class AppSettings(BaseSettings):
    """Application settings with environment variable validation."""
    
    # Database Configuration
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # AI Agent Configuration
    CLAUDE_API_KEY: str
    MEMORY_CONFIDENCE_THRESHOLD: float = 0.7
    MEMORY_STORAGE_PRIORITY: str = "medium"
    
    # Monitoring Configuration
    LOGFIRE_TOKEN: Optional[str] = None
    
    # Application Configuration
    APP_ENV: str = "development"
    DEBUG_MODE: bool = False
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

@lru_cache()
def get_settings() -> AppSettings:
    """Get cached settings instance."""
    return AppSettings()
```

### Shared Models Implementation
```python
# src/config/models.py
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, Dict, Any, List, Literal
from uuid import UUID

class ConversationContext(BaseModel):
    """Conversation context for memory operations."""
    platform: Literal["claude_desktop", "claude_code", "custom_agent"] = Field(
        description="Source platform"
    )
    user_id: str = Field(description="User identifier")
    session_id: str = Field(description="Session identifier")
    timestamp: datetime = Field(description="Conversation timestamp")
    content: str = Field(description="Conversation content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        frozen = True

class MemoryDecision(BaseModel):
    """Memory storage/retrieval decision."""
    should_store: bool = Field(description="Whether to store the information")
    confidence: float = Field(ge=0.0, le=1.0, description="Decision confidence")
    reasoning: str = Field(description="Decision reasoning")
    storage_priority: Literal["high", "medium", "low"] = Field(description="Storage priority")
    
    class Config:
        frozen = True

class MemoryQuery(BaseModel):
    """Memory query specification."""
    query_type: Literal["preference", "fact", "relationship"] = Field(
        description="Type of memory query"
    )
    search_terms: List[str] = Field(description="Search terms")
    temporal_filter: Optional[Dict[str, Any]] = Field(
        default=None, description="Temporal filtering parameters"
    )
    confidence_threshold: float = Field(
        default=0.7, ge=0.0, le=1.0, description="Confidence threshold"
    )
    max_results: int = Field(default=5, ge=1, le=100, description="Maximum results")
    
    class Config:
        frozen = True

class HealthStatus(BaseModel):
    """System health status."""
    component: str = Field(description="Component name")
    status: Literal["healthy", "degraded", "unhealthy"] = Field(description="Health status")
    message: str = Field(description="Status message")
    timestamp: datetime = Field(description="Status timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        frozen = True
```

### Custom Exceptions Implementation
```python
# src/config/exceptions.py
class ConfigurationError(Exception):
    """Base exception for configuration-related errors."""
    pass

class EnvironmentVariableError(ConfigurationError):
    """Raised when required environment variables are missing or invalid."""
    
    def __init__(self, variable_name: str, message: str = None):
        self.variable_name = variable_name
        self.message = message or f"Environment variable '{variable_name}' is required but not set"
        super().__init__(self.message)

class ValidationError(ConfigurationError):
    """Raised when configuration validation fails."""
    
    def __init__(self, field_name: str, value: Any, message: str = None):
        self.field_name = field_name
        self.value = value
        self.message = message or f"Validation failed for field '{field_name}' with value '{value}'"
        super().__init__(self.message)

class ConfigurationFactoryError(ConfigurationError):
    """Raised when configuration factory operations fail."""
    
    def __init__(self, factory_name: str, message: str = None):
        self.factory_name = factory_name
        self.message = message or f"Configuration factory '{factory_name}' operation failed"
        super().__init__(self.message)

class PerformanceError(ConfigurationError):
    """Raised when configuration operations exceed performance thresholds."""
    
    def __init__(self, operation: str, duration: float, threshold: float):
        self.operation = operation
        self.duration = duration
        self.threshold = threshold
        self.message = f"Operation '{operation}' took {duration:.3f}s, exceeding threshold of {threshold:.3f}s"
        super().__init__(self.message)
```

### Testing Implementation Examples
```python
# src/config/tests/test_factories.py
import pytest
from unittest.mock import Mock, patch
from src.config.factories import DatabaseConfigFactory, AgentConfigFactory
from src.config.settings import AppSettings
from src.config.exceptions import ConfigurationFactoryError

class TestDatabaseConfigFactory:
    """Test suite for database configuration factory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.NEO4J_URI = "bolt://localhost:7687"
        settings.NEO4J_USER = "neo4j"
        settings.NEO4J_PASSWORD = "password123"
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """Database configuration factory instance."""
        return DatabaseConfigFactory(mock_settings)
    
    def test_create_database_config_with_defaults(self, factory):
        """Test database config creation with environment defaults."""
        config = factory.create_database_config()
        
        assert config.uri == "bolt://localhost:7687"
        assert config.user == "neo4j"
        assert config.password == "password123"
    
    def test_create_database_config_with_overrides(self, factory):
        """Test database config creation with parameter overrides."""
        config = factory.create_database_config(
            uri="bolt://production:7687",
            user="prod_user"
        )
        
        assert config.uri == "bolt://production:7687"
        assert config.user == "prod_user"
        assert config.password == "password123"  # From environment
    
    def test_configuration_immutability(self, factory):
        """Test that configuration objects are immutable."""
        config = factory.create_database_config()
        
        with pytest.raises(AttributeError):
            config.uri = "bolt://hacked:7687"
    
    @pytest.mark.performance
    def test_configuration_creation_performance(self, factory):
        """Test configuration creation performance."""
        import time
        
        start_time = time.time()
        config = factory.create_database_config()
        duration = time.time() - start_time
        
        assert duration < 0.005  # <5ms requirement
        assert config is not None

class TestAgentConfigFactory:
    """Test suite for agent configuration factory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.CLAUDE_API_KEY = "test-api-key"
        settings.MEMORY_CONFIDENCE_THRESHOLD = 0.7
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """Agent configuration factory instance."""
        return AgentConfigFactory(mock_settings)
    
    def test_create_agent_config_with_defaults(self, factory):
        """Test agent config creation with environment defaults."""
        config = factory.create_agent_config()
        
        assert config.api_key == "test-api-key"
        assert config.model == "claude-3-sonnet-20240229"
        assert config.confidence_threshold == 0.7
    
    def test_create_agent_config_with_overrides(self, factory):
        """Test agent config creation with parameter overrides."""
        config = factory.create_agent_config(
            model="claude-3-haiku-20240307",
            confidence_threshold=0.8
        )
        
        assert config.api_key == "test-api-key"  # From environment
        assert config.model == "claude-3-haiku-20240307"
        assert config.confidence_threshold == 0.8
    
    def test_configuration_validation(self, factory):
        """Test configuration validation."""
        with pytest.raises(ValueError):
            factory.create_agent_config(confidence_threshold=1.5)  # Invalid range
        
        with pytest.raises(ValueError):
            factory.create_agent_config(api_key="")  # Empty string
```

## Next Steps

### Implementation Workflow
1. **Environment Setup**: Configure development environment with Python 3.12+, UV, and required dependencies
2. **Foundation Development**: Implement settings, models, and factory pattern structure
3. **Core Implementation**: Complete all configuration components with validation
4. **Testing & Validation**: Comprehensive testing suite with 95%+ coverage
5. **Integration Testing**: Cross-module integration testing with F2, F3, C1, C2, I1, I2
6. **Documentation & Completion**: Final documentation and module completion validation

### Ready for Autonomous Implementation
```bash
# Execute F1 module implementation:
cd /Users/<USER>/Desktop/Codebase/ActiveAgents/memory_orchestrator
uv sync
uv run pytest src/config/tests/ --cov=src/config --cov-report=html
```

---
**Module PRP Status**: ✅ Complete
**Implementation Readiness**: High confidence for autonomous development
**Quality Gate**: Ready for autonomous implementation
**Success Probability**: 85% single-pass implementation success