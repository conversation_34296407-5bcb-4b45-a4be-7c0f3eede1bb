# Module Specification: Agent Orchestration (I1)

## Module Overview
- **Purpose**: Multi-agent coordination and dual architecture integration orchestration
- **Complexity Score**: 6/10 - Medium complexity integration module
- **Phase Assignment**: Integration Phase 3
- **Dependencies**: Core modules C1 (Memory Agent with Direct Graphiti), C2 (Conversation Harvester), F3 (External MCP Interface)

## Technical Implementation

### Technology Stack
- **FastAPI**: Async API layer for agent communication and orchestration
- **asyncio**: Concurrent processing for responsive multi-agent coordination
- **Pydantic**: Data validation and serialization for agent communication
- **Circuit Breaker**: Error handling and resilience patterns
- **Event-Driven**: Async event processing for agent coordination

### Key Components
- **Agent Coordinator**: Central coordination of all AI agents with dual architecture support
- **System Orchestrator**: Overall system integration and workflow management
- **Dual Architecture Manager**: Coordination between external MCP interface and internal direct operations
- **Communication Hub**: Standardized communication between agents
- **Performance Monitor**: Real-time performance tracking and optimization
- **Error Handler**: Comprehensive error handling with graceful degradation

### Architecture Patterns
- **Dual Architecture Orchestration**: Coordinate both external MCP interface and internal direct operations
- **Event-Driven Architecture**: Async event processing for agent communication
- **Circuit Breaker Pattern**: Resilience and error handling for agent failures
- **Performance Monitoring**: Real-time tracking and optimization
- **Hybrid Integration**: Seamless coordination between external interface and internal operations

### AI Agent Requirements
- **No Direct AI Agents**: This module orchestrates existing agents
- **Agent Management**: Lifecycle management for Memory Agent and Conversation Harvester
- **Performance Optimization**: Efficient agent utilization and resource management
- **Error Handling**: Robust error handling for agent failures and recovery

### Configuration Patterns
```python
# ✅ REQUIRED: Orchestration configuration with factory pattern
@dataclass
class OrchestrationConfig:
    max_concurrent_operations: int = 10
    agent_timeout: float = 30.0
    retry_attempts: int = 3
    circuit_breaker_threshold: int = 5
    performance_monitoring: bool = True
    
    @classmethod
    def from_settings(cls, settings: AppSettings) -> 'OrchestrationConfig':
        return cls(
            max_concurrent_operations=settings.MAX_CONCURRENT_OPERATIONS,
            agent_timeout=settings.AGENT_TIMEOUT,
            retry_attempts=settings.RETRY_ATTEMPTS,
            circuit_breaker_threshold=settings.CIRCUIT_BREAKER_THRESHOLD,
            performance_monitoring=settings.PERFORMANCE_MONITORING
        )
```

### Interface Definitions
- **Orchestration API**: Agent coordination and workflow management
- **Communication API**: Standardized inter-agent communication
- **Performance API**: Real-time performance monitoring and optimization
- **Error API**: Comprehensive error handling and recovery

## Development Guidelines

### Implementation Approach
1. **Agent Coordinator**: Implement central coordination for all AI agents
2. **System Orchestrator**: Implement overall system integration workflows
3. **Communication Hub**: Implement standardized agent communication
4. **Performance Monitor**: Implement real-time performance tracking
5. **Error Handler**: Implement comprehensive error handling and recovery

### Code Organization
```
src/orchestration/
├── __init__.py              # Module exports
├── agent_coordinator.py     # Multi-agent coordination
├── system_orchestrator.py   # System integration
├── dual_architecture_manager.py # Dual architecture coordination
├── communication_hub.py     # Agent communication
├── performance_monitor.py   # Performance tracking
├── error_handler.py         # Error handling and recovery
└── tests/
    ├── test_agent_coordinator.py    # Agent coordination tests
    ├── test_system_orchestrator.py  # System integration tests
    ├── test_dual_architecture_manager.py # Dual architecture tests
    ├── test_communication_hub.py    # Communication tests
    ├── test_performance_monitor.py  # Performance tests
    └── test_error_handler.py        # Error handling tests
```

### Testing Requirements
- **Orchestration Testing**: Agent coordination and workflow validation
- **Communication Testing**: Inter-agent communication and data flow
- **Performance Testing**: Response time validation and load testing
- **Error Testing**: Error handling and recovery scenario validation
- **Integration Testing**: End-to-end system integration validation

### Quality Standards
- **File Length**: <400 lines per file (under 500-line limit)
- **Function Length**: <40 lines per function (under 50-line limit)
- **Type Safety**: Complete type hints for all async operations
- **Documentation**: Google-style docstrings for all orchestration interfaces
- **Error Handling**: Comprehensive error handling with retry mechanisms

## Success Criteria

### Functional Requirements
- **Agent Coordination**: Successful coordination of Memory Agent and Conversation Harvester
- **System Integration**: Seamless integration of all system components
- **Performance Optimization**: Efficient resource utilization and response times
- **Error Recovery**: Graceful handling of agent failures and system errors
- **Workflow Management**: Smooth execution of complex multi-agent workflows

### Performance Targets
- **Orchestration Overhead**: <50ms for agent coordination operations
- **System Response Time**: Maintain 500ms-2s overall system response time
- **Concurrent Operations**: Support 10+ concurrent agent operations
- **Error Recovery**: <5s for error recovery and system stabilization
- **Resource Utilization**: <80% CPU and memory usage under normal load

### Quality Gates
- **Test Coverage**: 85%+ test coverage for all orchestration components
- **Performance Validation**: All response time targets met under load
- **Error Handling**: 100% error recovery success rate
- **Agent Coordination**: 99%+ successful agent coordination rate
- **System Integration**: End-to-end workflows execute successfully

### Integration Validation
- **Agent Integration**: All agents respond correctly to orchestration commands
- **System Workflows**: Complex workflows execute successfully end-to-end
- **Platform Integration**: Seamless operation across all target platforms
- **Database Operations**: Efficient database access through infrastructure layer

## Implementation Context

### From PRD Research
- **Multi-Agent Coordination**: Coordinate Memory Agent and Conversation Harvester
- **System Integration**: Integrate all components into cohesive system
- **Performance Requirements**: Maintain 500ms-2s response time targets
- **Reliability Requirements**: 99.5% uptime with graceful degradation

### Technology-Specific Implementation
- **FastAPI Integration**: Async API endpoints for agent orchestration
- **asyncio Patterns**: Concurrent processing for responsive coordination
- **Circuit Breaker**: Resilience patterns for agent failure handling
- **Event-Driven**: Async event processing for efficient coordination

### Development Standards Integration
- **Async Operations**: Proper async/await patterns for all orchestration
- **Error Handling**: Structured error handling with meaningful error messages
- **Performance Monitoring**: Continuous performance tracking and optimization
- **Resource Management**: Efficient resource utilization and cleanup

## Orchestration Components

### Agent Coordinator
```python
class AgentCoordinator:
    """Central coordinator for all AI agents."""
    
    def __init__(self, config: OrchestrationConfig, deps: OrchestrationDeps):
        self.config = config
        self.deps = deps
        self.memory_agent = deps.memory_agent
        self.conversation_harvester = deps.conversation_harvester
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_threshold)
        self.performance_monitor = PerformanceMonitor()
    
    async def coordinate_memory_operation(
        self,
        operation: MemoryOperation,
        context: ConversationContext
    ) -> MemoryOperationResult:
        """Coordinate memory operation across agents."""
        
        with self.performance_monitor.track_operation("memory_coordination"):
            # Evaluate memory relevance using Memory Agent
            relevance_result = await self.circuit_breaker.call(
                self.memory_agent.evaluate_memory_relevance,
                context,
                operation.content,
                timeout=self.config.agent_timeout
            )
            
            if relevance_result.should_store:
                # Store directly via Memory Agent (no separate graphiti_client)
                storage_result = await self.circuit_breaker.call(
                    self.memory_agent.store_memory_episode,
                    context,
                    operation.content,
                    relevance_result.storage_priority,
                    timeout=self.config.agent_timeout
                )
                
                # Notify conversation harvester for batch processing
                await self.conversation_harvester.notify_storage(
                    operation.content,
                    context,
                    storage_result
                )
                
                return MemoryOperationResult(
                    success=True,
                    stored=True,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning
                )
            else:
                return MemoryOperationResult(
                    success=True,
                    stored=False,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning
                )
    
    async def coordinate_memory_retrieval(
        self,
        query: MemoryQuery,
        context: ConversationContext
    ) -> MemoryRetrievalResult:
        """Coordinate memory retrieval across agents."""
        
        with self.performance_monitor.track_operation("memory_retrieval"):
            # Structure optimal query using Memory Agent
            structured_query = await self.circuit_breaker.call(
                self.memory_agent.structure_memory_query,
                context,
                query.intent,
                timeout=self.config.agent_timeout
            )
            
            # Retrieve directly via Memory Agent
            retrieved_items = await self.circuit_breaker.call(
                self.memory_agent.search_memory_graph,
                structured_query.search_terms,
                structured_query.max_results,
                timeout=self.config.agent_timeout
            )
            
            # Assess context applicability
            applicability_result = await self.circuit_breaker.call(
                self.memory_agent.assess_context_applicability,
                retrieved_items,
                context,
                timeout=self.config.agent_timeout
            )
            
            return MemoryRetrievalResult(
                items=applicability_result.applicable_items,
                confidence=applicability_result.confidence,
                reasoning=applicability_result.reasoning
            )
    
    async def health_check(self) -> OrchestrationHealthResult:
        """Check health of all coordinated agents."""
        
        health_results = {}
        
        # Check Memory Agent health
        try:
            memory_health = await asyncio.wait_for(
                self.memory_agent.health_check(),
                timeout=5.0
            )
            health_results['memory_agent'] = memory_health
        except Exception as e:
            health_results['memory_agent'] = False
            logger.error(f"Memory Agent health check failed: {e}")
        
        # Check Conversation Harvester health
        try:
            harvester_health = await asyncio.wait_for(
                self.conversation_harvester.health_check(),
                timeout=5.0
            )
            health_results['conversation_harvester'] = harvester_health
        except Exception as e:
            health_results['conversation_harvester'] = False
            logger.error(f"Conversation Harvester health check failed: {e}")
        
        overall_health = all(health_results.values())
        
        return OrchestrationHealthResult(
            overall_health=overall_health,
            agent_health=health_results,
            circuit_breaker_status=self.circuit_breaker.state,
            performance_metrics=self.performance_monitor.get_metrics()
        )
```

### System Orchestrator
```python
class SystemOrchestrator:
    """Overall system integration and workflow management."""
    
    def __init__(self, config: OrchestrationConfig, deps: OrchestrationDeps):
        self.config = config
        self.deps = deps
        self.agent_coordinator = AgentCoordinator(config, deps)
        self.workflow_queue = asyncio.Queue()
        self.active_workflows = {}
    
    async def process_conversation_workflow(
        self,
        conversation: ConversationContext,
        platforms: List[str]
    ) -> ConversationWorkflowResult:
        """Process complete conversation workflow across platforms."""
        
        workflow_id = generate_workflow_id()
        
        try:
            # Track workflow
            self.active_workflows[workflow_id] = {
                'started_at': datetime.now(),
                'conversation': conversation,
                'platforms': platforms,
                'status': 'processing'
            }
            
            # Process memory operations
            memory_results = []
            for platform in platforms:
                platform_context = adapt_context_for_platform(conversation, platform)
                
                # Coordinate memory operation
                memory_result = await self.agent_coordinator.coordinate_memory_operation(
                    MemoryOperation(
                        content=conversation.content,
                        operation_type='evaluate_and_store'
                    ),
                    platform_context
                )
                memory_results.append(memory_result)
            
            # Update workflow status
            self.active_workflows[workflow_id]['status'] = 'completed'
            self.active_workflows[workflow_id]['completed_at'] = datetime.now()
            
            return ConversationWorkflowResult(
                workflow_id=workflow_id,
                success=True,
                memory_results=memory_results,
                processing_time=calculate_processing_time(workflow_id)
            )
            
        except Exception as e:
            # Handle workflow error
            self.active_workflows[workflow_id]['status'] = 'failed'
            self.active_workflows[workflow_id]['error'] = str(e)
            
            logger.error(f"Conversation workflow {workflow_id} failed: {e}")
            
            return ConversationWorkflowResult(
                workflow_id=workflow_id,
                success=False,
                error=str(e),
                processing_time=calculate_processing_time(workflow_id)
            )
        
        finally:
            # Cleanup completed workflow
            await self.cleanup_workflow(workflow_id)
    
    async def process_batch_workflow(
        self,
        conversations: List[ConversationContext]
    ) -> BatchWorkflowResult:
        """Process batch conversation workflow for optimization."""
        
        batch_id = generate_batch_id()
        
        try:
            # Process conversations in parallel with concurrency limit
            semaphore = asyncio.Semaphore(self.config.max_concurrent_operations)
            
            async def process_single_conversation(conversation):
                async with semaphore:
                    return await self.process_conversation_workflow(
                        conversation,
                        ['claude_desktop', 'claude_code']
                    )
            
            # Execute batch processing
            results = await asyncio.gather(
                *[process_single_conversation(conv) for conv in conversations],
                return_exceptions=True
            )
            
            # Analyze batch results
            successful_results = [r for r in results if isinstance(r, ConversationWorkflowResult) and r.success]
            failed_results = [r for r in results if isinstance(r, Exception) or not r.success]
            
            return BatchWorkflowResult(
                batch_id=batch_id,
                total_conversations=len(conversations),
                successful_count=len(successful_results),
                failed_count=len(failed_results),
                results=successful_results,
                errors=failed_results
            )
            
        except Exception as e:
            logger.error(f"Batch workflow {batch_id} failed: {e}")
            
            return BatchWorkflowResult(
                batch_id=batch_id,
                total_conversations=len(conversations),
                successful_count=0,
                failed_count=len(conversations),
                error=str(e)
            )
    
    async def start_background_processing(self):
        """Start background processing for continuous workflows."""
        
        while True:
            try:
                # Process workflow queue
                workflow = await asyncio.wait_for(
                    self.workflow_queue.get(),
                    timeout=1.0
                )
                
                # Process workflow based on type
                if workflow['type'] == 'conversation':
                    await self.process_conversation_workflow(
                        workflow['conversation'],
                        workflow['platforms']
                    )
                elif workflow['type'] == 'batch':
                    await self.process_batch_workflow(
                        workflow['conversations']
                    )
                
            except asyncio.TimeoutError:
                # No workflows in queue, continue
                continue
            except Exception as e:
                logger.error(f"Background processing error: {e}")
```

### Dual Architecture Manager
```python
class DualArchitectureManager:
    """Coordinate between external MCP interface and internal direct operations."""
    
    def __init__(self, config: OrchestrationConfig, deps: OrchestrationDeps):
        self.config = config
        self.deps = deps
        self.memory_agent = deps.memory_agent
        self.mcp_interface = deps.mcp_interface
        self.performance_monitor = PerformanceMonitor()
    
    async def route_memory_operation(
        self,
        operation: MemoryOperation,
        context: ConversationContext,
        source: Literal["external", "internal"]
    ) -> MemoryOperationResult:
        """Route memory operation based on source (external MCP vs internal direct)."""
        
        with self.performance_monitor.track_operation(f"memory_routing_{source}"):
            if source == "external":
                # External request via MCP interface
                return await self.handle_external_memory_operation(operation, context)
            else:
                # Internal direct operation
                return await self.handle_internal_memory_operation(operation, context)
    
    async def handle_external_memory_operation(
        self,
        operation: MemoryOperation,
        context: ConversationContext
    ) -> MemoryOperationResult:
        """Handle memory operation from external MCP interface."""
        
        # Translate MCP request to internal Memory Agent operation
        if operation.operation_type == "evaluate_and_store":
            # Direct Memory Agent evaluation (no MCP overhead)
            relevance_result = await self.memory_agent.evaluate_memory_relevance(
                context,
                operation.content
            )
            
            if relevance_result.should_store:
                # Direct Memory Agent storage
                storage_success = await self.memory_agent.store_memory_episode(
                    context,
                    operation.content,
                    relevance_result.storage_priority
                )
                
                return MemoryOperationResult(
                    success=storage_success,
                    stored=storage_success,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning,
                    processing_time=self.performance_monitor.get_last_operation_time()
                )
            else:
                return MemoryOperationResult(
                    success=True,
                    stored=False,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning,
                    processing_time=self.performance_monitor.get_last_operation_time()
                )
        
        elif operation.operation_type == "retrieve":
            # Direct Memory Agent retrieval
            structured_query = await self.memory_agent.structure_memory_query(
                context,
                operation.content
            )
            
            retrieved_items = await self.memory_agent.search_memory_graph(
                structured_query.search_terms,
                structured_query.max_results
            )
            
            applicability_result = await self.memory_agent.assess_context_applicability(
                retrieved_items,
                context
            )
            
            return MemoryOperationResult(
                success=True,
                stored=False,
                confidence=applicability_result.confidence,
                reasoning=applicability_result.reasoning,
                processing_time=self.performance_monitor.get_last_operation_time(),
                retrieved_items=applicability_result.applicable_items
            )
    
    async def handle_internal_memory_operation(
        self,
        operation: MemoryOperation,
        context: ConversationContext
    ) -> MemoryOperationResult:
        """Handle internal direct memory operation (no MCP overhead)."""
        
        # Direct Memory Agent operation with full performance optimization
        if operation.operation_type == "evaluate_and_store":
            relevance_result = await self.memory_agent.evaluate_memory_relevance(
                context,
                operation.content
            )
            
            if relevance_result.should_store:
                storage_success = await self.memory_agent.store_memory_episode(
                    context,
                    operation.content,
                    relevance_result.storage_priority
                )
                
                return MemoryOperationResult(
                    success=storage_success,
                    stored=storage_success,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning,
                    processing_time=self.performance_monitor.get_last_operation_time()
                )
            else:
                return MemoryOperationResult(
                    success=True,
                    stored=False,
                    confidence=relevance_result.confidence,
                    reasoning=relevance_result.reasoning,
                    processing_time=self.performance_monitor.get_last_operation_time()
                )
        
        # Similar implementation for other operation types...
    
    async def get_architecture_metrics(self) -> DualArchitectureMetrics:
        """Get metrics for both external and internal operations."""
        
        external_metrics = await self.mcp_interface.get_performance_metrics()
        internal_metrics = await self.memory_agent.get_performance_metrics()
        
        return DualArchitectureMetrics(
            external_operations=external_metrics.operation_count,
            internal_operations=internal_metrics.operation_count,
            external_avg_response_time=external_metrics.avg_response_time,
            internal_avg_response_time=internal_metrics.avg_response_time,
            architecture_efficiency=internal_metrics.avg_response_time / external_metrics.avg_response_time
        )
```

## Data Models

### Orchestration Models
```python
class MemoryOperation(BaseModel):
    """Memory operation for agent coordination."""
    content: str = Field(description="Content for memory operation")
    operation_type: Literal["evaluate_and_store", "retrieve", "update"] = Field(description="Operation type")
    priority: Literal["high", "medium", "low"] = Field(default="medium", description="Operation priority")

class MemoryOperationResult(BaseModel):
    """Result of coordinated memory operation."""
    success: bool = Field(description="Operation success status")
    stored: bool = Field(description="Whether content was stored")
    confidence: float = Field(ge=0.0, le=1.0, description="Operation confidence")
    reasoning: str = Field(description="Operation reasoning")
    processing_time: float = Field(description="Processing time in seconds")

class ConversationWorkflowResult(BaseModel):
    """Result of conversation workflow processing."""
    workflow_id: str = Field(description="Workflow identifier")
    success: bool = Field(description="Workflow success status")
    memory_results: List[MemoryOperationResult] = Field(description="Memory operation results")
    processing_time: float = Field(description="Total processing time")
    error: Optional[str] = Field(default=None, description="Error message if failed")

class OrchestrationHealthResult(BaseModel):
    """Health check result for orchestration system."""
    overall_health: bool = Field(description="Overall system health")
    agent_health: Dict[str, bool] = Field(description="Individual agent health status")
    circuit_breaker_status: str = Field(description="Circuit breaker state")
    performance_metrics: Dict[str, float] = Field(description="Performance metrics")

class DualArchitectureMetrics(BaseModel):
    """Metrics for dual architecture performance."""
    external_operations: int = Field(description="Number of external MCP operations")
    internal_operations: int = Field(description="Number of internal direct operations")
    external_avg_response_time: float = Field(description="Average response time for external operations")
    internal_avg_response_time: float = Field(description="Average response time for internal operations")
    architecture_efficiency: float = Field(description="Efficiency ratio of internal vs external operations")
```

## Error Handling

### Circuit Breaker Implementation
```python
class CircuitBreaker:
    """Circuit breaker for agent failure handling."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half_open
    
    async def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection."""
        
        if self.state == "open":
            if self.should_attempt_reset():
                self.state = "half_open"
            else:
                raise CircuitBreakerOpenError("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs)
            
            if self.state == "half_open":
                self.reset()
            
            return result
            
        except Exception as e:
            self.record_failure()
            raise e
    
    def record_failure(self):
        """Record failure and update circuit breaker state."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
    
    def reset(self):
        """Reset circuit breaker to closed state."""
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"
        logger.info("Circuit breaker reset to closed state")
    
    def should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time > self.recovery_timeout
```

---

**Module Status**: ✅ Specification Complete
**Implementation Ready**: Yes - All orchestration requirements and dual architecture patterns defined
**Critical Requirements**: Must coordinate agents efficiently with dual architecture support and error handling
**Next Step**: Begin implementation with agent coordinator, system orchestrator, and dual architecture manager
**Integration Points**: Coordinates all system components and agents with hybrid external/internal operations