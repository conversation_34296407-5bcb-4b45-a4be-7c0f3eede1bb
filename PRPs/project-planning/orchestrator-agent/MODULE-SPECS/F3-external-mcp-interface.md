# Module Specification: External MCP Interface (F3)

## Module Overview
- **Purpose**: External MCP interface for cross-platform communication with <PERSON>, <PERSON>, and custom agents
- **Complexity Score**: 4/10 - Standard MCP protocol implementation with external communication focus
- **Phase Assignment**: Foundation Phase 1
- **Dependencies**: External only (MCP libraries, WebSocket/HTTP clients)

## Technical Implementation

### Technology Stack
- **MCP Protocol**: Official MCP specification compliance for cross-platform communication
- **WebSocket/HTTP**: Transport layer for real-time communication
- **FastAPI**: HTTP endpoints for MCP server functionality
- **AsyncIO**: Non-blocking external communication
- **Connection Management**: Connection pooling and health monitoring
- **Error Handling**: Graceful degradation and retry mechanisms

### Key Components
- **MCP Server**: External interface for Claude Desktop and Claude Code
- **Protocol Handler**: MCP tool discovery and command routing
- **Memory Operations Translator**: Direct translation to Memory Agent operations
- **Connection Manager**: External client connection management
- **Error Recovery**: Graceful handling of external communication failures

### Architecture Patterns
- **External Interface Only**: No internal system abstraction - direct Memory Agent communication
- **Protocol Compliance**: Full MCP specification adherence for cross-platform compatibility
- **Direct Translation**: Simple pass-through to Memory Agent without internal database operations
- **Performance Focus**: Minimal overhead translation layer
- **Fault Tolerance**: Robust error handling for external communication

### MCP Interface Requirements
```python
# ✅ REQUIRED: Direct Memory Agent integration (not database abstraction)
from pydantic import BaseModel
from typing import Dict, Any, List
from mcp import Tool, Server

class MCPMemoryInterface:
    """External MCP interface that translates to Memory Agent operations."""
    
    def __init__(self, memory_agent_deps: MemoryAgentDeps):
        self.memory_agent_deps = memory_agent_deps
        self.mcp_server = Server("memory-orchestrator")
        self._register_tools()
    
    def _register_tools(self):
        """Register MCP tools that directly call Memory Agent operations."""
        
        @self.mcp_server.tool("evaluate_memory_storage")
        async def evaluate_memory_storage(
            conversation_context: Dict[str, Any],
            content: str
        ) -> Dict[str, Any]:
            """Evaluate whether content should be stored in memory."""
            # Direct Memory Agent operation - no database abstraction
            result = await memory_agent.run(
                "evaluate_memory_relevance",
                conversation_context=ConversationContext(**conversation_context),
                content=content,
                deps=self.memory_agent_deps
            )
            return result.model_dump()
        
        @self.mcp_server.tool("retrieve_memory_context")
        async def retrieve_memory_context(
            query: str,
            conversation_context: Dict[str, Any]
        ) -> Dict[str, Any]:
            """Retrieve relevant memory context for query."""
            # Direct Memory Agent operation with structured query
            structured_query = await memory_agent.run(
                "structure_memory_query",
                query_context=QueryContext(**conversation_context),
                search_intent=query,
                deps=self.memory_agent_deps
            )
            
            # Direct Graphiti search via Memory Agent
            results = await memory_agent.run(
                "search_memory_graph",
                query=structured_query.search_terms,
                num_results=structured_query.max_results,
                deps=self.memory_agent_deps
            )
            
            return {
                "query": structured_query.model_dump(),
                "results": [result.model_dump() for result in results]
            }
        
        @self.mcp_server.tool("store_memory_episode")
        async def store_memory_episode(
            conversation_context: Dict[str, Any],
            content: str,
            storage_priority: str = "medium"
        ) -> Dict[str, Any]:
            """Store memory episode via Memory Agent."""
            # Direct Memory Agent storage operation
            success = await memory_agent.run(
                "store_memory_episode",
                conversation_context=ConversationContext(**conversation_context),
                content=content,
                storage_priority=storage_priority,
                deps=self.memory_agent_deps
            )
            
            return {"success": success, "message": "Memory episode stored" if success else "Storage failed"}
```

### External Communication Patterns
- **Claude Desktop Integration**: MCP client communication for conversation access
- **Claude Code Integration**: Session enhancement and preference application
- **Custom Agent Support**: Standardized memory operations for Pydantic AI agents
- **Protocol Abstraction**: Hide internal architecture complexity from external clients
- **Error Propagation**: Meaningful error messages for external platforms

### Interface Definitions
- **MCP Tool API**: Standardized tool discovery and execution
- **Memory Operations API**: Direct translation to Memory Agent operations
- **Connection Management API**: External client lifecycle management
- **Error Handling API**: Graceful error responses and recovery mechanisms

## Development Guidelines

### Implementation Approach
1. **MCP Server Setup**: Initialize MCP server with tool registration
2. **Memory Agent Integration**: Direct integration with global Memory Agent instance
3. **Protocol Compliance**: Implement full MCP specification adherence
4. **Error Handling**: Comprehensive error handling with graceful degradation
5. **Performance Optimization**: Minimal overhead translation layer
6. **Testing**: External communication testing with mock clients

### Code Organization
```
src/mcp/
├── __init__.py              # Module exports
├── mcp_server.py            # MCP server implementation
├── memory_interface.py      # Memory Agent translation layer
├── protocol_handler.py      # MCP protocol handling
├── connection_manager.py    # External connection management
├── models.py                # MCP-specific models
└── tests/
    ├── test_mcp_server.py   # MCP server tests
    ├── test_memory_interface.py # Memory Agent integration tests
    ├── test_protocol_handler.py # Protocol compliance tests
    └── test_connection_manager.py # Connection management tests
```

### Testing Requirements
- **MCP Protocol Testing**: Protocol compliance and tool discovery
- **Memory Agent Integration**: Direct Memory Agent operation validation
- **External Communication**: Mock client testing with various platforms
- **Error Handling**: Graceful degradation and recovery testing
- **Performance Testing**: Minimal overhead validation

### Quality Standards
- **File Length**: <300 lines per file (under 500-line limit)
- **Function Length**: <30 lines per function (under 50-line limit)
- **Type Safety**: Complete type hints for all MCP operations
- **Documentation**: Google-style docstrings for all MCP tools
- **Error Handling**: Comprehensive error handling with meaningful messages

## Success Criteria

### Functional Requirements
- **MCP Protocol Compliance**: Full specification adherence for cross-platform compatibility
- **Memory Agent Integration**: Direct translation to Memory Agent operations
- **External Platform Support**: Successful integration with Claude Desktop and Claude Code
- **Error Handling**: Graceful handling of external communication failures
- **Performance**: Minimal overhead translation layer

### Performance Targets
- **MCP Response Time**: <100ms for tool discovery and routing
- **Memory Operation Translation**: <50ms overhead for Memory Agent calls
- **Connection Management**: <200ms for connection establishment
- **Error Recovery**: <500ms for graceful degradation and retry
- **Throughput**: Support 100+ concurrent external connections

### Quality Gates
- **Protocol Compliance**: 100% MCP specification adherence
- **Integration Testing**: Successful communication with all target platforms
- **Error Handling**: Graceful handling of all error conditions
- **Performance Validation**: All response time targets met consistently
- **Connection Stability**: 99.9% connection success rate

### Integration Validation
- **Claude Desktop Integration**: Successful MCP client communication
- **Claude Code Integration**: Session enhancement and preference application
- **Memory Agent Communication**: Direct integration without database abstraction
- **External Error Handling**: Meaningful error responses for external platforms

## Implementation Context

### From PRD Research
- **Cross-Platform Continuity**: Enable seamless memory across Claude Desktop and Code
- **External Interface Focus**: MCP interface for external communication only
- **Performance Requirements**: Minimal overhead translation layer
- **Protocol Compliance**: Full MCP specification adherence

### Technology-Specific Implementation
- **MCP Protocol Integration**: Tool discovery, structured communication, error propagation
- **Memory Agent Integration**: Direct operation translation without abstraction
- **External Communication**: WebSocket/HTTP transport with authentication
- **Error Handling**: Graceful degradation and fallback mechanisms

### Development Standards Integration
- **Foundation Module Pattern**: External dependencies only, no internal system dependencies
- **Direct Memory Agent Integration**: No database abstraction layer
- **Performance Focus**: Minimal overhead translation layer
- **Error Handling**: Comprehensive error handling with meaningful messages

## MCP Tool Implementation

### Memory Storage Tool
```python
@mcp_server.tool("store_memory")
async def store_memory(
    conversation_context: Dict[str, Any],
    content: str,
    storage_priority: str = "medium"
) -> Dict[str, Any]:
    """
    Store memory content via Memory Agent.
    
    Args:
        conversation_context: Context of the conversation
        content: Content to store in memory
        storage_priority: Priority level for storage
        
    Returns:
        Dict containing success status and message
    """
    try:
        # Direct Memory Agent evaluation
        relevance_result = await memory_agent.run(
            "evaluate_memory_relevance",
            conversation_context=ConversationContext(**conversation_context),
            content=content,
            deps=memory_agent_deps
        )
        
        if relevance_result.should_store:
            # Direct Memory Agent storage
            success = await memory_agent.run(
                "store_memory_episode",
                conversation_context=ConversationContext(**conversation_context),
                content=content,
                storage_priority=storage_priority,
                deps=memory_agent_deps
            )
            
            return {
                "success": success,
                "message": "Memory stored successfully" if success else "Storage failed",
                "confidence": relevance_result.confidence,
                "reasoning": relevance_result.reasoning
            }
        else:
            return {
                "success": False,
                "message": "Content not relevant for storage",
                "confidence": relevance_result.confidence,
                "reasoning": relevance_result.reasoning
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Storage error: {str(e)}",
            "error": str(e)
        }
```

### Memory Retrieval Tool
```python
@mcp_server.tool("retrieve_memory")
async def retrieve_memory(
    query: str,
    conversation_context: Dict[str, Any],
    max_results: int = 5
) -> Dict[str, Any]:
    """
    Retrieve relevant memory context for query.
    
    Args:
        query: Search query string
        conversation_context: Context for the memory query
        max_results: Maximum number of results to return
        
    Returns:
        Dict containing query results and metadata
    """
    try:
        # Direct Memory Agent query structuring
        structured_query = await memory_agent.run(
            "structure_memory_query",
            query_context=QueryContext(**conversation_context),
            search_intent=query,
            deps=memory_agent_deps
        )
        
        # Direct Memory Agent search
        results = await memory_agent.run(
            "search_memory_graph",
            query=structured_query.search_terms,
            num_results=min(max_results, structured_query.max_results),
            deps=memory_agent_deps
        )
        
        # Direct Memory Agent applicability assessment
        applicability_result = await memory_agent.run(
            "assess_context_applicability",
            retrieved_memory=results,
            current_context=ConversationContext(**conversation_context),
            deps=memory_agent_deps
        )
        
        return {
            "success": True,
            "query": structured_query.model_dump(),
            "results": [result.model_dump() for result in applicability_result.applicable_items],
            "relevance_scores": applicability_result.relevance_scores,
            "confidence": applicability_result.confidence,
            "reasoning": applicability_result.reasoning
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Retrieval error: {str(e)}",
            "error": str(e)
        }
```

### Memory Management Tool
```python
@mcp_server.tool("manage_memory")
async def manage_memory(
    action: str,
    conversation_context: Dict[str, Any],
    target: str = None
) -> Dict[str, Any]:
    """
    Manage memory operations (delete, update, etc.).
    
    Args:
        action: Management action to perform
        conversation_context: Context for the operation
        target: Target memory item or query
        
    Returns:
        Dict containing operation result
    """
    try:
        if action == "delete":
            # Direct Memory Agent deletion (if implemented)
            # This would require additional Memory Agent tools
            return {
                "success": False,
                "message": "Memory deletion not yet implemented"
            }
        elif action == "update":
            # Direct Memory Agent update (if implemented)
            return {
                "success": False,
                "message": "Memory update not yet implemented"
            }
        else:
            return {
                "success": False,
                "message": f"Unknown action: {action}"
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Management error: {str(e)}",
            "error": str(e)
        }
```

## Performance Optimization

### Connection Management
```python
class MCPConnectionManager:
    """Manage external MCP client connections."""
    
    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.active_connections: Dict[str, MCPConnection] = {}
        self.connection_pool = ConnectionPool(max_connections)
    
    async def handle_connection(self, client_id: str, transport: Transport):
        """Handle new MCP client connection."""
        if len(self.active_connections) >= self.max_connections:
            await transport.close("Connection limit reached")
            return
        
        connection = MCPConnection(client_id, transport)
        self.active_connections[client_id] = connection
        
        try:
            await connection.handle_requests()
        except Exception as e:
            logger.error(f"Connection error for {client_id}: {e}")
        finally:
            self.active_connections.pop(client_id, None)
            await connection.close()
```

### Error Handling Strategy
```python
class MCPErrorHandler:
    """Comprehensive error handling for MCP operations."""
    
    async def handle_memory_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Memory Agent operation errors."""
        if isinstance(error, ValidationError):
            return {
                "success": False,
                "error": "Invalid input data",
                "details": str(error)
            }
        elif isinstance(error, ConnectionError):
            return {
                "success": False,
                "error": "Database connection failed",
                "details": "Please try again later"
            }
        else:
            logger.error(f"Unexpected error in MCP operation: {error}")
            return {
                "success": False,
                "error": "Internal server error",
                "details": "Operation failed"
            }
```

---

**Module Status**: ✅ Specification Complete
**Implementation Ready**: Yes - All MCP interface requirements defined with direct Memory Agent integration
**Critical Requirements**: External interface only, no internal database abstraction
**Next Step**: Begin implementation with MCP server setup and Memory Agent integration
**Integration Points**: Direct Memory Agent operations, external platform communication only