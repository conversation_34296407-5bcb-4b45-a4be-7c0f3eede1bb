# Module Specification: Memory Intelligence Core (C1)

## Module Overview
- **Purpose**: Intelligent decision-making agent for memory operations with relevance assessment and direct Graphiti library integration
- **Complexity Score**: 6/10 - Medium complexity AI agent module with direct library integration
- **Phase Assignment**: Core Logic Phase 2
- **Dependencies**: Foundation modules F1 (Configuration), F2 (Infrastructure)

## Technical Implementation

### Technology Stack
- **Pydantic AI**: Agent framework with module-global reuse pattern (CRITICAL)
- **Graphiti Core Library**: Direct integration for intelligent temporal knowledge graph operations
- **Claude-3-Sonnet**: AI model for intelligent decision-making
- **Structured Results**: Typed agent responses with validation
- **Tool Integration**: Custom tools for memory relevance assessment and direct graph operations
- **Async Operations**: Non-blocking agent operations with performance optimization

### Key Components
- **Memory Agent**: Global agent instance with intelligent decision-making tools and direct Graphiti client
- **Relevance Assessment**: Tools for evaluating memory storage worthiness
- **Direct Graph Operations**: Direct Graphiti library integration for temporal knowledge graph operations
- **Query Structuring**: Tools for optimizing Graphiti search queries
- **Context Evaluation**: Tools for assessing retrieved memory applicability
- **Decision Logic**: Structured decision-making with confidence scoring

### Architecture Patterns
- **Module-Global Agent**: Single agent instance reused across all operations
- **Direct Library Integration**: Direct Graphiti client for high-performance operations
- **Structured Tools**: Typed tools with validation and error handling
- **Decision Framework**: Consistent decision-making with confidence scoring
- **Performance Optimization**: Agent reuse and direct library connection pooling

### AI Agent Requirements (CRITICAL)
```python
# ✅ REQUIRED: Module-global agent instantiation
from pydantic_ai import Agent
from typing import Any, Dict, List

# Global agent instance - instantiate once per module
memory_agent = Agent(
    'claude-3-sonnet-20240229',
    system_prompt="""You are a Memory Intelligence Agent responsible for intelligent 
    memory decisions across multi-agent AI systems.
    
    Your core responsibilities:
    1. Evaluate whether information should be stored in memory
    2. Structure optimal queries for retrieving relevant memory
    3. Assess the applicability of retrieved memory to current context
    4. Make intelligent decisions about memory operations with confidence scoring
    
    Your decision-making should be:
    - Conservative: Only store truly valuable information
    - Contextual: Consider user preferences and conversation context
    - Confident: Provide clear confidence scores for all decisions
    - Efficient: Structure queries for optimal performance
    """,
    deps_type=MemoryAgentDeps,
    result_type=MemoryDecision
)

# ❌ FORBIDDEN: Creating new agents per operation
async def bad_memory_operation():
    agent = Agent(...)  # Performance killer
    return await agent.run(query)
```

### Configuration Patterns
```python
# ✅ REQUIRED: Dependencies with factory pattern including direct Graphiti client
@dataclass
class MemoryAgentDeps:
    config: MemoryConfig
    database: DatabaseManager
    cache: RedisManager
    monitoring: LogfireManager
    graphiti_client: Graphiti  # Direct Graphiti library client
    
    @classmethod
    def create(cls, config_factory: MemoryConfigFactory) -> 'MemoryAgentDeps':
        return cls(
            config=config_factory.create_memory_config(),
            database=get_database_manager(),
            cache=get_cache_manager(),
            monitoring=get_monitoring_manager(),
            graphiti_client=get_graphiti_client()  # Direct library client
        )

# ✅ REQUIRED: Direct Graphiti client setup
from graphiti_core import Graphiti

def get_graphiti_client() -> Graphiti:
    """Get direct Graphiti client with configuration."""
    return Graphiti(
        neo4j_uri=config.NEO4J_URI,
        neo4j_user=config.NEO4J_USER,
        neo4j_password=config.NEO4J_PASSWORD,
        # Additional Graphiti configuration
    )
```

### Interface Definitions
- **Memory Decision API**: Structured decision responses with confidence scoring
- **Tool API**: Memory relevance assessment and query structuring tools
- **Agent API**: Async agent operations with error handling
- **Direct Graphiti API**: Direct library integration for temporal knowledge graph operations
- **Integration API**: Cross-module communication with other agents

## Development Guidelines

### Implementation Approach
1. **Agent Definition**: Create global agent instance with structured tools
2. **Direct Graphiti Integration**: Setup direct Graphiti client with connection pooling
3. **Tool Implementation**: Implement memory relevance assessment tools with direct graph operations
4. **Decision Logic**: Implement structured decision-making with confidence scoring
5. **Performance Optimization**: Implement agent reuse and direct library connection pooling
6. **Integration Testing**: Test agent behavior, decision accuracy, and direct graph operations

### Code Organization
```
src/agents/memory_agent/
├── __init__.py              # Module exports with global agent
├── memory_agent.py          # Main agent implementation
├── tools.py                 # Agent-specific tools with direct Graphiti operations
├── models.py                # Agent-specific models
├── dependencies.py          # Agent dependencies and factories
├── graphiti_client.py       # Direct Graphiti client setup and configuration
└── tests/
    ├── test_memory_agent.py # Agent behavior tests
    ├── test_tools.py        # Tool functionality tests
    ├── test_models.py       # Model validation tests
    ├── test_dependencies.py # Dependency injection tests
    └── test_graphiti_client.py # Direct Graphiti integration tests
```

### Testing Requirements
- **Agent Testing**: AI agent behavior and decision accuracy
- **Tool Testing**: Individual tool functionality and performance
- **Direct Graphiti Testing**: Direct library integration and graph operations
- **Model Testing**: Response model validation and serialization
- **Integration Testing**: Cross-module communication and data flow
- **Performance Testing**: Response time validation and agent reuse with direct library operations

### Quality Standards
- **File Length**: <400 lines per file (under 500-line limit)
- **Function Length**: <40 lines per function (under 50-line limit)
- **Type Safety**: Complete type hints for all agent operations
- **Documentation**: Google-style docstrings for all agent tools
- **Error Handling**: Comprehensive error handling with retry mechanisms

## Success Criteria

### Functional Requirements
- **Memory Relevance**: Accurately assess whether information should be stored
- **Query Optimization**: Structure efficient queries for memory retrieval
- **Context Assessment**: Evaluate applicability of retrieved memory
- **Decision Confidence**: Provide accurate confidence scores for all decisions
- **Cross-Platform**: Work seamlessly across Claude Desktop, Claude Code, and custom agents

### Performance Targets
- **Agent Response Time**: 500ms-2s for memory decisions
- **Tool Execution**: <200ms for individual tool operations
- **Decision Accuracy**: >90% accuracy in relevance assessment
- **Query Efficiency**: <500ms for structured query generation
- **Memory Usage**: <50MB for agent and tool operations

### Quality Gates
- **Test Coverage**: 85%+ test coverage for all agent components
- **Decision Accuracy**: >90% accuracy in memory relevance assessment
- **Performance Validation**: All response time targets met consistently
- **Agent Reuse**: 100% agent reuse (no per-operation instantiation)
- **Error Handling**: Graceful handling of all error conditions

### Integration Validation
- **Cross-Module Communication**: Successful communication with all other modules
- **Platform Integration**: Seamless operation across all target platforms
- **Database Operations**: Efficient database queries through infrastructure layer
- **Cache Integration**: Effective use of caching for performance optimization

## Implementation Context

### From PRD Research
- **Intelligence Gap**: Bridge the gap between memory storage and intelligent usage
- **Cross-Platform Continuity**: Enable seamless memory across Claude Desktop and Code
- **Performance Requirements**: Support 500ms-2s response time targets
- **Accuracy Requirements**: 95% accuracy in preference application and context retrieval

### Technology-Specific Implementation
- **Pydantic AI Integration**: Leverage structured AI interactions with typed responses
- **Claude-3-Sonnet**: Utilize advanced reasoning capabilities for intelligent decisions
- **Async Operations**: Implement non-blocking operations for responsive user experience
- **Error Handling**: Robust error handling with graceful degradation

### Development Standards Integration
- **Agent Reuse Pattern**: Mandatory module-global agent instantiation
- **Configuration Factory**: Environment-driven configuration with factory pattern
- **Testing Strategy**: Comprehensive testing including agent behavior validation
- **Performance Monitoring**: Continuous performance tracking and optimization

## Agent Tools Implementation

### Memory Relevance Assessment Tool
```python
@memory_agent.tool
async def evaluate_memory_relevance(
    ctx: RunContext[MemoryAgentDeps],
    conversation_context: ConversationContext,
    content: str
) -> MemoryRelevanceResult:
    """
    Evaluate whether conversation content should be stored in memory.
    
    Args:
        ctx: Agent runtime context with dependencies
        conversation_context: Context of the conversation
        content: Content to evaluate for memory storage
        
    Returns:
        MemoryRelevanceResult with decision and confidence score
    """
    # Analyze content for memory worthiness
    content_analysis = analyze_content_significance(content)
    
    # Consider user preferences and conversation context
    context_relevance = assess_context_relevance(
        conversation_context, 
        content_analysis
    )
    
    # Calculate confidence score
    confidence_score = calculate_confidence(
        content_analysis, 
        context_relevance
    )
    
    # Make storage decision
    should_store = (
        content_analysis.significance > ctx.deps.config.significance_threshold and
        context_relevance.score > ctx.deps.config.relevance_threshold and
        confidence_score > ctx.deps.config.confidence_threshold
    )
    
    return MemoryRelevanceResult(
        should_store=should_store,
        confidence=confidence_score,
        reasoning=generate_reasoning(content_analysis, context_relevance),
        storage_priority=determine_priority(content_analysis, context_relevance)
    )
```

### Query Structuring Tool
```python
@memory_agent.tool
async def structure_memory_query(
    ctx: RunContext[MemoryAgentDeps],
    query_context: QueryContext,
    search_intent: str
) -> MemoryQuery:
    """
    Structure optimal Graphiti queries for memory retrieval.
    
    Args:
        ctx: Agent runtime context with dependencies
        query_context: Context for the memory query
        search_intent: User's intent for memory retrieval
        
    Returns:
        MemoryQuery with optimized search parameters
    """
    # Analyze search intent and extract key concepts
    intent_analysis = analyze_search_intent(search_intent)
    
    # Structure query parameters for optimal performance
    query_params = optimize_query_parameters(
        intent_analysis,
        query_context,
        ctx.deps.config.query_optimization_settings
    )
    
    # Determine appropriate query type and filters
    query_type = determine_query_type(intent_analysis)
    temporal_filters = extract_temporal_filters(query_context)
    
    return MemoryQuery(
        query_type=query_type,
        search_terms=query_params.search_terms,
        temporal_filter=temporal_filters,
        confidence_threshold=ctx.deps.config.retrieval_confidence_threshold,
        max_results=query_params.max_results
    )
```

### Context Applicability Assessment Tool
```python
@memory_agent.tool
async def assess_context_applicability(
    ctx: RunContext[MemoryAgentDeps],
    retrieved_memory: List[MemoryItem],
    current_context: ConversationContext
) -> ContextApplicabilityResult:
    """
    Assess the applicability of retrieved memory to current context.
    
    Args:
        ctx: Agent runtime context with dependencies
        retrieved_memory: Memory items retrieved from storage
        current_context: Current conversation context
        
    Returns:
        ContextApplicabilityResult with relevance scoring
    """
    # Analyze each memory item for current context relevance
    relevance_scores = []
    applicable_items = []
    
    for memory_item in retrieved_memory:
        # Calculate contextual relevance
        relevance_score = calculate_contextual_relevance(
            memory_item,
            current_context
        )
        
        # Check temporal relevance
        temporal_relevance = assess_temporal_relevance(
            memory_item.timestamp,
            current_context.timestamp
        )
        
        # Combine scores
        combined_score = combine_relevance_scores(
            relevance_score,
            temporal_relevance
        )
        
        if combined_score > ctx.deps.config.applicability_threshold:
            applicable_items.append(memory_item)
            relevance_scores.append(combined_score)
    
    return ContextApplicabilityResult(
        applicable_items=applicable_items,
        relevance_scores=relevance_scores,
        confidence=calculate_overall_confidence(relevance_scores),
        reasoning=generate_applicability_reasoning(applicable_items, current_context)
    )
```

### Direct Graphiti Operations Tools
```python
@memory_agent.tool
async def store_memory_episode(
    ctx: RunContext[MemoryAgentDeps],
    conversation_context: ConversationContext,
    content: str,
    storage_priority: Literal["high", "medium", "low"] = "medium"
) -> bool:
    """
    Store memory episode directly in Graphiti temporal knowledge graph.
    
    Args:
        ctx: Agent runtime context with dependencies
        conversation_context: Context of the conversation
        content: Content to store in memory
        storage_priority: Priority level for storage
        
    Returns:
        bool: True if stored successfully, False otherwise
    """
    try:
        # Direct Graphiti library operation - no MCP overhead
        await ctx.deps.graphiti_client.add_episode(
            name=f"Memory-{conversation_context.session_id}",
            episode_body=content,
            source_description=f"Platform: {conversation_context.platform}",
            source=EpisodeType.message,
            reference_time=conversation_context.timestamp,
            # Additional metadata
            metadata={
                "user_id": conversation_context.user_id,
                "platform": conversation_context.platform,
                "priority": storage_priority
            }
        )
        return True
    except Exception as e:
        logger.error(f"Failed to store memory episode: {e}")
        return False

@memory_agent.tool
async def search_memory_graph(
    ctx: RunContext[MemoryAgentDeps],
    query: str,
    num_results: int = 5,
    center_node_uuid: Optional[str] = None
) -> List[EntityEdge]:
    """
    Search memory graph directly using Graphiti library.
    
    Args:
        ctx: Agent runtime context with dependencies
        query: Search query string
        num_results: Number of results to return
        center_node_uuid: Optional center node for focused search
        
    Returns:
        List[EntityEdge]: Search results from temporal knowledge graph
    """
    try:
        # Direct Graphiti library search - full AI intelligence
        results = await ctx.deps.graphiti_client.search(
            query=query,
            num_results=num_results,
            center_node_uuid=center_node_uuid
        )
        return results
    except Exception as e:
        logger.error(f"Failed to search memory graph: {e}")
        return []

@memory_agent.tool
async def get_temporal_facts(
    ctx: RunContext[MemoryAgentDeps],
    entity_name: str,
    time_range: Optional[DateRange] = None
) -> List[EntityEdge]:
    """
    Get temporal facts about an entity directly from Graphiti.
    
    Args:
        ctx: Agent runtime context with dependencies
        entity_name: Name of the entity to get facts about
        time_range: Optional time range filter
        
    Returns:
        List[EntityEdge]: Temporal facts about the entity
    """
    try:
        # Direct Graphiti temporal intelligence operations
        search_query = f"Facts about {entity_name}"
        if time_range:
            search_query += f" from {time_range.start} to {time_range.end}"
        
        results = await ctx.deps.graphiti_client.search(
            query=search_query,
            num_results=10,
            # Additional temporal filtering via Graphiti
        )
        
        # Filter results based on time range if provided
        if time_range:
            results = [
                result for result in results
                if time_range.start <= result.created_at <= time_range.end
            ]
        
        return results
    except Exception as e:
        logger.error(f"Failed to get temporal facts: {e}")
        return []
```

## Data Models

### Agent Response Models
```python
class MemoryDecision(BaseModel):
    """Memory storage/retrieval decision from agent."""
    should_store: bool = Field(description="Whether to store the information")
    confidence: float = Field(ge=0.0, le=1.0, description="Decision confidence")
    reasoning: str = Field(description="Human-readable decision reasoning")
    storage_priority: Literal["high", "medium", "low"] = Field(description="Storage priority")
    estimated_utility: float = Field(ge=0.0, le=1.0, description="Estimated future utility")

class MemoryRelevanceResult(BaseModel):
    """Result of memory relevance assessment."""
    should_store: bool = Field(description="Storage recommendation")
    confidence: float = Field(ge=0.0, le=1.0, description="Assessment confidence")
    reasoning: str = Field(description="Assessment reasoning")
    storage_priority: Literal["high", "medium", "low"] = Field(description="Priority level")
    content_categories: List[str] = Field(description="Identified content categories")

class ContextApplicabilityResult(BaseModel):
    """Result of context applicability assessment."""
    applicable_items: List[MemoryItem] = Field(description="Applicable memory items")
    relevance_scores: List[float] = Field(description="Relevance scores for each item")
    confidence: float = Field(ge=0.0, le=1.0, description="Overall confidence")
    reasoning: str = Field(description="Applicability reasoning")
```

## Performance Optimization

### Agent Reuse Strategy
```python
# ✅ REQUIRED: Module-level agent with dependency injection
async def process_memory_decision(
    conversation_context: ConversationContext,
    content: str,
    deps: MemoryAgentDeps
) -> MemoryDecision:
    """Process memory decision using global agent instance."""
    # Use global agent instance - never instantiate per call
    result = await memory_agent.run(
        user_input=content,
        conversation_context=conversation_context,
        deps=deps
    )
    
    # Log performance metrics
    with deps.monitoring.trace_operation("memory.decision"):
        return result
```

### Caching Strategy
```python
async def cached_memory_decision(
    conversation_context: ConversationContext,
    content: str,
    deps: MemoryAgentDeps
) -> MemoryDecision:
    """Memory decision with caching optimization."""
    # Generate cache key
    cache_key = generate_cache_key(conversation_context, content)
    
    # Check cache first
    cached_result = await deps.cache.get(cache_key)
    if cached_result:
        return MemoryDecision.model_validate_json(cached_result)
    
    # Generate new decision
    decision = await process_memory_decision(
        conversation_context,
        content,
        deps
    )
    
    # Cache result
    await deps.cache.set(
        cache_key,
        decision.model_dump_json(),
        expire=3600  # 1 hour cache
    )
    
    return decision
```

### Direct Graphiti Performance Optimization
```python
# ✅ REQUIRED: Direct Graphiti operations with connection pooling
async def optimized_memory_storage(
    conversation_context: ConversationContext,
    content: str,
    deps: MemoryAgentDeps
) -> bool:
    """Optimized memory storage using direct Graphiti library."""
    
    # Use direct Graphiti client - no MCP overhead
    with deps.monitoring.trace_operation("memory.direct_storage"):
        try:
            # Direct library operation with full intelligence
            await deps.graphiti_client.add_episode(
                name=f"Memory-{conversation_context.session_id}",
                episode_body=content,
                source_description=f"Platform: {conversation_context.platform}",
                source=EpisodeType.message,
                reference_time=conversation_context.timestamp
            )
            return True
        except Exception as e:
            logger.error(f"Direct Graphiti storage failed: {e}")
            return False

async def optimized_memory_search(
    query: str,
    deps: MemoryAgentDeps,
    num_results: int = 5
) -> List[EntityEdge]:
    """Optimized memory search using direct Graphiti library."""
    
    # Direct search with full temporal intelligence
    with deps.monitoring.trace_operation("memory.direct_search"):
        try:
            # Direct library search - full AI intelligence
            results = await deps.graphiti_client.search(
                query=query,
                num_results=num_results
            )
            return results
        except Exception as e:
            logger.error(f"Direct Graphiti search failed: {e}")
            return []
```

---

**Module Status**: ✅ Specification Complete
**Implementation Ready**: Yes - All agent requirements and tools defined with direct Graphiti integration
**Critical Requirements**: Must use module-global agent instantiation AND direct Graphiti library integration
**Next Step**: Begin implementation with agent definition, direct Graphiti client setup, and tool development
**Integration Points**: Direct Graphiti library for internal operations, external MCP interface for cross-platform communication