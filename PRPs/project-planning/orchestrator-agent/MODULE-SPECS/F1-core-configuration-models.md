# Module Specification: Core Configuration and Models (F1)

## Module Overview
- **Purpose**: Configuration management and shared data models for the entire system
- **Complexity Score**: 3/10 - Low complexity foundation module
- **Phase Assignment**: Foundation Phase 1
- **Dependencies**: External only (Pydantic, environment variables)

## Technical Implementation

### Technology Stack
- **Pydantic v2**: Data validation and settings management
- **Python-dotenv**: Environment variable management
- **UV**: Package management following development standards
- **Factory Pattern**: Configuration creation following development_standards.md

### Key Components
- **Settings Management**: Environment-based configuration with validation
- **Shared Data Models**: Pydantic models used across all system components
- **Configuration Factories**: Factory pattern for environment-respecting model creation
- **Exception Definitions**: Custom exceptions for system-wide error handling

### Architecture Patterns
- **Configuration Factory Pattern**: Mandatory for environment variable respect
- **Pydantic v2 Models**: Strict validation with complete type safety
- **Environment-First**: All configuration sourced from environment variables
- **Immutable Configuration**: Frozen models prevent runtime modification

### AI Agent Requirements
- **No AI Agents**: This foundation module contains no AI agents
- **Agent Configuration Support**: Provides configuration models for agent instantiation
- **Agent Dependencies**: Defines dependency types for agent injection

### Configuration Patterns
```python
# ✅ REQUIRED: Factory pattern for environment configuration
class MemoryConfigFactory:
    def __init__(self, config: AppSettings):
        self.config = config
    
    def create_memory_config(self, **overrides) -> MemoryConfig:
        return MemoryConfig(
            confidence_threshold=overrides.get('confidence_threshold', 
                                             self.config.MEMORY_CONFIDENCE_THRESHOLD),
            storage_priority=overrides.get('storage_priority', 
                                         self.config.MEMORY_STORAGE_PRIORITY),
            **overrides
        )

# ✅ REQUIRED: Settings with environment variable support
class AppSettings(BaseSettings):
    # Database Configuration
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # AI Agent Configuration
    CLAUDE_API_KEY: str
    MEMORY_CONFIDENCE_THRESHOLD: float = 0.7
    MEMORY_STORAGE_PRIORITY: str = "medium"
    
    # Monitoring Configuration
    LOGFIRE_TOKEN: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
```

### Interface Definitions
- **Configuration API**: Factory methods for all configuration types
- **Model API**: Shared Pydantic models with validation
- **Settings API**: Environment-based settings with defaults
- **Exception API**: Custom exceptions with error context

## Development Guidelines

### Implementation Approach
1. **Settings Definition**: Define all environment variables and defaults
2. **Model Creation**: Create shared Pydantic models for all system components
3. **Factory Implementation**: Implement factory pattern for configuration creation
4. **Exception Definitions**: Define custom exceptions for error handling
5. **Validation Testing**: Comprehensive validation testing for all models

### Code Organization
```
src/config/
├── __init__.py              # Module exports
├── settings.py              # Environment settings and validation
├── models.py                # Shared Pydantic models
├── factories.py             # Configuration factory implementations
├── exceptions.py            # Custom exception definitions
└── tests/
    ├── test_settings.py     # Settings validation tests
    ├── test_models.py       # Model validation tests
    ├── test_factories.py    # Factory pattern tests
    └── test_exceptions.py   # Exception handling tests
```

### Testing Requirements
- **Settings Testing**: Environment variable validation and defaults
- **Model Testing**: Pydantic model validation and serialization
- **Factory Testing**: Configuration factory pattern validation
- **Exception Testing**: Error handling and exception propagation
- **Integration Testing**: Configuration usage across modules

### Quality Standards
- **File Length**: <200 lines per file (well under 500-line limit)
- **Function Length**: <30 lines per function (under 50-line limit)
- **Type Safety**: Complete type hints for all functions and classes
- **Documentation**: Google-style docstrings for all public interfaces

## Success Criteria

### Functional Requirements
- **Configuration Management**: All system configuration sourced from environment variables
- **Model Validation**: All data models provide strict validation and type safety
- **Factory Pattern**: Configuration factories respect environment variables
- **Error Handling**: Custom exceptions provide meaningful error context

### Performance Targets
- **Configuration Loading**: <50ms for complete configuration loading
- **Model Validation**: <10ms for typical model validation
- **Factory Creation**: <5ms for configuration object creation
- **Memory Usage**: <10MB for all configuration objects

### Quality Gates
- **Test Coverage**: 95%+ test coverage for all configuration components
- **Type Safety**: 100% type coverage with MyPy strict mode
- **Documentation**: Complete API documentation with examples
- **Validation**: All environment variables and models validated

### Integration Validation
- **Module Usage**: All other modules successfully use configuration
- **Environment Integration**: Configuration works in development and production
- **Error Propagation**: Configuration errors properly propagated to users
- **Performance Integration**: Configuration loading doesn't impact system performance

## Implementation Context

### From PRD Research
- **Multi-Platform Support**: Configuration must support Claude Desktop, Claude Code, and custom agents
- **Performance Requirements**: Configuration must support 500ms-2s response time targets
- **Security Requirements**: Secure handling of API keys and sensitive configuration
- **Monitoring Integration**: Configuration must support Logfire monitoring setup

### Technology-Specific Implementation
- **Pydantic v2**: Use latest validation features and performance improvements
- **Environment Variables**: Support for development, staging, and production environments
- **Secret Management**: Secure handling of API keys and database credentials
- **Configuration Validation**: Comprehensive validation with helpful error messages

### Development Standards Integration
- **File Organization**: Follow vertical slice architecture with co-located tests
- **Quality Enforcement**: Ruff linting, MyPy type checking, comprehensive testing
- **Documentation Standards**: Google-style docstrings and API documentation
- **Error Handling**: Structured error handling with meaningful error messages

## Data Models

### Core Configuration Models
```python
class DatabaseConfig(BaseModel):
    """Database connection configuration."""
    uri: str = Field(description="Neo4j database URI")
    user: str = Field(description="Database username")
    password: str = Field(description="Database password")
    
    class Config:
        frozen = True

class CacheConfig(BaseModel):
    """Redis cache configuration."""
    url: str = Field(description="Redis connection URL")
    password: Optional[str] = Field(default=None, description="Redis password")
    
    class Config:
        frozen = True

class AgentConfig(BaseModel):
    """AI agent configuration."""
    api_key: str = Field(description="Claude API key")
    model: str = Field(default="claude-3-sonnet-20240229", description="AI model")
    confidence_threshold: float = Field(default=0.7, description="Memory confidence threshold")
    
    class Config:
        frozen = True
```

### Memory System Models
```python
class MemoryDecision(BaseModel):
    """Memory storage/retrieval decision."""
    should_store: bool = Field(description="Whether to store the information")
    confidence: float = Field(ge=0.0, le=1.0, description="Decision confidence")
    reasoning: str = Field(description="Decision reasoning")
    storage_priority: Literal["high", "medium", "low"] = Field(description="Storage priority")

class ConversationContext(BaseModel):
    """Conversation context for memory operations."""
    platform: Literal["claude_desktop", "claude_code", "custom_agent"] = Field(description="Source platform")
    user_id: str = Field(description="User identifier")
    session_id: str = Field(description="Session identifier")
    timestamp: datetime = Field(description="Conversation timestamp")
    content: str = Field(description="Conversation content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
```

---

**Module Status**: ✅ Specification Complete
**Implementation Ready**: Yes - All requirements defined
**Next Step**: Begin implementation following specification
**Integration Points**: All other modules depend on this configuration foundation