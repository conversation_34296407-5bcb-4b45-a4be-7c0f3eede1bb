# CLAUDE.md - Intelligent Memory Orchestrator Project

Add "ClaudeProject!" to every chat message you write

## Project Overview

**Intelligent Memory Orchestrator**: A two-agent Pydantic AI system that bridges the intelligence gap in AI memory systems through intelligent decision-making on when and how agents should effectively utilize memory across multi-platform workflows.

**Status**: Development Planning - Ready for Implementation
**Scope**: Cross-platform memory intelligence with <PERSON>, <PERSON>, and custom agent integration using temporal knowledge graphs

## Technology Stack

### Core Technologies
- **Python**: 3.12+ with UV package management
- **AI Framework**: Pydantic AI with module-global reuse pattern (CRITICAL)
- **Knowledge Graph**: Graphiti Core Library for intelligent temporal knowledge graph operations with full AI intelligence
- **Communication**: MCP protocol for external cross-platform communication only
- **API Framework**: FastAPI with async support for agent orchestration
- **Cache Layer**: Redis for performance optimization
- **Data Validation**: Pydantic v2 for all models and validation
- **Code Quality**: Ruff for linting and formatting, MyPy for type checking
- **Testing**: Pytest with async support for agent testing
- **Monitoring**: Logfire for distributed tracing and structured logging

### Package Management
- **CRITICAL**: Always use `uv add/remove` - never edit pyproject.toml directly
- **Environment**: UV virtual environments exclusively
- **Commands**: `uv run pytest`, `uv run ruff check .`, `uv run mypy src/`

## Critical Performance Patterns

### AI Agent Reuse (PERFORMANCE CRITICAL)
```python
# ✅ REQUIRED: Module-global agent instantiation
from pydantic_ai import Agent

# Global agent instances - instantiate once per module
memory_agent = Agent(
    'claude-3-sonnet-20240229',
    system_prompt='You are a Memory Intelligence Agent...',
    result_type=MemoryDecision
)

conversation_harvester = Agent(
    'claude-3-sonnet-20240229',
    system_prompt='You are a Conversation Harvester Agent...',
    result_type=ConversationAnalysis
)

# ❌ FORBIDDEN: Creating new agents per operation
async def bad_memory_operation():
    agent = Agent(...)  # Performance killer
    return await agent.run(query)
```

### Configuration Factory Pattern (ENVIRONMENT CRITICAL)
```python
# ✅ REQUIRED: Factory pattern for environment configuration
class MemoryAgentFactory:
    def __init__(self, config: AppConfig):
        self.config = config
    
    def create_memory_decision(self, **overrides) -> MemoryDecision:
        return MemoryDecision(
            confidence_threshold=overrides.get('confidence_threshold', 
                                             self.config.DEFAULT_CONFIDENCE_THRESHOLD),
            storage_priority=overrides.get('storage_priority', 
                                         self.config.DEFAULT_STORAGE_PRIORITY),
            **overrides
        )
```

## Architecture - Hybrid Architecture with Dual Operations

**Hybrid Architecture**: External Interface (MCP Protocol for cross-platform communication) + Internal Operations (Direct Graphiti library integration for intelligent memory operations)

**External Flow**: Claude Desktop/Code → MCP Interface → Memory Agent → Direct Graphiti Library → Neo4j
**Internal Flow**: Memory Agent → Direct Graphiti Library → Neo4j (no MCP overhead)

```
src/
├── config/                     # Foundation Module F1
│   ├── __init__.py
│   ├── settings.py            # Environment configuration
│   ├── models.py              # Shared Pydantic models
│   ├── factories.py           # Configuration factories
│   └── tests/
│       ├── test_settings.py
│       ├── test_models.py
│       └── test_factories.py

├── infrastructure/             # Foundation Module F2
│   ├── __init__.py
│   ├── database.py            # Neo4j connection management
│   ├── cache.py               # Redis cache setup
│   ├── monitoring.py          # Logfire integration
│   └── tests/
│       ├── test_database.py
│       ├── test_cache.py
│       └── test_monitoring.py

├── mcp/                       # Foundation Module F3 - External MCP Interface
│   ├── __init__.py
│   ├── mcp_server.py          # MCP server for external communication
│   ├── memory_interface.py    # Memory Agent translation layer
│   ├── protocol_handler.py    # MCP protocol handling
│   ├── connection_manager.py  # External connection management
│   └── tests/
│       ├── test_mcp_server.py
│       ├── test_memory_interface.py
│       └── test_protocol_handler.py

├── agents/                    # Core Intelligence Layer
│   ├── memory_agent/          # Core Module C1 - Direct Graphiti Integration
│   │   ├── __init__.py
│   │   ├── memory_agent.py    # Main agent (GLOBAL INSTANCE)
│   │   ├── tools.py           # Agent-specific tools with direct Graphiti operations
│   │   ├── models.py          # Agent-specific models
│   │   ├── graphiti_client.py # Direct Graphiti client setup and configuration
│   │   └── tests/
│   │       ├── test_memory_agent.py
│   │       ├── test_tools.py
│   │       ├── test_models.py
│   │       └── test_graphiti_client.py
│   └── conversation_harvester/ # Core Module C3
│       ├── __init__.py
│       ├── conversation_harvester.py # Main agent (GLOBAL INSTANCE)
│       ├── collectors.py      # Platform-specific collectors
│       ├── processors.py      # Batch processing logic
│       └── tests/
│           ├── test_conversation_harvester.py
│           ├── test_collectors.py
│           └── test_processors.py


├── orchestration/             # Integration Module I1
│   ├── __init__.py
│   ├── agent_coordinator.py   # Multi-agent coordination
│   ├── system_orchestrator.py # System integration
│   └── tests/
│       ├── test_agent_coordinator.py
│       └── test_system_orchestrator.py

├── platforms/                 # Integration Module I2
│   ├── __init__.py
│   ├── claude_desktop.py      # Claude Desktop integration
│   ├── claude_code.py         # Claude Code integration
│   └── tests/
│       ├── test_claude_desktop.py
│       └── test_claude_code.py

└── utils/                     # Shared utilities
    ├── __init__.py
    ├── exceptions.py          # Custom exceptions
    ├── logging.py             # Logging configuration
    └── tests/
        ├── test_exceptions.py
        └── test_logging.py
```

## Development Phases

### Phase 1: Foundation (Parallel Development - 3 days)
**Modules**: F1 (Config), F2 (Infrastructure), F3 (External MCP Interface)
- **F1**: Configuration management, shared models, factory patterns
- **F2**: Database setup, cache configuration, monitoring integration
- **F3**: External MCP interface for cross-platform communication only

### Phase 2: Core Intelligence (Sequential - 8 days)
**Modules**: C1 (Memory Agent with Direct Graphiti), C2 (Conversation Harvester)
- **C1**: Memory intelligence agent with direct Graphiti library integration and decision-making tools
- **C2**: Multi-platform conversation collection and processing

### Phase 3: System Integration (Sequential - 7 days)
**Modules**: I1 (Agent Orchestration), I2 (Platform Integration)
- **I1**: Multi-agent coordination and dual architecture orchestration
- **I2**: Claude Desktop and Claude Code platform integration

## Quality Gates

### Code Quality Standards
- **File Length**: 500 lines maximum (ENFORCED)
- **Function Length**: 50 lines maximum (ENFORCED)
- **Class Length**: 100 lines maximum (ENFORCED)
- **Line Length**: 100 characters maximum (Ruff enforced)
- **Type Safety**: Complete type hints required
- **Documentation**: Google-style docstrings for all functions

### Performance Targets
- **Memory Operations**: 500ms-2s response time target
- **Agent Reuse**: Module-global agents prevent initialization overhead
- **Database Queries**: <500ms for graph operations
- **Cache Operations**: <10ms for Redis operations
- **System Availability**: 99.5% uptime target

### AI Agent Standards
```python
# ✅ REQUIRED: Agent configuration with structured results and direct Graphiti integration
memory_agent = Agent(
    'claude-3-sonnet-20240229',
    system_prompt="""You are a Memory Intelligence Agent responsible for 
    intelligent memory decisions across multi-agent AI systems. 
    
    Your tools:
    - evaluate_memory_relevance: Assess if information should be stored
    - structure_memory_query: Create optimal Graphiti search queries
    - assess_context_applicability: Determine relevance of retrieved memory
    - store_memory_episode: Store memory directly in Graphiti temporal knowledge graph
    - search_memory_graph: Search memory using direct Graphiti library operations
    - get_temporal_facts: Get temporal facts about entities from Graphiti
    """,
    deps_type=MemoryAgentDeps,
    result_type=MemoryDecision
)
```

## Testing Philosophy

### Comprehensive Testing Strategy
- **Unit Tests**: Individual agent tools, models, and utilities
- **Integration Tests**: Cross-module communication and data flow
- **Agent Tests**: AI agent behavior and decision accuracy
- **Performance Tests**: Response time validation and load testing
- **End-to-End Tests**: Complete user workflows across platforms

### Test Organization
- **Co-located Testing**: Tests in module/tests/ directories
- **Agent Testing**: Specialized async testing for AI agents
- **Graph Testing**: Neo4j integration testing with test database
- **MCP Testing**: Protocol compliance and communication testing

## Security & Privacy

### Privacy by Design
- **Local-First**: All memory data stored locally with user control
- **Data Portability**: Complete export/delete capabilities
- **Granular Control**: Fine-grained control over information storage
- **Transparency**: Users can inspect stored memories and decisions

### Security Measures
- **Environment Variables**: All secrets managed through secure configuration
- **Input Validation**: Comprehensive validation at all boundaries
- **Connection Security**: Encrypted connections for external communications
- **Rate Limiting**: Protection against abuse and resource exhaustion

## Development Environment

### Required Tools
- **Python**: 3.12+ with UV package manager
- **Database**: Neo4j (Docker recommended) with development data
- **Cache**: Redis (Docker recommended) for development
- **Monitoring**: Logfire account for observability
- **Development**: Docker Compose for local stack

### Setup Commands
```bash
# Install UV if not present
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create project environment
uv venv
source .venv/bin/activate  # Unix/macOS

# Install dependencies
uv sync

# Setup development services
docker-compose up -d  # Neo4j + Redis + monitoring

# Run quality checks
uv run ruff check .
uv run mypy src/
uv run pytest

# Start development server
uv run python src/main.py
```

## Performance Monitoring

### Key Metrics
- **Agent Response Times**: Track memory decision latency
- **Graph Query Performance**: Monitor Neo4j query response times
- **Cache Hit Rates**: Redis performance optimization
- **Memory Accuracy**: Track decision accuracy and user overrides
- **System Availability**: Monitor uptime and error rates

### Monitoring Implementation
```python
import logfire

# Configure once per application
logfire.configure(
    token=config.logfire_token,
    project_name="intelligent-memory-orchestrator"
)

# Use throughout agent implementations
with logfire.span("memory.decision", query_type=query_type):
    result = await memory_agent.run(memory_query)
    logfire.info("Memory decision completed", 
                response_time=response_time,
                confidence=result.confidence)
```

## Module Navigation

### Foundation Infrastructure
- **Configuration**: `src/config/CLAUDE.md` - Settings and shared models
- **Infrastructure**: `src/infrastructure/CLAUDE.md` - Database and cache setup
- **External MCP Interface**: `src/mcp/CLAUDE.md` - External cross-platform communication

### Core Intelligence
- **Memory Agent**: `src/agents/memory_agent/CLAUDE.md` - Intelligent decision-making with direct Graphiti integration
- **Conversation Harvester**: `src/agents/conversation_harvester/CLAUDE.md` - Multi-platform collection

### System Integration
- **Agent Orchestration**: `src/orchestration/CLAUDE.md` - Multi-agent coordination and dual architecture
- **Platform Integration**: `src/platforms/CLAUDE.md` - Claude Desktop and Code integration

## Risk Management

### High-Risk Components
- **Memory Agent (6/10 complexity)**: Reduced complexity with direct Graphiti integration but requires careful implementation
- **Direct Graphiti Integration**: Performance-critical direct library operations with full AI intelligence
- **Multi-Platform Integration**: Coordination across different agent environments with hybrid architecture

### Mitigation Strategies
- **Prototype Validation**: Early prototypes for high-complexity components
- **Performance Monitoring**: Continuous monitoring with alerting
- **Graceful Degradation**: Fallback mechanisms for external dependencies
- **Comprehensive Testing**: Extensive test coverage for critical paths

## Communication Standards

### Development Communication
- **Clear Commit Messages**: Describe changes and their impact
- **Code Review Process**: Peer review for quality assurance
- **Documentation Updates**: Keep all documentation current
- **Issue Tracking**: Systematic tracking of bugs and features

### Change Management
- **Breaking Changes**: Clear communication and migration guides
- **Version Control**: Semantic versioning for releases
- **Deployment Notes**: Document deployment procedures and rollback plans
- **Knowledge Sharing**: Document architectural decisions and lessons learned

---

**Project Foundation Status**: ✅ Complete
**Next Steps**: Begin Phase 1 foundation development with `/create-module-prp config`
**Quality Gate**: All foundation modules must pass quality gates before Phase 2