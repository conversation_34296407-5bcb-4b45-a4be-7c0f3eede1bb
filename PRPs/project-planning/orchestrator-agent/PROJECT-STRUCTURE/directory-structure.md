# Memory Orchestrator Directory Structure

## Complete Project Structure

```
src/
├── config/                     # Foundation Module F1 - Core Configuration
│   ├── __init__.py
│   ├── settings.py            # Memory orchestrator configuration
│   ├── models.py              # Shared Pydantic models for memory operations
│   ├── exceptions.py          # Memory orchestrator custom exceptions
│   └── tests/
│       ├── __init__.py
│       ├── test_settings.py
│       ├── test_models.py
│       └── test_exceptions.py
├── infrastructure/             # Foundation Module F2 - Infrastructure Setup
│   ├── __init__.py
│   ├── database.py            # Neo4j connection with Graphiti integration
│   ├── cache.py               # Redis cache for memory query optimization
│   ├── monitoring.py          # Logfire integration for memory system observability
│   └── tests/
│       ├── __init__.py
│       ├── test_database.py
│       ├── test_cache.py
│       └── test_monitoring.py
├── mcp/                       # Foundation Module F3 - MCP Protocol Foundation
│   ├── __init__.py
│   ├── client.py              # MCP client for Claude platform integration
│   ├── server.py              # MCP server foundations for memory operations
│   ├── protocol.py            # MCP protocol handling with memory-specific tools
│   ├── wrapper.py             # Memory MCP wrapper for standardized communication
│   └── tests/
│       ├── __init__.py
│       ├── test_client.py
│       ├── test_server.py
│       ├── test_protocol.py
│       └── test_wrapper.py
├── agents/                    # Core Modules C1, C3 - AI Agents
│   ├── __init__.py
│   ├── memory_agent/          # Core Module C1 - Memory Intelligence Core
│   │   ├── __init__.py
│   │   ├── memory_agent.py    # Main memory intelligence agent
│   │   ├── tools.py           # Memory-specific agent tools
│   │   ├── models.py          # Memory agent data models
│   │   ├── evaluators.py      # Relevance assessment algorithms
│   │   └── tests/
│   │       ├── __init__.py
│   │       ├── test_memory_agent.py
│   │       ├── test_tools.py
│   │       ├── test_models.py
│   │       └── test_evaluators.py
│   └── conversation_harvester/ # Core Module C3 - Conversation Processing Core
│       ├── __init__.py
│       ├── conversation_harvester.py
│       ├── collectors.py      # Platform-specific conversation collectors
│       ├── processors.py      # Batch conversation processing logic
│       ├── deduplicators.py   # Conversation deduplication logic
│       └── tests/
│           ├── __init__.py
│           ├── test_conversation_harvester.py
│           ├── test_collectors.py
│           ├── test_processors.py
│           └── test_deduplicators.py
├── integrations/              # Core Module C2 - Graphiti Integration Core
│   ├── __init__.py
│   ├── graphiti/
│   │   ├── __init__.py
│   │   ├── graphiti_client.py # Graphiti MCP integration for temporal graphs
│   │   ├── query_optimizer.py # Memory-specific graph query optimization
│   │   ├── models.py          # Graphiti-specific temporal models
│   │   ├── schema_manager.py  # Graph schema management
│   │   └── tests/
│   │       ├── __init__.py
│   │       ├── test_graphiti_client.py
│   │       ├── test_query_optimizer.py
│   │       ├── test_models.py
│   │       └── test_schema_manager.py
├── orchestration/             # Integration Module I1 - Agent Orchestration
│   ├── __init__.py
│   ├── agent_coordinator.py   # Multi-agent coordination for memory operations
│   ├── system_orchestrator.py # System integration and performance optimization
│   ├── performance_monitor.py # Performance monitoring and optimization
│   └── tests/
│       ├── __init__.py
│       ├── test_agent_coordinator.py
│       ├── test_system_orchestrator.py
│       └── test_performance_monitor.py
├── platforms/                 # Integration Module I2 - Platform Integration
│   ├── __init__.py
│   ├── claude_desktop.py      # Claude Desktop memory integration
│   ├── claude_code.py         # Claude Code memory integration
│   ├── session_manager.py     # Cross-platform session management
│   └── tests/
│       ├── __init__.py
│       ├── test_claude_desktop.py
│       ├── test_claude_code.py
│       └── test_session_manager.py
├── utils/                     # Shared Utilities
│   ├── __init__.py
│   ├── logging.py             # Structured logging for memory operations
│   ├── validators.py          # Input validation utilities
│   ├── performance.py         # Performance measurement utilities
│   └── tests/
│       ├── __init__.py
│       ├── test_logging.py
│       ├── test_validators.py
│       └── test_performance.py
├── api/                       # API Layer (Optional FastAPI implementation)
│   ├── __init__.py
│   ├── main.py                # FastAPI application setup
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── memory.py          # Memory operation endpoints
│   │   └── health.py          # Health check endpoints
│   └── tests/
│       ├── __init__.py
│       ├── test_main.py
│       └── test_routes.py
├── main.py                    # Application entry point
├── conftest.py                # Pytest configuration
└── tests/                     # Integration tests
    ├── __init__.py
    ├── integration/
    │   ├── __init__.py
    │   ├── test_memory_flow.py
    │   ├── test_conversation_flow.py
    │   └── test_cross_platform.py
    └── performance/
        ├── __init__.py
        ├── test_memory_performance.py
        └── test_system_performance.py
```

## Development Configuration Files

```
# Root level configuration
├── pyproject.toml             # UV package configuration
├── .env.example               # Environment variable template
├── .gitignore                 # Git ignore patterns
├── README.md                  # Project documentation
├── CHANGELOG.md               # Version history
├── docker-compose.yml         # Local development infrastructure
├── Dockerfile                 # Container configuration
└── .github/
    └── workflows/
        ├── ci.yml             # Continuous integration
        └── cd.yml             # Continuous deployment
```

## Module Boundaries and Responsibilities

### Foundation Layer (Phase 1 - Parallel Development)
- **F1 (config/)**: Configuration management, shared models, exceptions
- **F2 (infrastructure/)**: Database, cache, monitoring setup
- **F3 (mcp/)**: MCP protocol implementation and memory wrapper

### Core Logic Layer (Phase 2 - Sequential Development)
- **C1 (agents/memory_agent/)**: Memory intelligence and decision-making
- **C2 (integrations/graphiti/)**: Temporal graph operations and optimization
- **C3 (agents/conversation_harvester/)**: Multi-platform conversation processing

### Integration Layer (Phase 3 - Sequential Development)
- **I1 (orchestration/)**: Agent coordination and system orchestration
- **I2 (platforms/)**: Platform-specific integrations and session management

### Supporting Components
- **utils/**: Shared utilities across all modules
- **api/**: Optional FastAPI layer for HTTP endpoints
- **tests/**: System-level integration and performance tests

## File Naming Conventions

### Module Files
- **Primary module file**: `{module_name}.py` (e.g., `memory_agent.py`)
- **Supporting files**: Descriptive names (e.g., `evaluators.py`, `collectors.py`)
- **Test files**: `test_{module_name}.py` pattern
- **Init files**: `__init__.py` for package definition

### Class and Function Naming
- **Classes**: PascalCase (e.g., `MemoryAgent`, `ConversationHarvester`)
- **Functions**: snake_case (e.g., `evaluate_relevance`, `process_conversation`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `DEFAULT_RELEVANCE_THRESHOLD`)
- **Private methods**: `_leading_underscore`

### Configuration Files
- **Settings**: `settings.py` for configuration classes
- **Models**: `models.py` for Pydantic data models
- **Exceptions**: `exceptions.py` for custom exception classes
- **Tests**: `test_*.py` for all test files

## Development Phase Organization

### Phase 1: Foundation (3 days parallel)
All foundation modules can be developed simultaneously as they have no internal dependencies.

### Phase 2: Core Logic (10 days sequential)
Core modules must be developed sequentially due to shared dependencies:
1. **C1 (Memory Agent)**: 4 days - Core intelligence implementation
2. **C2 (Graphiti Integration)**: 3 days - Graph operations and optimization
3. **C3 (Conversation Harvester)**: 3 days - Multi-platform processing

### Phase 3: Integration (5 days sequential)
Integration modules require completed core modules:
1. **I1 (Agent Orchestration)**: 3 days - System coordination
2. **I2 (Platform Integration)**: 2 days - Platform-specific implementations

## Testing Organization

### Unit Tests
- Co-located with source code in `tests/` subdirectories
- Focus on individual function and class testing
- Mock external dependencies for isolation

### Integration Tests
- Located in `tests/integration/` at root level
- Test module boundaries and data flow
- Use real dependencies where possible

### Performance Tests
- Located in `tests/performance/` at root level
- Validate response time requirements (500ms-2s)
- Load testing and scalability validation

## Documentation Structure

### Code Documentation
- **Module docstrings**: Purpose and usage for each module
- **Class docstrings**: Responsibility and key methods
- **Function docstrings**: Google-style with args, returns, raises
- **Inline comments**: `# Reason:` for complex logic

### Project Documentation
- **README.md**: Project overview and setup instructions
- **CHANGELOG.md**: Version history and changes
- **API documentation**: Auto-generated from docstrings
- **Architecture documentation**: System design and patterns