name: "Multi-Agent Prototype Template - Mini<PERSON> Scope with <PERSON><PERSON> Pat<PERSON>"
description: |

## Purpose
Template optimized for rapid prototype development with multi-agent research synthesis, providing essential context for minimal implementation while maintaining proper patterns and structure for production evolution.

## Core Principles
1. **Minimal Scope Focus**: Core concept proof with essential features only
2. **Proper Patterns**: Clean implementation following best practices from start
3. **Rapid Development**: Optimized for 60+ minute implementation timeline
4. **Learning Orientation**: Focus on constraint discovery and assumption validation
5. **Production Preparation**: Structure that supports scaling to production

---

## Prototype Goal
[Core concept to prove - be specific about minimal viable functionality]

## Why Build This Prototype
- [Key assumption to validate]
- [Constraint to discover]
- [Technical feasibility to prove]

## What (Minimal Scope)
[Essential functionality only - core concept proof]

### Prototype Success Criteria
- [ ] [Core concept proven working]
- [ ] [Key constraint discovered]
- [ ] [Technical assumption validated]

## Essential Research Context

### Minimal Implementation Guidance
```yaml
prototype_focus:
  core_concept: [single main functionality to prove]
  essential_patterns: [clean structure to follow]
  constraint_discovery: [what we need to learn]
  
specialist_findings:
  codebase_patterns:
    - file: [minimal but clean example to follow]
      pattern: [essential structure approach]
      prototype_adaptation: [how to simplify while keeping structure]
  
  documentation_essentials:
    - url: [quickstart guide or basic tutorial]
      sections: [getting started, basic implementation]
      key_patterns: [minimal official implementation approaches]
  
  critical_requirements:
    - requirement: [essential technical need]
      minimal_approach: [simplest way to address this]
      gotcha: [issue that affects even minimal implementation]
```

## All Needed Context (Prototype-Focused)

### Essential Documentation
```yaml
# MUST READ - Key resources for minimal implementation
essential_docs:
  - url: [Official quickstart/tutorial URL]
    why: [Basic implementation patterns needed]
    focus_sections: [getting started, basic examples]
    
  - url: [API documentation for core functionality]
    why: [Essential API methods for prototype]
    focus_sections: [basic usage, authentication if needed]

minimal_examples:
  - file: [path/to/simple_example.py]
    why: [Clean minimal pattern to follow]
    adaptation: [how to simplify for prototype scope]
    
  - file: [path/to/basic_structure.py]
    why: [Essential project structure to maintain]
    pattern: [architectural approach for minimal scope]
```

### Current Codebase (Essential Patterns)
```bash
# Minimal codebase structure for prototype
src/
├── [prototype_main].py          # Core prototype functionality
├── models.py                    # Basic data models (if needed)
└── config.py                    # Essential configuration

# Essential patterns to follow:
# - [Pattern 1]: [Minimal implementation approach]
# - [Pattern 2]: [Basic structure for prototype scope]
```

### Critical Gotchas (Prototype-Applicable)
```python
# ESSENTIAL GOTCHAS - Apply even to minimal implementations
# [Library/API name] requirements:
# - [Basic setup requirement for prototype]
# - [Configuration needed even for minimal implementation]
# - [Common issue that affects prototype development]

# Project standards (prototype-applicable):
# - [Essential pattern that must be followed]
# - [Basic validation approach required]
# - [Minimal error handling standard]
```

## Rapid Implementation Blueprint

### Essential Data Models (If Needed)
```python
# Basic data models for prototype
# Keep minimal but proper structure:
#  - Basic Pydantic models for data validation
#  - Simple configuration models
#  - Core data structures only

# Prototype approach:
# - Use hardcoded values where appropriate
# - Focus on proving concept, not comprehensive validation
# - Include TODOs for production enhancement
```

### Minimal Task Sequence
```yaml
Task 1: Core Functionality Proof
  approach: |
    CREATE src/[prototype_main].py:
      - IMPLEMENT core concept with minimal scope
      - FOLLOW clean pattern from [essential_example]
      - FOCUS on proving concept works
      - INCLUDE basic error handling only
  
  validation:
    - Core functionality works
    - Basic integration successful
    - Key assumption validated

Task 2: Essential Configuration
  approach: |
    CREATE src/config.py (if needed):
      - IMPLEMENT basic configuration
      - USE hardcoded values for prototype
      - INCLUDE factory pattern TODOs for production
  
  validation:
    - Configuration loads properly
    - Essential settings work

Task 3: Basic Testing & Validation
  approach: |
    CREATE basic tests:
      - VERIFY core functionality works
      - TEST key integration points
      - VALIDATE assumptions
  
  validation:
    - Prototype proves concept
    - Constraints discovered
    - Production path clear
```

### Prototype Implementation Pattern
```python
# Minimal but clean implementation approach
def core_prototype_function(input_data):
    """
    Core prototype functionality - minimal scope but proper structure
    
    TODO for production:
    - Add comprehensive error handling
    - Implement async patterns
    - Add full validation
    - Scale for production load
    """
    
    # Basic validation (essential only)
    if not input_data:
        raise ValueError("Input required")
    
    # Core concept implementation
    # PATTERN: Follow [essential_pattern] but simplified
    result = process_core_functionality(input_data)
    
    # Basic response (minimal structure)
    return {"status": "success", "data": result}

# Production evolution notes:
# - Enhance error handling for edge cases
# - Add async support for scalability  
# - Implement comprehensive validation
# - Add monitoring and logging
```

## Rapid Validation Loop

### Level 1: Basic Validation
```bash
# Quick syntax and basic checks
python -m py_compile src/[prototype_main].py  # Basic syntax check
python src/[prototype_main].py                # Basic execution test

# Expected: No syntax errors, basic functionality works
```

### Level 2: Concept Validation
```python
# CREATE basic_test.py - minimal testing
def test_core_concept():
    """Verify core concept works"""
    result = core_prototype_function("test_input")
    assert result["status"] == "success"
    # Concept proven working

def test_key_assumption():
    """Validate key assumption"""
    # Test the main assumption this prototype is meant to validate
    assert assumption_works()
    # Assumption validated
```

```bash
# Run basic tests
python basic_test.py
# Expected: Core concept proven, key assumptions validated
```

### Level 3: Integration Reality Check
```bash
# Test real-world integration (if applicable)
# Simple curl test or basic integration check
# Focus: Does this actually work with real services/APIs?

# Expected: Real integration works, constraints discovered
```

## Prototype Success Validation
- [ ] **Core Concept**: Minimal functionality proven working
- [ ] **Key Assumption**: Primary assumption validated or refuted
- [ ] **Real Constraints**: Actual limitations discovered through testing
- [ ] **Clean Structure**: Proper patterns maintained despite minimal scope
- [ ] **Production Path**: Clear scaling approach identified

## Production Evolution Notes
```yaml
scaling_requirements:
  - architecture: [How to scale minimal structure to production]
  - features: [Additional functionality needed for production]
  - patterns: [Enhancements needed for production patterns]
  - testing: [Comprehensive testing approach for production]
  
discovered_constraints:
  - technical: [Real limitations discovered during prototype]
  - integration: [Actual challenges found through testing]
  - performance: [Observed characteristics and scaling needs]

next_steps:
  - immediate: [What needs enhancement for production]
  - architecture: [Scaling decisions required]
  - implementation: [Development approach for production version]
```

---

## Prototype Anti-Patterns
- ❌ Don't add comprehensive features (focus on core concept only)
- ❌ Don't skip proper patterns (maintain clean structure)
- ❌ Don't ignore real testing (discover actual constraints)
- ❌ Don't perfectionist implementation (prove concept quickly)
- ❌ Don't skip production notes (plan scaling path)

## Prototype Success Indicators
- ✅ Core concept proven in minimal implementation
- ✅ Real constraints discovered through actual testing
- ✅ Clean patterns maintained while keeping minimal scope
- ✅ Production scaling path clearly identified
- ✅ Development time: 60-120 minutes to working prototype
- ✅ Ready for production workflow with evidence-based context