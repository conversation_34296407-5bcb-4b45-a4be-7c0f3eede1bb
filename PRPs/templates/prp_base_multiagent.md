name: "Multi-Agent PRP Template v2 - Enhanced Context Synthesis with Cross-Validation"
description: |

## Purpose
Template optimized for AI agents implementing features with multi-agent research synthesis, providing comprehensive context from specialist investigations and cross-validated findings for maximum autonomous implementation success.

## Core Principles
1. **Multi-Agent Context Synthesis**: Integrate findings from specialist subagent research
2. **Cross-Validation Framework**: Leverage multiple expert perspectives for higher confidence
3. **Specialist Intelligence**: Organize context from domain-specific investigations
4. **Enhanced Quality Assurance**: Multi-dimensional validation and confidence scoring

---

## Goal
[What needs to be built - be specific about the end state and desires]

## Why
- [Business value and user impact]
- [Integration with existing features]
- [Problems this solves and for whom]

## What
[User-visible behavior and technical requirements]

### Success Criteria
- [ ] [Specific measurable outcomes]

## Multi-Agent Research Synthesis

### Research Quality Matrix
```yaml
multi_agent_confidence:
  overall_score: [8-10]/10
  specialist_assessments:
    implementation_readiness: [X/10] - [specific findings about autonomous feasibility]
    technical_accuracy: [X/10] - [specific findings about technical quality] 
    completeness_coverage: [X/10] - [specific findings about information gaps]
  
  research_depth:
    codebase_patterns: [X examples verified, Y new patterns discovered]
    documentation_research: [X URLs validated, Y additional resources found]
    implementation_requirements: [X specifications added, Y gotchas discovered]
```

### Cross-Validated Research Findings
```yaml
specialist_investigations:
  
  codebase_pattern_analysis:
    verified_examples:
      - file: [path/to/verified_example.py]
        pattern: [specific architectural approach confirmed]
        usage_guidance: [how to apply this pattern]
        specialist_notes: [key insights from codebase investigator]
    
    discovered_patterns:
      - file: [path/to/additional_pattern.py] 
        relevance: [why this pattern applies]
        adaptation_needed: [what to modify for this use case]
        cross_validation: [confirmed by technical accuracy investigator]

  documentation_research:
    validated_resources:
      - url: [verified documentation URL]
        sections: [specific sections confirmed useful]
        implementation_guidance: [how this supports autonomous development]
        specialist_assessment: [quality and relevance rating]
    
    additional_resources:
      - url: [additional resource discovered]
        value: [specific implementation support provided]
        cross_reference: [connection to other research findings]

  implementation_requirements:
    technical_specifications:
      - requirement: [specific technical need identified]
        implementation_approach: [how to address this requirement]
        gotcha_analysis: [potential issues and mitigation]
        confidence_level: [specialist assessment of feasibility]
    
    project_considerations:
      - consideration: [project-specific pattern or standard]
        implementation_details: [exactly how to handle this]
        validation_approach: [how to verify this works]
```

## All Needed Context (Multi-Agent Enhanced)

### Documentation & References (Cross-Validated by Specialists)
```yaml
# MUST READ - Verified by documentation research specialist
primary_documentation:
  - url: [Official API docs URL - validated by specialist]
    why: [Specific sections/methods confirmed essential]
    quality_assessment: [specialist rating of documentation quality]
    implementation_notes: [key insights for autonomous development]
  
  - url: [Library documentation URL - cross-validated]
    section: [Specific section about patterns confirmed relevant]
    critical_insights: [Key insights that prevent common errors]
    specialist_verification: [how this was validated]

verified_examples:
  - file: [path/to/example.py - pattern verified by codebase specialist]
    why: [Pattern to follow confirmed by specialist analysis]
    adaptation_guidance: [specific modifications needed]
    cross_validation: [additional specialist confirmation]
  
  - file: [path/to/additional_example.py - discovered by specialist]
    relevance: [why this example supports implementation]
    usage_pattern: [how to apply this pattern]
    specialist_notes: [key insights from investigation]

ai_documentation:
  - docfile: [PRPs/ai_docs/file.md - content validated by specialists]
    why: [specialist assessment of relevance and quality]
    implementation_support: [how this enables autonomous development]
```

### Current Codebase Overview (Specialist Analysis)
```bash
# Codebase structure analyzed by specialist investigators
[Codebase tree with specialist annotations about relevant patterns]

# Key patterns identified by specialists:
# - [Pattern 1]: [Location and usage guidance]
# - [Pattern 2]: [Relevance and adaptation notes]
# - [Pattern 3]: [Cross-validation findings]
```

### Enhanced Target Architecture (Multi-Agent Designed)
```bash
# Target structure with specialist input
[Enhanced codebase tree with multi-agent research insights]

# File responsibilities (specialist-verified):
# - [file1.py]: [Purpose and pattern confirmed by specialists]
# - [file2.py]: [Integration approach validated by investigators]
# - [file3.py]: [Implementation strategy cross-validated]
```

### Comprehensive Gotchas & Library Intelligence
```python
# CRITICAL INSIGHTS - Discovered through specialist investigation
# [Library name] requirements (validated by technical accuracy specialist):
# - [Specific setup requirement with implementation details]
# - [Configuration gotcha with exact solution]
# - [Version-specific consideration with mitigation approach]

# Project-specific patterns (confirmed by codebase specialist):
# - [Pattern 1]: [Exact implementation approach]
# - [Pattern 2]: [Integration requirement and validation]
# - [Standard 3]: [Compliance approach and testing]

# Cross-validated implementation insights:
# - [Insight 1]: [Confirmed by multiple specialists]
# - [Insight 2]: [Technical accuracy and completeness validated]
# - [Insight 3]: [Implementation readiness confirmed]
```

## Implementation Blueprint (Multi-Agent Orchestrated)

### Data Models and Structure (Specialist-Designed)
```python
# Core data models designed with specialist input
# Examples informed by codebase pattern specialist:
#  - ORM models (following verified patterns)
#  - Pydantic models (using confirmed validation approaches)
#  - Pydantic schemas (with cross-validated structure)
#  - Pydantic validators (implementing discovered best practices)

# Specialist cross-validation notes:
# - [Validation approach confirmed by technical accuracy specialist]
# - [Data structure verified by implementation readiness specialist]
# - [Pattern consistency validated by completeness specialist]
```

### Enhanced Task Sequence (Multi-Agent Coordinated)
```yaml
# Implementation tasks with specialist guidance

Task 1: [Primary component - designed by implementation blueprint specialist]
  approach: |
    MODIFY src/existing_module.py:
      - FIND pattern: "class OldImplementation" (confirmed by codebase specialist)
      - INJECT after line containing "def __init__" (validated approach)
      - PRESERVE existing method signatures (cross-validated requirement)
    
    CREATE src/new_feature.py:
      - MIRROR pattern from: src/similar_feature.py (verified by specialist)
      - MODIFY class name and core logic (following discovered patterns)
      - KEEP error handling pattern identical (confirmed by gotcha analysis)
  
  specialist_validation:
    - implementation_readiness: [specialist assessment]
    - technical_accuracy: [specialist verification]
    - pattern_compliance: [codebase specialist confirmation]

Task 2: [Integration component - cross-validated by multiple specialists]
  approach: |
    [Implementation approach with specialist insights]
  
  specialist_insights:
    - [Key insight from specialist investigation]
    - [Cross-validation finding]
    - [Implementation guidance from specialist]

# Continue for all tasks...
```

### Enhanced Pseudocode (Specialist Intelligence)
```python
# Task 1 - Enhanced with specialist findings
# Pseudocode incorporating multi-agent research insights
async def new_feature(param: str) -> Result:
    # PATTERN: Validated by codebase specialist (see src/validators.py)
    validated = validate_input(param)  # raises ValidationError
    
    # GOTCHA: Discovered by implementation requirements specialist
    # This library requires connection pooling (confirmed by documentation specialist)
    async with get_connection() as conn:  # pattern verified (see src/db/pool.py)
        
        # PATTERN: Cross-validated by multiple specialists
        @retry(attempts=3, backoff=exponential)  # approach confirmed
        async def _inner():
            # CRITICAL: Rate limit discovered by specialist investigation
            # API returns 429 if >10 req/sec (documented in specialist research)
            await rate_limiter.acquire()
            return await external_api.call(validated)
        
        result = await _inner()
    
    # PATTERN: Standardized format confirmed by specialists
    return format_response(result)  # see src/utils/responses.py (validated)

# Specialist cross-validation:
# - Implementation approach confirmed by readiness specialist
# - Technical details verified by accuracy specialist  
# - Error handling validated by completeness specialist
```

### Integration Points (Multi-Agent Validated)
```yaml
DATABASE:
  - migration: "Add column 'feature_enabled' to users table"
    specialist_validation: [confirmed by architecture specialist]
  - index: "CREATE INDEX idx_feature_lookup ON users(feature_id)"
    performance_analysis: [validated by technical accuracy specialist]
  
CONFIG:
  - add_to: config/settings.py
    pattern: "FEATURE_TIMEOUT = int(os.getenv('FEATURE_TIMEOUT', '30'))"
    compliance_check: [confirmed by codebase pattern specialist]
  
ROUTES:
  - add_to: src/api/routes.py  
    pattern: "router.include_router(feature_router, prefix='/feature')"
    integration_validation: [cross-validated by implementation specialist]
```

## Enhanced Validation Loop (Multi-Agent Quality Assurance)

### Level 1: Syntax & Style (Specialist-Verified Commands)
```bash
# Commands validated by specialists for this codebase
ruff check src/new_feature.py --fix  # Auto-fix approach confirmed
mypy src/new_feature.py              # Type checking validated

# Expected results confirmed by specialist investigation:
# - No errors anticipated based on pattern analysis
# - Common issues and solutions pre-identified by specialists
```

### Level 2: Unit Tests (Cross-Validated Patterns)
```python
# Test patterns confirmed by specialist investigation
# CREATE test_new_feature.py following verified approaches:

def test_happy_path():
    """Basic functionality - approach validated by specialists"""
    result = new_feature("valid_input")
    assert result.status == "success"
    # Pattern confirmed by codebase specialist analysis

def test_validation_error():
    """Error handling - cross-validated by multiple specialists"""
    with pytest.raises(ValidationError):
        new_feature("")
    # Exception pattern verified by gotcha specialist

def test_external_api_timeout():
    """Timeout handling - discovered through specialist research"""
    with mock.patch('external_api.call', side_effect=TimeoutError):
        result = new_feature("valid")
        assert result.status == "error"
        assert "timeout" in result.message
    # Edge case identified and validated by specialists
```

```bash
# Test execution with specialist-confirmed expectations
uv run pytest test_new_feature.py -v
# Failure patterns and solutions pre-analyzed by specialists
# Common issues and resolutions documented through multi-agent research
```

### Level 3: Integration Test (Multi-Agent Verified)
```bash
# Integration approach validated by specialist investigation
uv run python -m src.main --dev  # Startup pattern confirmed

# Test commands verified by specialists
curl -X POST http://localhost:8000/feature \
  -H "Content-Type: application/json" \
  -d '{"param": "test_value"}'

# Expected responses based on specialist analysis:
# Success: {"status": "success", "data": {...}}
# Error patterns and diagnostic approaches pre-identified
```

## Multi-Agent Validation Checklist
- [ ] **Specialist Research Synthesis**: All multi-agent findings integrated
- [ ] **Cross-Validation Complete**: Multiple specialist perspectives confirmed
- [ ] **Pattern Compliance**: Codebase specialist verification passed
- [ ] **Technical Accuracy**: Documentation and implementation verified
- [ ] **Implementation Readiness**: Autonomous development confidence ≥8/10
- [ ] **Completeness Assessment**: Gap analysis and coverage validation
- [ ] **Quality Gates**: All validation loops confirmed by specialists

## Final Validation (Enhanced Confidence)
- [ ] All tests pass: `uv run pytest tests/ -v` (pattern validated)
- [ ] No linting errors: `uv run ruff check src/` (approach confirmed)
- [ ] No type errors: `uv run mypy src/` (type strategy verified)
- [ ] Manual test successful: [specialist-validated command]
- [ ] Error cases handled: [cross-validated by specialists]
- [ ] Logs informative: [logging pattern confirmed]
- [ ] Documentation updated: [completeness validated]

---

## Multi-Agent Anti-Patterns
- ❌ Don't ignore specialist cross-validation findings
- ❌ Don't skip multi-dimensional confidence assessment  
- ❌ Don't use single-perspective analysis when multi-agent insights available
- ❌ Don't bypass specialist-verified patterns for untested approaches
- ❌ Don't ignore gotchas discovered through specialist investigation
- ❌ Don't skip validation of multi-agent research synthesis

## Enhanced Success Indicators
- ✅ Multi-agent research synthesis comprehensive and cross-validated
- ✅ Specialist findings integrated and actionable for autonomous development
- ✅ Confidence scores ≥8/10 across all specialist assessments
- ✅ Implementation approach validated by multiple expert perspectives
- ✅ Context organization optimized for AI agent consumption
- ✅ Quality assurance enhanced through specialist cross-validation