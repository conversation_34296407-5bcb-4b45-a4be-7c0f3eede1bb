name: "Multi-Agent Planning PRP Template - Enhanced PRD Generation with Cross-Validated Research"
description: |

## Purpose
Generate comprehensive Product Requirements Documents (PRDs) through multi-agent research synthesis, combining specialist investigations across market analysis, technical architecture, user experience, and implementation strategy for maximum planning confidence and autonomous implementation readiness.

## Philosophy
1. **Multi-Agent Research First**: Leverage specialist parallel investigation
2. **Cross-Validated Intelligence**: Synthesize findings from expert perspectives
3. **Visual Thinking Enhanced**: Use diagrams validated by multiple specialists
4. **Implementation-Ready Output**: PRDs optimized for autonomous development success

---

## Initial Concept
$ARGUMENTS

## Multi-Agent Planning Process

### Phase 1: Research Synthesis & Cross-Validation

#### Multi-Agent Research Assessment
```yaml
research_quality_matrix:
  overall_confidence: [8-10]/10
  specialist_investigations:
    market_technical_feasibility: [X/10] - [solutions researched, approaches evaluated]
    architecture_integration: [X/10] - [integration points, components designed]
    user_experience_requirements: [X/10] - [user flows, success criteria defined]
    implementation_strategy: [X/10] - [phases planned, risks identified]

cross_validation_summary:
  research_consistency: [findings alignment across specialists]
  technical_feasibility: [confirmed by multiple expert perspectives]
  implementation_confidence: [validated autonomous development readiness]
```

#### Comprehensive Research Findings
```yaml
market_technical_research:
  competitive_analysis:
    - solution: [competitor/approach identified]
      technical_approach: [implementation pattern discovered]
      advantages: [benefits confirmed by specialist]
      applicability: [relevance to our requirements]
      specialist_assessment: [feasibility and adoption recommendation]
  
  technical_patterns:
    - pattern: [proven technical approach]
      libraries_frameworks: [specific tools recommended]
      performance_considerations: [scalability and efficiency insights]
      implementation_complexity: [specialist complexity assessment]
      cross_validation: [confirmed by architecture specialist]

architecture_integration_research:
  system_requirements:
    - component: [system component identified]
      integration_points: [connections with existing systems]
      data_flow_design: [information architecture approach]
      technical_dependencies: [required technical foundations]
      specialist_verification: [architecture specialist confirmation]
  
  design_decisions:
    - decision: [architectural choice required]
      options_evaluated: [alternatives considered by specialist]
      recommended_approach: [specialist recommendation with reasoning]
      implementation_impact: [development and maintenance implications]
      cross_validation: [confirmed by implementation strategy specialist]

user_experience_research:
  user_personas:
    - persona: [user type identified]
      context: [usage scenarios and environments]
      goals: [primary objectives and success criteria]
      pain_points: [current challenges and friction]
      specialist_insights: [UX specialist recommendations]
  
  journey_mapping:
    - flow: [user workflow identified]
      touchpoints: [interaction points and decision nodes]
      success_paths: [optimal user experience design]
      edge_cases: [alternative scenarios and error conditions]
      cross_validation: [confirmed by technical architecture specialist]

implementation_strategy_research:
  development_approach:
    - phase: [implementation phase defined]
      objectives: [specific goals and deliverables]
      dependencies: [technical and resource requirements]
      risk_assessment: [potential challenges and mitigation]
      specialist_validation: [confirmed by architecture and UX specialists]
  
  resource_planning:
    - requirement: [development resource need]
      complexity_assessment: [implementation difficulty evaluation]
      timeline_estimate: [development duration projection]
      validation_approach: [quality assurance and testing strategy]
```

### Phase 2: Enhanced PRD Structure Generation

#### 1. Executive Summary (Multi-Agent Synthesis)
```markdown
## Problem Statement
[Clear articulation based on market research and user experience findings]
- Market Context: [from competitive analysis specialist]
- User Pain Points: [from UX research specialist]
- Technical Opportunity: [from architecture specialist]

## Solution Overview
[High-level description integrating all specialist perspectives]
- Technical Approach: [from architecture and market research specialists]
- User Experience Strategy: [from UX specialist with technical validation]
- Implementation Philosophy: [from implementation strategy specialist]

## Success Metrics (Cross-Validated)
- Business Metrics: [from market research with UX validation]
- Technical KPIs: [from architecture specialist with implementation validation]
- User Satisfaction: [from UX specialist with feasibility confirmation]
```

#### 2. User Stories & Experience Design (Specialist-Driven)
```markdown
## Primary User Flow (UX Specialist Design, Technically Validated)
\```mermaid
graph LR
    A[User Entry Point] --> B{Decision Node}
    B -->|Primary Path| C[Core Functionality]
    B -->|Alternative| D[Secondary Flow]
    C --> E[Success State]
    D --> E
    
    %% Annotations from specialist research
    %% A: Entry validated by UX specialist
    %% B: Decision logic confirmed by architecture specialist
    %% C: Implementation feasibility verified by strategy specialist
\```

## Detailed User Stories (Multi-Agent Validated)
### Story 1: [Primary Use Case]
**As a** [user type from UX research] **I want to** [functionality from technical research] **so that** [benefit from market analysis]

**Acceptance Criteria** (Cross-Validated):
- [ ] [Criterion validated by UX specialist]
- [ ] [Technical requirement confirmed by architecture specialist]
- [ ] [Implementation feasibility verified by strategy specialist]

**Edge Cases** (Comprehensive Specialist Analysis):
- [Edge case identified by UX specialist]: [technical solution from architecture specialist]
- [Technical edge case from architecture specialist]: [UX handling from experience specialist]
- [Implementation challenge from strategy specialist]: [mitigation confirmed by other specialists]

### Technical User Flow Validation
\```mermaid
sequenceDiagram
    participant U as User
    participant UI as Interface
    participant API as Backend
    participant DB as Database
    participant EXT as External Services
    
    Note over U,EXT: Flow validated by all specialists
    U->>UI: Action (UX specialist design)
    UI->>API: Request (architecture specialist design)
    API->>DB: Query (technical validation)
    API->>EXT: External Call (integration specialist validation)
    EXT-->>API: Response (error handling validated)
    API-->>UI: Result (performance validated)
    UI-->>U: Display (UX optimization confirmed)
\```
```

#### 3. System Architecture (Multi-Specialist Design)
```markdown
## High-Level Architecture (Cross-Validated Design)
\```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[User Interface - UX Specialist Design]
        State[State Management - Architecture Specialist]
    end
    
    subgraph "Backend Services"
        API[API Layer - Architecture Specialist]
        BL[Business Logic - Implementation Specialist]
        Cache[Caching Layer - Performance Specialist]
    end
    
    subgraph "Data Layer"
        DB[(Primary Database - Architecture)]
        Search[(Search Index - Technical Research)]
    end
    
    subgraph "External Integration"
        EXT[External Services - Market Research]
        Queue[Message Queue - Implementation Strategy]
    end
    
    UI --> API
    API --> BL
    BL --> DB
    BL --> Cache
    BL --> EXT
    API --> Queue
    State --> UI
    
    %% Cross-validation annotations
    %% Performance validated by implementation specialist
    %% Security confirmed by architecture specialist
    %% Scalability verified by market research specialist
\```

## Component Breakdown (Specialist-Designed)
### Frontend Components (UX + Technical Validation)
- **[Component 1]**: [Purpose from UX specialist] | [Technical approach from architecture specialist]
- **[Component 2]**: [User experience design] | [Implementation strategy validation]

### Backend Services (Architecture + Implementation Validation)
- **[Service 1]**: [Technical purpose from architecture specialist] | [Implementation complexity from strategy specialist]
- **[Service 2]**: [Integration design] | [Performance validation from market research]

### Data Models (Multi-Specialist Design)
- **[Model 1]**: [Structure from architecture specialist] | [Usage patterns from UX specialist] | [Implementation approach from strategy specialist]
- **[Model 2]**: [Technical design] | [User experience impact] | [Development complexity assessment]
```

#### 4. Enhanced Technical Specifications
```markdown
## API Design (Architecture Specialist + Implementation Validation)
\```mermaid
sequenceDiagram
    participant Client as Client Application
    participant Gateway as API Gateway
    participant Auth as Authentication
    participant Service as Business Service
    participant Cache as Cache Layer
    participant DB as Database
    participant External as External API
    
    Note over Client,External: Sequence validated by all specialists
    
    Client->>Gateway: API Request
    Gateway->>Auth: Verify Token
    Auth-->>Gateway: Validation Result
    Gateway->>Service: Authenticated Request
    Service->>Cache: Check Cache
    alt Cache Hit
        Cache-->>Service: Cached Data
    else Cache Miss
        Service->>DB: Database Query
        DB-->>Service: Query Result
        Service->>Cache: Update Cache
    end
    
    opt External Data Needed
        Service->>External: External API Call
        External-->>Service: External Response
    end
    
    Service-->>Gateway: Service Response
    Gateway-->>Client: API Response
\```

## Endpoints (Cross-Validated Specifications)
### Primary Endpoints (Architecture + UX + Implementation Validation)
- **POST /api/[resource]** (Architecture Specialist Design)
  - Request Schema: [validated by implementation specialist]
  - Response Format: [optimized by UX specialist]
  - Error Handling: [comprehensive strategy from all specialists]
  - Performance Requirements: [validated by market research specialist]

## Data Flow Architecture (Multi-Specialist Validation)
\```mermaid
flowchart TD
    A[Input Data - UX Validated] --> B{Validation Layer - Technical}
    B -->|Valid| C[Processing Engine - Architecture]
    B -->|Invalid| D[Error Handling - UX Design]
    C --> E[Transform Layer - Implementation]
    E --> F[Persistence Layer - Architecture]
    F --> G[Response Generation - UX]
    G --> H[Client Delivery - Performance]
    
    %% Cross-validation notes
    %% Performance validated by implementation specialist
    %% Error UX confirmed by experience specialist
    %% Security verified by architecture specialist
\```
```

#### 5. Implementation Strategy (Multi-Agent Coordination)
```markdown
## Development Phases (Implementation Specialist + Cross-Validation)
\```mermaid
graph LR
    subgraph "Phase 1: Foundation"
        A1[Infrastructure Setup]
        A2[Core Architecture]
        A3[Authentication Framework]
    end
    
    subgraph "Phase 2: Core Features"
        B1[Primary User Flows]
        B2[Business Logic]
        B3[API Development]
    end
    
    subgraph "Phase 3: Integration"
        C1[External Services]
        C2[Advanced Features]
        C3[Performance Optimization]
    end
    
    subgraph "Phase 4: Production"
        D1[Testing & QA]
        D2[Documentation]
        D3[Deployment]
    end
    
    A1 --> A2 --> A3
    A3 --> B1 --> B2 --> B3
    B3 --> C1 --> C2 --> C3
    C3 --> D1 --> D2 --> D3
    
    %% Specialist validation annotations
    %% Phase dependencies confirmed by implementation specialist
    %% Technical feasibility validated by architecture specialist
    %% User impact assessed by UX specialist
    %% Market timing confirmed by market research specialist
\```

## Implementation Priority Matrix (Multi-Specialist Assessment)
### Phase 1: Foundation (Architecture + Implementation Specialists)
- **Core Infrastructure**: [technical requirements from architecture specialist]
- **Development Framework**: [implementation approach from strategy specialist]
- **Quality Foundation**: [validation strategy cross-confirmed]

### Phase 2: MVP Features (UX + Technical Validation)
- **Essential User Flows**: [prioritized by UX specialist, validated technically]
- **Core Business Logic**: [designed by architecture, confirmed by implementation]
- **Basic Integration**: [external dependencies identified and validated]

### Phase 3: Enhanced Features (Market + Implementation Validation)
- **Advanced Capabilities**: [market research priorities with technical feasibility]
- **Performance Optimization**: [implementation specialist recommendations]
- **User Experience Polish**: [UX specialist enhancements with technical validation]
```

### Phase 3: Risk Assessment & Validation (Multi-Agent Analysis)

#### Comprehensive Risk Matrix (All Specialists)
```yaml
technical_risks:
  - risk: "Performance at scale"
    likelihood: [assessment from implementation specialist]
    impact: [evaluation from architecture specialist]
    mitigation: [strategy validated by all specialists]
    responsible_specialist: [architecture + implementation]
  
  - risk: "External API reliability"
    likelihood: [market research assessment]
    impact: [UX and technical impact evaluation]
    mitigation: [comprehensive fallback strategy]
    cross_validation: [confirmed by implementation and architecture specialists]

business_risks:
  - risk: "User adoption challenges"
    likelihood: [UX specialist assessment]
    impact: [market research evaluation]
    mitigation: [user experience strategy with technical validation]
    validation_approach: [UX testing with implementation feedback]
  
  - risk: "Scope creep"
    likelihood: [implementation specialist assessment]
    impact: [all specialists impact evaluation]
    mitigation: [strict MVP definition with cross-specialist agreement]

integration_risks:
  - risk: "System integration complexity"
    likelihood: [architecture specialist assessment]
    impact: [implementation and UX impact evaluation]
    mitigation: [phased integration with validation checkpoints]
    specialist_coordination: [architecture + implementation + UX validation]
```

#### Success Criteria (Cross-Validated)
```markdown
## Definition of Done (Multi-Specialist Validation)
- [ ] **Technical Excellence**: All architecture specialist requirements met
- [ ] **User Experience**: UX specialist success criteria achieved  
- [ ] **Implementation Quality**: Strategy specialist standards fulfilled
- [ ] **Market Validation**: Competitive analysis benchmarks exceeded
- [ ] **Cross-Integration**: All specialist requirements integrated successfully

## Measurable Outcomes (Specialist-Defined)
- **Technical Performance**: [metrics from architecture specialist]
- **User Satisfaction**: [criteria from UX specialist]  
- **Implementation Efficiency**: [benchmarks from strategy specialist]
- **Market Competitiveness**: [standards from market research specialist]
```

### Phase 4: Enhanced Validation & Output

#### Multi-Agent Pre-Implementation Checklist
```yaml
specialist_validation:
  market_technical_feasibility:
    - [ ] Technical approaches validated and competitive
    - [ ] Implementation libraries and frameworks confirmed
    - [ ] Performance benchmarks established
    - [ ] Competitive analysis complete with differentiation strategy
  
  architecture_integration:
    - [ ] System architecture designed and validated
    - [ ] Integration points identified and tested
    - [ ] Data models designed with performance validation
    - [ ] Technical dependencies mapped and confirmed
  
  user_experience_requirements:
    - [ ] User personas defined with market validation
    - [ ] User journeys mapped with technical feasibility confirmed
    - [ ] Success criteria established with measurable outcomes
    - [ ] Edge cases identified with technical solutions
  
  implementation_strategy:
    - [ ] Development phases planned with resource validation
    - [ ] Risk assessment complete with mitigation strategies
    - [ ] Timeline estimates validated by technical complexity
    - [ ] Quality assurance approach confirmed by all specialists

cross_validation_complete:
  - [ ] Technical feasibility confirmed across all user requirements
  - [ ] User experience validated against technical constraints  
  - [ ] Implementation strategy aligned with architecture design
  - [ ] Market requirements satisfied by technical solution
  - [ ] All specialist findings integrated and consistent
```

#### Enhanced Output Format
The final PRD structure integrates all specialist research:

1. **Executive Summary** (Multi-agent synthesis)
2. **User Experience Design** (UX specialist with technical validation)
3. **Technical Architecture** (Architecture specialist with implementation validation)
4. **Implementation Strategy** (Implementation specialist with market validation)
5. **Risk Assessment** (All specialists with cross-validation)
6. **Appendices** (Comprehensive specialist research findings)

### Multi-Agent Validation Commands

```bash
# Verify PRD completeness across all specialist domains
grep -E "(TODO|TBD|FIXME)" generated_prd.md

# Validate specialist research integration
python validate_multiagent_prd.py generated_prd.md

# Check cross-validation consistency
python check_specialist_alignment.py generated_prd.md

# Verify diagram accuracy across specialist domains
mermaid-cli -i generated_prd.md -o multiagent_prd_diagrams.pdf
```

## Enhanced Anti-Patterns
- ❌ Single-specialist perspective without cross-validation
- ❌ Technical solutions without user experience validation
- ❌ User requirements without technical feasibility confirmation
- ❌ Implementation plans without architecture specialist validation
- ❌ Market research without technical implementation assessment
- ❌ Missing cross-specialist consistency verification

## Multi-Agent Success Indicators
- ✅ All four specialist domains thoroughly researched and integrated
- ✅ Cross-validation complete with specialist agreement on feasibility
- ✅ Technical architecture validated by user experience requirements
- ✅ Implementation strategy confirmed by market research and technical constraints
- ✅ Risk assessment comprehensive with specialist-validated mitigation strategies
- ✅ PRD enables autonomous implementation with 8-10/10 confidence
- ✅ All specialist research findings synthesized and actionable

## Template Usage Example

**Input**: "Build a real-time notification system for our app"

**Multi-Agent Output would include**:
- **Market Research**: Competitive notification platforms, technical approaches, performance benchmarks
- **Architecture Design**: Pub/sub patterns, real-time delivery systems, scalability architecture  
- **User Experience**: Notification preferences, delivery timing, user control interfaces
- **Implementation Strategy**: Development phases, technology selection, rollout approach
- **Cross-Validation**: UX requirements validated against technical constraints, performance needs confirmed by market research
- **Risk Assessment**: Technical scalability, user adoption, integration complexity with specialist mitigation strategies

The resulting PRD becomes optimally structured input for `/create-base-prp` with comprehensive multi-agent research synthesis.