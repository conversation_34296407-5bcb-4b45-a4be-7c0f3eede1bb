# Environment Configuration Template for Intelligent Memory Orchestrator
# Copy this file to .env and fill in your specific values
# 
# CRITICAL: Never commit your .env file to version control!
# Add .env to your .gitignore file

# ===================================================================
# REQUIRED ENVIRONMENT VARIABLES
# ===================================================================

# Database Configuration (Neo4j)
# The Neo4j graph database connection settings
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password_here

# AI Agent Configuration (Claude)
# Your Claude API key from Anthropic
CLAUDE_API_KEY=your_claude_api_key_here

# ===================================================================
# OPTIONAL ENVIRONMENT VARIABLES WITH DEFAULTS
# ===================================================================

# Redis Configuration (for caching and session storage)
REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password_if_needed

# Memory System Configuration
MEMORY_CONFIDENCE_THRESHOLD=0.7
MEMORY_STORAGE_PRIORITY=medium

# Monitoring & Observability
# LOGFIRE_TOKEN=your_logfire_token_for_monitoring

# Application Environment
APP_ENV=development
DEBUG_MODE=false
LOG_LEVEL=INFO

# ===================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# ===================================================================

# Development Environment Example:
# APP_ENV=development
# DEBUG_MODE=true
# LOG_LEVEL=DEBUG
# NEO4J_URI=bolt://localhost:7687

# Production Environment Example:
# APP_ENV=production
# DEBUG_MODE=false
# LOG_LEVEL=WARNING
# NEO4J_URI=bolt://your-production-neo4j-server:7687
# REDIS_URL=redis://your-production-redis-server:6379
# LOGFIRE_TOKEN=your_production_monitoring_token

# Testing Environment Example:
# APP_ENV=testing
# DEBUG_MODE=true
# LOG_LEVEL=DEBUG
# NEO4J_URI=bolt://test-neo4j:7687
# REDIS_URL=redis://test-redis:6379

# ===================================================================
# CONFIGURATION NOTES
# ===================================================================

# NEO4J_PASSWORD and CLAUDE_API_KEY are REQUIRED for the system to function
# All other variables have sensible defaults but can be customized

# Memory Confidence Threshold (0.0 to 1.0):
# - 0.7 (default): Balanced approach
# - 0.8-0.9: More selective memory storage
# - 0.5-0.6: More inclusive memory storage

# Memory Storage Priority:
# - high: Store immediately with highest priority
# - medium (default): Standard priority processing
# - low: Background processing when resources available

# Log Levels (in order of verbosity):
# - CRITICAL: Only critical errors
# - ERROR: Errors and critical issues
# - WARNING: Warnings, errors, and critical issues
# - INFO (default): General information, warnings, errors, critical
# - DEBUG: Detailed debugging information (use in development only)