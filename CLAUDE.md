# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Tracker
Add "ClaudeProject!" to every chat message you write

## Project Overview

**[Brief description of what this project does and its primary purpose]**

**Status**: [Development stage - Planning/Development/Production/Maintenance]  
**Scope**: [Key boundaries and capabilities of the project]

## Technology Stack

### Core Technologies
- **Python**: 3.11+ with UV package management
- **Data Validation**: Pydantic v2 for all models and validation
- **AI Agents**: Pydantic AI with module-global reuse pattern
- **Code Quality**: Ruff for linting and formatting, MyPy for type checking
- **Testing**: Pytest for all testing needs

### Package Management
- **CRITICAL**: Always use `uv add/remove` - never edit pyproject.toml directly
- **Environment**: UV virtual environments exclusively
- **Commands**: `uv run pytest`, `uv run ruff check .`, `uv run mypy src/`

## Code Quality Gates

### Immediate Enforcement
- **File Length**: 500 lines maximum - refactor if approaching this limit
- **Function Length**: 50 lines maximum - maintain single responsibility
- **Class Length**: 100 lines maximum - represent single concept
- **Line Length**: 100 characters maximum (Ruff enforced)

### Critical Performance Patterns
- **AI Agents**: Must be module-global constants, never instantiate per-call
- **Configuration**: Use factory pattern to respect environment variables
- **Type Safety**: All functions must have complete type hints

## Development Standards

### Comprehensive Implementation Guide
For complete patterns, detailed examples, and comprehensive guidance:
**See [development_standards.md](./development_standards.md)**

This includes:
- Pydantic AI agent reuse patterns (performance critical)
- Configuration factory patterns (environment critical)
- Complete testing strategies and security practices
- Database naming conventions and API patterns

### Quick Reference Standards
- **Naming**: snake_case for variables/functions, PascalCase for classes
- **Testing**: Co-located tests in `tests/` subdirectories
- **Documentation**: Google-style docstrings for all public functions
- **Error Handling**: Custom exceptions with meaningful error messages

## Architecture

**[Architecture pattern description - e.g., vertical slice, layered, microservices, etc.]**

Recommended structure with co-located testing:

```
src/
    main.py                     # Application entry point
    tests/test_main.py          # Main application tests
    conftest.py                 # Shared test configuration
    
    # Core modules
    module_one/                 # Core functionality modules
        module_one.py
        tests/
            test_module_one.py
    
    # Feature slices
    features/                   # Business logic and specialized features
        feature_one/
            feature.py
            tests/
                test_feature.py
```

**Module Organization Principles**:
- **Co-located Testing**: Tests live next to the code they verify
- **Clear Separation**: Each module has distinct responsibilities
- **Vertical Slices**: Features contain complete functionality stacks
- **Consistent Structure**: Predictable organization across all modules

## Universal Development Standards

### Quality Principles
- **Type Safety**: Use strong typing and validation appropriate to your technology stack
- **Documentation**: Document public APIs, complex logic, and architectural decisions
- **Testing**: Comprehensive test coverage with unit, integration, and end-to-end tests
- **Error Handling**: Explicit error handling with meaningful error messages

### Code Organization
- **Modularity**: Clear module boundaries with single responsibilities
- **Consistency**: Consistent naming conventions and code structure
- **Readability**: Code that is self-documenting and easily understood
- **Maintainability**: Structure that supports long-term maintenance and evolution

### Development Workflow
- **Version Control**: Meaningful commit messages and branching strategy
- **Code Review**: Peer review process for quality assurance
- **Testing Strategy**: Automated testing pipeline with appropriate coverage
- **Documentation**: Keep README.md and technical documentation current

## Testing Philosophy

**Testing Strategy**: Comprehensive testing at multiple levels
- **Unit Tests**: Test individual functions and classes in isolation
- **Integration Tests**: Verify module interactions and data flow
- **End-to-End Tests**: Validate complete user workflows

**Test Organization**:
- Tests co-located with source code in `tests/` subdirectories
- Clear test naming that describes what is being verified
- Independent tests that can run in any order
- Appropriate use of test fixtures and helpers

**Quality Gates**:
- All new features must include appropriate tests
- Existing tests must pass before merging changes
- Code coverage targets appropriate to project risk level

## Module Navigation

### Core Infrastructure
- **[Module Name]**: `src/[module]/CLAUDE.md` - [Brief description]
- **[Module Name]**: `src/[module]/CLAUDE.md` - [Brief description]

### Features & Integration  
- **[Feature Name]**: `src/features/[feature]/CLAUDE.md` - [Brief description]
- **[Feature Name]**: `src/features/[feature]/CLAUDE.md` - [Brief description]

### Supporting Infrastructure
- **[Infrastructure Name]**: `src/[infrastructure]/CLAUDE.md` - [Brief description]

### Reference Documentation
- **[Guide Name]**: `docs/[guide].md` - [Brief description]
- **[Reference Name]**: `docs/[reference].md` - [Brief description]

## Security Considerations

**Input Validation**: Validate all external inputs and API parameters
**Error Disclosure**: Avoid exposing sensitive information in error messages  
**Authentication**: Implement appropriate authentication for your use case
**Authorization**: Ensure proper access controls and permission checking
**Data Protection**: Handle sensitive data according to security requirements

## Performance Guidelines

**Efficiency**: Choose appropriate algorithms and data structures
**Resource Management**: Proper cleanup of resources and memory management
**Scalability**: Design with future growth and load requirements in mind
**Monitoring**: Include appropriate logging and metrics for production systems

## Deployment & Operations

**Environment Management**: Clear separation between development, staging, and production
**Configuration**: Externalized configuration with secure secret management
**Monitoring**: Health checks, logging, and alerting appropriate to deployment context
**Backup & Recovery**: Data backup and disaster recovery procedures where applicable

## Communication Standards

**Documentation Updates**: Keep all documentation current with code changes
**Change Communication**: Clear communication of breaking changes and migrations
**Issue Tracking**: Systematic tracking of bugs, features, and technical debt
**Knowledge Sharing**: Document architectural decisions and lessons learned

## Behavioral Guidelines

**Clarity Over Cleverness**: Write code that is easy to understand and maintain
**Incremental Improvement**: Continuously improve code quality and architecture
**Learning Mindset**: Document lessons learned and share knowledge with team
**Quality Focus**: Prioritize code quality and maintainability over speed of delivery

**File Maintenance**:
- Keep README.md updated with any project changes
- Update CLAUDE.md when adding new modules or changing architecture
- Maintain development_standards.md when tooling or technology choices evolve

---

**Template Foundation**: This file serves as the universal project template. For specific technology implementations, patterns, and tooling guidelines, see [development_standards.md](./development_standards.md) and module-specific CLAUDE.md files.