"""Custom exceptions for the Memory Orchestrator system."""

from typing import Any, Optional


class ConfigurationError(Exception):
    """Base exception for configuration-related errors.
    
    This is the base class for all configuration-related exceptions
    in the Memory Orchestrator system.
    """
    pass


class EnvironmentVariableError(ConfigurationError):
    """Raised when required environment variables are missing or invalid.
    
    This exception is raised when the system cannot find required
    environment variables or when their values are invalid.
    """
    
    def __init__(self, variable_name: str, message: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            variable_name: Name of the problematic environment variable
            message: Optional custom error message
        """
        self.variable_name = variable_name
        self.message = message or (
            f"Environment variable '{variable_name}' is required but not set"
        )
        super().__init__(self.message)


class ValidationError(ConfigurationError):
    """Raised when configuration validation fails.
    
    This exception is raised when configuration values fail validation
    rules or constraints.
    """
    
    def __init__(self, field_name: str, value: Any, message: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            field_name: Name of the field that failed validation
            value: The invalid value
            message: Optional custom error message
        """
        self.field_name = field_name
        self.value = value
        self.message = message or (
            f"Validation failed for field '{field_name}' with value '{value}'"
        )
        super().__init__(self.message)


class ConfigurationFactoryError(ConfigurationError):
    """Raised when configuration factory operations fail.
    
    This exception is raised when factory methods fail to create
    valid configuration objects.
    """
    
    def __init__(self, factory_name: str, message: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            factory_name: Name of the factory that failed
            message: Optional custom error message
        """
        self.factory_name = factory_name
        self.message = message or (
            f"Configuration factory '{factory_name}' operation failed"
        )
        super().__init__(self.message)


class PerformanceError(ConfigurationError):
    """Raised when configuration operations exceed performance thresholds.
    
    This exception is raised when configuration operations take longer
    than expected performance thresholds.
    """
    
    def __init__(self, operation: str, duration: float, threshold: float):
        """Initialize the exception.
        
        Args:
            operation: Name of the operation that exceeded threshold
            duration: Actual duration in seconds
            threshold: Expected threshold in seconds
        """
        self.operation = operation
        self.duration = duration
        self.threshold = threshold
        self.message = (
            f"Operation '{operation}' took {duration:.3f}s, "
            f"exceeding threshold of {threshold:.3f}s"
        )
        super().__init__(self.message)


class MemoryError(Exception):
    """Base exception for memory system operations.
    
    This is the base class for all memory-related exceptions
    in the Memory Orchestrator system.
    """
    pass


class MemoryStorageError(MemoryError):
    """Raised when memory storage operations fail.
    
    This exception is raised when the system fails to store
    memories in the underlying storage system.
    """
    
    def __init__(self, operation: str, details: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            operation: Description of the failed storage operation
            details: Optional additional error details
        """
        self.operation = operation
        self.details = details
        self.message = f"Memory storage operation failed: {operation}"
        if details:
            self.message += f" - {details}"
        super().__init__(self.message)


class MemoryRetrievalError(MemoryError):
    """Raised when memory retrieval operations fail.
    
    This exception is raised when the system fails to retrieve
    memories from the underlying storage system.
    """
    
    def __init__(self, query: str, details: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            query: Description of the failed retrieval query
            details: Optional additional error details
        """
        self.query = query
        self.details = details
        self.message = f"Memory retrieval operation failed: {query}"
        if details:
            self.message += f" - {details}"
        super().__init__(self.message)


class AgentError(Exception):
    """Base exception for AI agent operations.
    
    This is the base class for all AI agent-related exceptions
    in the Memory Orchestrator system.
    """
    pass


class AgentInitializationError(AgentError):
    """Raised when AI agent initialization fails.
    
    This exception is raised when the system fails to properly
    initialize AI agents with their required configuration.
    """
    
    def __init__(self, agent_type: str, details: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            agent_type: Type of agent that failed to initialize
            details: Optional additional error details
        """
        self.agent_type = agent_type
        self.details = details
        self.message = f"Agent initialization failed for type '{agent_type}'"
        if details:
            self.message += f" - {details}"
        super().__init__(self.message)


class AgentCommunicationError(AgentError):
    """Raised when AI agent communication fails.
    
    This exception is raised when agents fail to communicate
    with external services or other system components.
    """
    
    def __init__(self, agent_type: str, service: str, details: Optional[str] = None):
        """Initialize the exception.
        
        Args:
            agent_type: Type of agent experiencing communication issues
            service: Name of the service that failed to communicate
            details: Optional additional error details
        """
        self.agent_type = agent_type
        self.service = service
        self.details = details
        self.message = (
            f"Agent communication failed: {agent_type} -> {service}"
        )
        if details:
            self.message += f" - {details}"
        super().__init__(self.message)