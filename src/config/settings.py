"""Application settings with environment variable validation."""

from pydantic_settings import BaseSettings
from typing import Optional


class AppSettings(BaseSettings):
    """Application settings with environment variable validation.
    
    This class provides configuration management for the entire Intelligent
    Memory Orchestrator system with proper environment variable precedence
    and validation.
    
    Environment variables are the authoritative source for configuration.
    See .env.template for all required and optional variables.
    """
    
    # Database Configuration
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str
    
    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # AI Agent Configuration
    CLAUDE_API_KEY: str
    MEMORY_CONFIDENCE_THRESHOLD: float = 0.7
    MEMORY_STORAGE_PRIORITY: str = "medium"
    
    # Monitoring Configuration
    LOGFIRE_TOKEN: Optional[str] = None
    
    # Application Configuration
    APP_ENV: str = "development"
    DEBUG_MODE: bool = False
    LOG_LEVEL: str = "INFO"
    
    class Config:
        """Pydantic configuration for settings."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        
    def validate_configuration(self) -> bool:
        """Validate configuration consistency and requirements.
        
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        if not (0.0 <= self.MEMORY_CONFIDENCE_THRESHOLD <= 1.0):
            raise ValueError(
                f"MEMORY_CONFIDENCE_THRESHOLD must be between 0.0 and 1.0, "
                f"got {self.MEMORY_CONFIDENCE_THRESHOLD}"
            )
            
        if self.MEMORY_STORAGE_PRIORITY not in ["high", "medium", "low"]:
            raise ValueError(
                f"MEMORY_STORAGE_PRIORITY must be 'high', 'medium', or 'low', "
                f"got '{self.MEMORY_STORAGE_PRIORITY}'"
            )
            
        if self.LOG_LEVEL not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError(
                f"LOG_LEVEL must be a valid logging level, "
                f"got '{self.LOG_LEVEL}'"
            )
            
        return True


def get_settings() -> AppSettings:
    """Get settings instance.
    
    This function creates a new AppSettings instance each time it's called,
    ensuring that environment variable changes are always respected.
    Caching has been removed for development phase to ensure proper testing
    and environment variable authority.
    
    Returns:
        AppSettings: Fresh settings instance from environment
    """
    settings = AppSettings()  # type: ignore[call-arg]
    settings.validate_configuration()
    return settings