"""Configuration factories following the factory pattern for environment respect."""

from pydantic import BaseModel, Field, ConfigDict
from typing import Protocol, Optional, Any
from .exceptions import ConfigurationFactoryError


class ConfigProtocol(Protocol):
    """Protocol defining the configuration interface for factories.
    
    This protocol ensures that configuration factories can work with
    any configuration source that provides the required attributes.
    """
    NEO4J_URI: str
    NEO4J_USER: str
    NEO4J_PASSWORD: str
    REDIS_URL: str
    REDIS_PASSWORD: Optional[str]
    CLAUDE_API_KEY: str
    MEMORY_CONFIDENCE_THRESHOLD: float
    MEMORY_STORAGE_PRIORITY: str
    LOGFIRE_TOKEN: Optional[str]
    APP_ENV: str
    DEBUG_MODE: bool
    LOG_LEVEL: str


class DatabaseConfig(BaseModel):
    """Database configuration model without hardcoded defaults.
    
    This model represents database connection configuration that
    respects environment variables through the factory pattern.
    """
    uri: str = Field(description="Neo4j database URI")
    user: str = Field(description="Database username")
    password: str = Field(description="Database password")
    max_connection_pool_size: int = Field(
        default=10,
        description="Maximum number of connections in the pool"
    )
    connection_timeout: int = Field(
        default=30,
        description="Connection timeout in seconds"
    )
    
    model_config = ConfigDict(frozen=True)


class RedisConfig(BaseModel):
    """Redis configuration model without hardcoded defaults.
    
    This model represents Redis connection configuration that
    respects environment variables through the factory pattern.
    """
    url: str = Field(description="Redis connection URL")
    password: Optional[str] = Field(default=None, description="Redis password")
    max_connections: int = Field(
        default=10,
        description="Maximum number of connections in the pool"
    )
    socket_timeout: int = Field(
        default=30,
        description="Socket timeout in seconds"
    )
    
    model_config = ConfigDict(frozen=True)


class AgentConfig(BaseModel):
    """AI agent configuration model without hardcoded defaults.
    
    This model represents AI agent configuration that respects
    environment variables through the factory pattern.
    """
    api_key: str = Field(description="Claude API key")
    model: str = Field(description="AI model identifier")
    confidence_threshold: float = Field(
        description="Memory confidence threshold"
    )
    storage_priority: str = Field(description="Default storage priority")
    max_tokens: int = Field(
        default=4000,
        description="Maximum tokens per request"
    )
    temperature: float = Field(
        default=0.1,
        description="Model temperature for deterministic responses"
    )
    
    model_config = ConfigDict(frozen=True)


class MonitoringConfig(BaseModel):
    """Monitoring configuration model without hardcoded defaults.
    
    This model represents monitoring and observability configuration
    that respects environment variables through the factory pattern.
    """
    logfire_token: Optional[str] = Field(
        default=None,
        description="Logfire API token for monitoring"
    )
    log_level: str = Field(description="Logging level")
    debug_mode: bool = Field(description="Enable debug mode")
    metrics_enabled: bool = Field(
        default=True,
        description="Enable metrics collection"
    )
    
    model_config = ConfigDict(frozen=True)


class DatabaseConfigFactory:
    """Factory for creating database configuration from environment.
    
    This factory ensures that database configuration respects environment
    variables while providing type-safe configuration objects.
    """
    
    def __init__(self, config: ConfigProtocol):
        """Initialize the factory with configuration source.
        
        Args:
            config: Configuration source implementing ConfigProtocol
        """
        self.config = config
    
    def create_database_config(self, **overrides: Any) -> DatabaseConfig:
        """Create database config with environment defaults and optional overrides.
        
        Args:
            **overrides: Optional parameter overrides
            
        Returns:
            DatabaseConfig: Configured database settings
            
        Raises:
            ConfigurationFactoryError: If configuration creation fails
        """
        try:
            return DatabaseConfig(
                uri=overrides.get('uri', self.config.NEO4J_URI),
                user=overrides.get('user', self.config.NEO4J_USER),
                password=overrides.get('password', self.config.NEO4J_PASSWORD),
                max_connection_pool_size=overrides.get(
                    'max_connection_pool_size', 10
                ),
                connection_timeout=overrides.get('connection_timeout', 30),
                **{k: v for k, v in overrides.items() 
                   if k not in [
                       'uri', 'user', 'password', 
                       'max_connection_pool_size', 'connection_timeout'
                   ]}
            )
        except Exception as e:
            raise ConfigurationFactoryError(
                "DatabaseConfigFactory",
                f"Failed to create database configuration: {e}"
            ) from e


class RedisConfigFactory:
    """Factory for creating Redis configuration from environment.
    
    This factory ensures that Redis configuration respects environment
    variables while providing type-safe configuration objects.
    """
    
    def __init__(self, config: ConfigProtocol):
        """Initialize the factory with configuration source.
        
        Args:
            config: Configuration source implementing ConfigProtocol
        """
        self.config = config
    
    def create_redis_config(self, **overrides: Any) -> RedisConfig:
        """Create Redis config with environment defaults and optional overrides.
        
        Args:
            **overrides: Optional parameter overrides
            
        Returns:
            RedisConfig: Configured Redis settings
            
        Raises:
            ConfigurationFactoryError: If configuration creation fails
        """
        try:
            return RedisConfig(
                url=overrides.get('url', self.config.REDIS_URL),
                password=overrides.get('password', self.config.REDIS_PASSWORD),
                max_connections=overrides.get('max_connections', 10),
                socket_timeout=overrides.get('socket_timeout', 30),
                **{k: v for k, v in overrides.items() 
                   if k not in [
                       'url', 'password', 'max_connections', 'socket_timeout'
                   ]}
            )
        except Exception as e:
            raise ConfigurationFactoryError(
                "RedisConfigFactory",
                f"Failed to create Redis configuration: {e}"
            ) from e


class AgentConfigFactory:
    """Factory for creating agent configuration from environment.
    
    This factory ensures that AI agent configuration respects environment
    variables while providing type-safe configuration objects.
    """
    
    def __init__(self, config: ConfigProtocol):
        """Initialize the factory with configuration source.
        
        Args:
            config: Configuration source implementing ConfigProtocol
        """
        self.config = config
    
    def create_agent_config(self, **overrides: Any) -> AgentConfig:
        """Create agent config with environment defaults and optional overrides.
        
        Args:
            **overrides: Optional parameter overrides
            
        Returns:
            AgentConfig: Configured agent settings
            
        Raises:
            ConfigurationFactoryError: If configuration creation fails
        """
        try:
            return AgentConfig(
                api_key=overrides.get('api_key', self.config.CLAUDE_API_KEY),
                model=overrides.get('model', 'claude-3-sonnet-20240229'),
                confidence_threshold=overrides.get(
                    'confidence_threshold', 
                    self.config.MEMORY_CONFIDENCE_THRESHOLD
                ),
                storage_priority=overrides.get(
                    'storage_priority',
                    self.config.MEMORY_STORAGE_PRIORITY
                ),
                max_tokens=overrides.get('max_tokens', 4000),
                temperature=overrides.get('temperature', 0.1),
                **{k: v for k, v in overrides.items() 
                   if k not in [
                       'api_key', 'model', 'confidence_threshold',
                       'storage_priority', 'max_tokens', 'temperature'
                   ]}
            )
        except Exception as e:
            raise ConfigurationFactoryError(
                "AgentConfigFactory",
                f"Failed to create agent configuration: {e}"
            ) from e


class MonitoringConfigFactory:
    """Factory for creating monitoring configuration from environment.
    
    This factory ensures that monitoring configuration respects environment
    variables while providing type-safe configuration objects.
    """
    
    def __init__(self, config: ConfigProtocol):
        """Initialize the factory with configuration source.
        
        Args:
            config: Configuration source implementing ConfigProtocol
        """
        self.config = config
    
    def create_monitoring_config(self, **overrides: Any) -> MonitoringConfig:
        """Create monitoring config with environment defaults and overrides.
        
        Args:
            **overrides: Optional parameter overrides
            
        Returns:
            MonitoringConfig: Configured monitoring settings
            
        Raises:
            ConfigurationFactoryError: If configuration creation fails
        """
        try:
            return MonitoringConfig(
                logfire_token=overrides.get(
                    'logfire_token', 
                    self.config.LOGFIRE_TOKEN
                ),
                log_level=overrides.get('log_level', self.config.LOG_LEVEL),
                debug_mode=overrides.get('debug_mode', self.config.DEBUG_MODE),
                metrics_enabled=overrides.get('metrics_enabled', True),
                **{k: v for k, v in overrides.items() 
                   if k not in [
                       'logfire_token', 'log_level', 'debug_mode', 'metrics_enabled'
                   ]}
            )
        except Exception as e:
            raise ConfigurationFactoryError(
                "MonitoringConfigFactory",
                f"Failed to create monitoring configuration: {e}"
            ) from e