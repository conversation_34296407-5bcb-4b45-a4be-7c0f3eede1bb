"""Shared Pydantic models for the entire Memory Orchestrator system."""

from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from typing import Optional, Dict, Any, List, Literal
from uuid import UUID, uuid4


class ConversationContext(BaseModel):
    """Conversation context for memory operations.
    
    This model represents the context of a conversation that needs to be
    processed by the memory system, including metadata for intelligent
    storage and retrieval decisions.
    """
    platform: Literal["claude_desktop", "claude_code", "custom_agent"] = Field(
        description="Source platform where the conversation originated"
    )
    user_id: str = Field(description="Unique identifier for the user")
    session_id: str = Field(description="Unique identifier for the conversation session")
    timestamp: datetime = Field(description="Timestamp when the conversation occurred")
    content: str = Field(description="The actual conversation content")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, 
        description="Additional metadata for context-aware processing"
    )
    
    model_config = ConfigDict(frozen=True)


class MemoryDecision(BaseModel):
    """Memory storage/retrieval decision made by the Memory Agent.
    
    This model captures the AI agent's decision about whether and how
    to store information in the memory system, including confidence
    scores and reasoning for transparency.
    """
    should_store: bool = Field(description="Whether the information should be stored")
    confidence: float = Field(
        ge=0.0, le=1.0, 
        description="Confidence level in the storage decision (0.0-1.0)"
    )
    reasoning: str = Field(description="Human-readable explanation of the decision")
    storage_priority: Literal["high", "medium", "low"] = Field(
        description="Priority level for storage in the memory system"
    )
    memory_type: Optional[Literal["preference", "fact", "relationship"]] = Field(
        default=None,
        description="Type of memory for specialized storage strategies"
    )
    
    model_config = ConfigDict(frozen=True)


class MemoryQuery(BaseModel):
    """Memory query specification for retrieving relevant information.
    
    This model defines the parameters for querying the memory system,
    including search terms, filtering criteria, and result preferences.
    """
    query_type: Literal["preference", "fact", "relationship"] = Field(
        description="Type of memory to search for"
    )
    search_terms: List[str] = Field(description="Keywords and phrases to search for")
    temporal_filter: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Time-based filtering parameters (e.g., 'last_week', 'before_date')"
    )
    confidence_threshold: float = Field(
        default=0.7, ge=0.0, le=1.0,
        description="Minimum confidence threshold for returned results"
    )
    max_results: int = Field(
        default=5, ge=1, le=100,
        description="Maximum number of results to return"
    )
    context_expansion: bool = Field(
        default=True,
        description="Whether to include related context in results"
    )
    
    model_config = ConfigDict(frozen=True)


class MemoryResult(BaseModel):
    """Result from a memory query operation.
    
    This model represents a single result returned from the memory system,
    including the content, metadata, and relevance scoring.
    """
    id: UUID = Field(default_factory=uuid4, description="Unique identifier for the result")
    content: str = Field(description="The retrieved memory content")
    memory_type: Literal["preference", "fact", "relationship"] = Field(
        description="Type of memory retrieved"
    )
    confidence: float = Field(
        ge=0.0, le=1.0,
        description="Confidence score for the relevance of this result"
    )
    timestamp: datetime = Field(description="When this memory was originally stored")
    source_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Original context where this memory was created"
    )
    
    model_config = ConfigDict(frozen=True)


class HealthStatus(BaseModel):
    """System health status for monitoring and operations.
    
    This model provides standardized health check information for
    all components in the Memory Orchestrator system.
    """
    component: str = Field(description="Name of the system component")
    status: Literal["healthy", "degraded", "unhealthy"] = Field(
        description="Current health status of the component"
    )
    message: str = Field(description="Human-readable status message")
    timestamp: datetime = Field(description="When this status was recorded")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional diagnostic information"
    )
    response_time_ms: Optional[float] = Field(
        default=None,
        description="Response time in milliseconds for performance monitoring"
    )
    
    model_config = ConfigDict(frozen=True)


class SystemMetrics(BaseModel):
    """System performance and operational metrics.
    
    This model captures key performance indicators and operational
    metrics for monitoring system health and performance.
    """
    memory_usage_mb: float = Field(description="Memory usage in megabytes")
    cpu_usage_percent: float = Field(ge=0.0, le=100.0, description="CPU usage percentage")
    active_connections: int = Field(ge=0, description="Number of active database connections")
    total_memories_stored: int = Field(ge=0, description="Total number of memories in storage")
    average_query_time_ms: float = Field(ge=0.0, description="Average query response time")
    timestamp: datetime = Field(description="When these metrics were collected")
    
    model_config = ConfigDict(frozen=True)