"""Comprehensive tests for configuration factory pattern implementation."""

import pytest
from unittest.mock import Mock, patch
import time
from pydantic import ValidationError

from src.config.factories import (
    DatabaseConfig,
    RedisConfig,
    AgentConfig,
    MonitoringConfig,
    DatabaseConfigFactory,
    RedisConfigFactory,
    AgentConfigFactory,
    MonitoringConfigFactory,
)
from src.config.settings import AppSettings
from src.config.exceptions import ConfigurationFactoryError


class TestDatabaseConfig:
    """Test suite for DatabaseConfig model."""
    
    def test_database_config_creation_valid(self):
        """Test successful creation of DatabaseConfig."""
        config = DatabaseConfig(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="password123"
        )
        
        assert config.uri == "bolt://localhost:7687"
        assert config.user == "neo4j"
        assert config.password == "password123"
        assert config.max_connection_pool_size == 10
        assert config.connection_timeout == 30
    
    def test_database_config_with_custom_values(self):
        """Test DatabaseConfig with custom pool and timeout values."""
        config = DatabaseConfig(
            uri="bolt://prod:7687",
            user="admin",
            password="secret",
            max_connection_pool_size=20,
            connection_timeout=60
        )
        
        assert config.max_connection_pool_size == 20
        assert config.connection_timeout == 60
    
    def test_database_config_immutability(self):
        """Test that DatabaseConfig instances are immutable."""
        config = DatabaseConfig(
            uri="bolt://localhost:7687",
            user="neo4j",
            password="password123"
        )
        
        with pytest.raises(ValidationError):
            config.uri = "bolt://hacked:7687"


class TestDatabaseConfigFactory:
    """Test suite for DatabaseConfigFactory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.NEO4J_URI = "bolt://localhost:7687"
        settings.NEO4J_USER = "neo4j"
        settings.NEO4J_PASSWORD = "password123"
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """DatabaseConfigFactory instance."""
        return DatabaseConfigFactory(mock_settings)
    
    def test_create_database_config_with_defaults(self, factory):
        """Test database config creation with environment defaults."""
        config = factory.create_database_config()
        
        assert config.uri == "bolt://localhost:7687"
        assert config.user == "neo4j"
        assert config.password == "password123"
        assert config.max_connection_pool_size == 10
        assert config.connection_timeout == 30
    
    def test_create_database_config_with_overrides(self, factory):
        """Test database config creation with parameter overrides."""
        config = factory.create_database_config(
            uri="bolt://production:7687",
            user="prod_user",
            max_connection_pool_size=20
        )
        
        assert config.uri == "bolt://production:7687"
        assert config.user == "prod_user"
        assert config.password == "password123"  # From environment
        assert config.max_connection_pool_size == 20  # Override
        assert config.connection_timeout == 30  # Default
    
    def test_create_database_config_all_overrides(self, factory):
        """Test database config creation with all parameters overridden."""
        config = factory.create_database_config(
            uri="bolt://test:7687",
            user="test_user",
            password="test_password",
            max_connection_pool_size=5,
            connection_timeout=15
        )
        
        assert config.uri == "bolt://test:7687"
        assert config.user == "test_user"
        assert config.password == "test_password"
        assert config.max_connection_pool_size == 5
        assert config.connection_timeout == 15
    
    def test_create_database_config_extra_overrides(self, factory):
        """Test that extra override parameters are ignored gracefully."""
        config = factory.create_database_config(
            uri="bolt://test:7687",
            extra_param="ignored"
        )
        
        assert config.uri == "bolt://test:7687"
        assert config.user == "neo4j"  # From environment
        assert not hasattr(config, 'extra_param')
    
    @pytest.mark.performance
    def test_create_database_config_performance(self, factory):
        """Test database config creation performance (<5ms)."""
        start_time = time.time()
        config = factory.create_database_config()
        duration = time.time() - start_time
        
        assert duration < 0.005  # <5ms requirement
        assert config is not None
    
    def test_create_database_config_error_handling(self, mock_settings):
        """Test error handling in database config creation."""
        # Simulate a configuration that causes validation error by making NEO4J_URI None
        mock_settings.NEO4J_URI = None  # Invalid None value
        factory = DatabaseConfigFactory(mock_settings)
        
        with pytest.raises(ConfigurationFactoryError) as exc_info:
            factory.create_database_config()
        
        error = exc_info.value
        assert error.factory_name == "DatabaseConfigFactory"
        assert "Failed to create database configuration" in str(error)


class TestRedisConfigFactory:
    """Test suite for RedisConfigFactory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.REDIS_URL = "redis://localhost:6379"
        settings.REDIS_PASSWORD = None
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """RedisConfigFactory instance."""
        return RedisConfigFactory(mock_settings)
    
    def test_create_redis_config_with_defaults(self, factory):
        """Test Redis config creation with environment defaults."""
        config = factory.create_redis_config()
        
        assert config.url == "redis://localhost:6379"
        assert config.password is None
        assert config.max_connections == 10
        assert config.socket_timeout == 30
    
    def test_create_redis_config_with_password(self, mock_settings):
        """Test Redis config creation with password from environment."""
        mock_settings.REDIS_PASSWORD = "redis_secret"
        factory = RedisConfigFactory(mock_settings)
        
        config = factory.create_redis_config()
        
        assert config.password == "redis_secret"
    
    def test_create_redis_config_with_overrides(self, factory):
        """Test Redis config creation with parameter overrides."""
        config = factory.create_redis_config(
            url="redis://production:6379",
            password="override_password",
            max_connections=20
        )
        
        assert config.url == "redis://production:6379"
        assert config.password == "override_password"
        assert config.max_connections == 20
        assert config.socket_timeout == 30  # Default


class TestAgentConfigFactory:
    """Test suite for AgentConfigFactory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.CLAUDE_API_KEY = "test-api-key"
        settings.MEMORY_CONFIDENCE_THRESHOLD = 0.7
        settings.MEMORY_STORAGE_PRIORITY = "medium"
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """AgentConfigFactory instance."""
        return AgentConfigFactory(mock_settings)
    
    def test_create_agent_config_with_defaults(self, factory):
        """Test agent config creation with environment defaults."""
        config = factory.create_agent_config()
        
        assert config.api_key == "test-api-key"
        assert config.model == "claude-3-sonnet-20240229"
        assert config.confidence_threshold == 0.7
        assert config.storage_priority == "medium"
        assert config.max_tokens == 4000
        assert config.temperature == 0.1
    
    def test_create_agent_config_with_overrides(self, factory):
        """Test agent config creation with parameter overrides."""
        config = factory.create_agent_config(
            model="claude-3-haiku-20240307",
            confidence_threshold=0.8,
            max_tokens=2000,
            temperature=0.2
        )
        
        assert config.api_key == "test-api-key"  # From environment
        assert config.model == "claude-3-haiku-20240307"
        assert config.confidence_threshold == 0.8
        assert config.storage_priority == "medium"  # From environment
        assert config.max_tokens == 2000
        assert config.temperature == 0.2
    
    @pytest.mark.performance
    def test_create_agent_config_performance(self, factory):
        """Test agent config creation performance (<5ms)."""
        start_time = time.time()
        config = factory.create_agent_config()
        duration = time.time() - start_time
        
        assert duration < 0.005  # <5ms requirement
        assert config is not None


class TestMonitoringConfigFactory:
    """Test suite for MonitoringConfigFactory."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=AppSettings)
        settings.LOGFIRE_TOKEN = None
        settings.LOG_LEVEL = "INFO"
        settings.DEBUG_MODE = False
        return settings
    
    @pytest.fixture
    def factory(self, mock_settings):
        """MonitoringConfigFactory instance."""
        return MonitoringConfigFactory(mock_settings)
    
    def test_create_monitoring_config_with_defaults(self, factory):
        """Test monitoring config creation with environment defaults."""
        config = factory.create_monitoring_config()
        
        assert config.logfire_token is None
        assert config.log_level == "INFO"
        assert config.debug_mode is False
        assert config.metrics_enabled is True
    
    def test_create_monitoring_config_with_logfire(self, mock_settings):
        """Test monitoring config with Logfire token."""
        mock_settings.LOGFIRE_TOKEN = "logfire_token_123"
        factory = MonitoringConfigFactory(mock_settings)
        
        config = factory.create_monitoring_config()
        
        assert config.logfire_token == "logfire_token_123"
    
    def test_create_monitoring_config_debug_mode(self, mock_settings):
        """Test monitoring config with debug mode enabled."""
        mock_settings.DEBUG_MODE = True
        mock_settings.LOG_LEVEL = "DEBUG"
        factory = MonitoringConfigFactory(mock_settings)
        
        config = factory.create_monitoring_config()
        
        assert config.debug_mode is True
        assert config.log_level == "DEBUG"


class TestFactoryIntegration:
    """Integration tests for factory pattern with real AppSettings."""
    
    def test_all_factories_with_real_settings(self):
        """Test all factories work with real AppSettings."""
        with patch.dict('os.environ', {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            
            # Test all factories
            db_factory = DatabaseConfigFactory(settings)
            redis_factory = RedisConfigFactory(settings)
            agent_factory = AgentConfigFactory(settings)
            monitoring_factory = MonitoringConfigFactory(settings)
            
            # Create configurations
            db_config = db_factory.create_database_config()
            redis_config = redis_factory.create_redis_config()
            agent_config = agent_factory.create_agent_config()
            monitoring_config = monitoring_factory.create_monitoring_config()
            
            # Verify all configurations are valid
            assert isinstance(db_config, DatabaseConfig)
            assert isinstance(redis_config, RedisConfig)
            assert isinstance(agent_config, AgentConfig)
            assert isinstance(monitoring_config, MonitoringConfig)
    
    @pytest.mark.performance
    def test_factory_batch_creation_performance(self):
        """Test performance of creating multiple configs (<20ms total)."""
        with patch.dict('os.environ', {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            
            start_time = time.time()
            
            # Create all factories and configurations
            db_factory = DatabaseConfigFactory(settings)
            redis_factory = RedisConfigFactory(settings)
            agent_factory = AgentConfigFactory(settings)
            monitoring_factory = MonitoringConfigFactory(settings)
            
            db_config = db_factory.create_database_config()
            redis_config = redis_factory.create_redis_config()
            agent_config = agent_factory.create_agent_config()
            monitoring_config = monitoring_factory.create_monitoring_config()
            
            duration = time.time() - start_time
            
            # All factory operations should complete in <20ms
            assert duration < 0.02
            assert all([db_config, redis_config, agent_config, monitoring_config])
    
    def test_factory_environment_override_integration(self):
        """Test factory pattern respects environment variable changes."""
        with patch.dict('os.environ', {
            'NEO4J_PASSWORD': 'original_password',
            'CLAUDE_API_KEY': 'original_api_key',
            'NEO4J_URI': 'bolt://original:7687',
            'MEMORY_CONFIDENCE_THRESHOLD': '0.8'
        }):
            settings = AppSettings()
            db_factory = DatabaseConfigFactory(settings)
            agent_factory = AgentConfigFactory(settings)
            
            # Create configs with environment defaults
            db_config = db_factory.create_database_config()
            agent_config = agent_factory.create_agent_config()
            
            assert db_config.uri == "bolt://original:7687"
            assert db_config.password == "original_password"
            assert agent_config.api_key == "original_api_key"
            assert agent_config.confidence_threshold == 0.8
            
            # Override specific values while keeping environment for others
            overridden_db_config = db_factory.create_database_config(
                uri="bolt://override:7687"
            )
            overridden_agent_config = agent_factory.create_agent_config(
                confidence_threshold=0.9
            )
            
            # Overridden values
            assert overridden_db_config.uri == "bolt://override:7687"
            assert overridden_agent_config.confidence_threshold == 0.9
            
            # Environment values preserved
            assert overridden_db_config.password == "original_password"
            assert overridden_agent_config.api_key == "original_api_key"


class TestConfigProtocol:
    """Test suite for ConfigProtocol compliance."""
    
    def test_app_settings_implements_config_protocol(self):
        """Test that AppSettings implements ConfigProtocol."""
        with patch.dict('os.environ', {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            
            # Verify all required protocol attributes exist
            assert hasattr(settings, 'NEO4J_URI')
            assert hasattr(settings, 'NEO4J_USER')
            assert hasattr(settings, 'NEO4J_PASSWORD')
            assert hasattr(settings, 'REDIS_URL')
            assert hasattr(settings, 'REDIS_PASSWORD')
            assert hasattr(settings, 'CLAUDE_API_KEY')
            assert hasattr(settings, 'MEMORY_CONFIDENCE_THRESHOLD')
            assert hasattr(settings, 'MEMORY_STORAGE_PRIORITY')
            assert hasattr(settings, 'LOGFIRE_TOKEN')
            assert hasattr(settings, 'APP_ENV')
            assert hasattr(settings, 'DEBUG_MODE')
            assert hasattr(settings, 'LOG_LEVEL')
    
    def test_factories_accept_protocol_implementation(self):
        """Test that factories work with any ConfigProtocol implementation."""
        # Create a custom protocol implementation
        custom_config = Mock()
        custom_config.NEO4J_URI = "bolt://custom:7687"
        custom_config.NEO4J_USER = "custom_user"
        custom_config.NEO4J_PASSWORD = "custom_password"
        
        # Factory should work with any protocol implementation
        factory = DatabaseConfigFactory(custom_config)
        config = factory.create_database_config()
        
        assert config.uri == "bolt://custom:7687"
        assert config.user == "custom_user"
        assert config.password == "custom_password"