"""Comprehensive tests for configuration settings."""

import pytest
import os
from unittest.mock import patch
from pydantic import ValidationError
import time

from src.config.settings import AppSettings, get_settings


class TestAppSettings:
    """Test suite for AppSettings configuration class."""
    
    def test_settings_with_all_required_env_vars(self):
        """Test settings creation with all required environment variables."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            
            assert settings.NEO4J_URI == "bolt://localhost:7687"
            assert settings.NEO4J_USER == "neo4j"
            assert settings.NEO4J_PASSWORD == "test_password"
            assert settings.CLAUDE_API_KEY == "test_api_key"
            assert settings.MEMORY_CONFIDENCE_THRESHOLD == 0.7
            assert settings.APP_ENV == "development"
    
    def test_settings_missing_required_env_vars(self):
        """Test settings creation fails with missing required variables."""
        # Clear environment variables
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValidationError) as exc_info:
                AppSettings()
            
            error_message = str(exc_info.value)
            assert "NEO4J_PASSWORD" in error_message
            assert "CLAUDE_API_KEY" in error_message
    
    def test_settings_with_custom_env_file(self):
        """Test settings can load from custom environment file."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'env_password',
            'CLAUDE_API_KEY': 'env_api_key',
            'REDIS_URL': 'redis://custom:6379',
            'LOG_LEVEL': 'DEBUG'
        }):
            settings = AppSettings()
            
            assert settings.REDIS_URL == "redis://custom:6379"
            assert settings.LOG_LEVEL == "DEBUG"
    
    def test_settings_validation_confidence_threshold_valid(self):
        """Test validation passes for valid confidence threshold values."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'MEMORY_CONFIDENCE_THRESHOLD': '0.8'
        }):
            settings = AppSettings()
            assert settings.validate_configuration() is True
            assert settings.MEMORY_CONFIDENCE_THRESHOLD == 0.8
    
    def test_settings_validation_confidence_threshold_invalid_high(self):
        """Test validation fails for confidence threshold > 1.0."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'MEMORY_CONFIDENCE_THRESHOLD': '1.5'
        }):
            settings = AppSettings()
            with pytest.raises(ValueError) as exc_info:
                settings.validate_configuration()
            assert "MEMORY_CONFIDENCE_THRESHOLD must be between 0.0 and 1.0" in str(exc_info.value)
    
    def test_settings_validation_confidence_threshold_invalid_low(self):
        """Test validation fails for confidence threshold < 0.0."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'MEMORY_CONFIDENCE_THRESHOLD': '-0.1'
        }):
            settings = AppSettings()
            with pytest.raises(ValueError) as exc_info:
                settings.validate_configuration()
            assert "MEMORY_CONFIDENCE_THRESHOLD must be between 0.0 and 1.0" in str(exc_info.value)
    
    def test_settings_validation_storage_priority_valid(self):
        """Test validation passes for valid storage priority values."""
        valid_priorities = ["high", "medium", "low"]
        
        for priority in valid_priorities:
            with patch.dict(os.environ, {
                'NEO4J_PASSWORD': 'test_password',
                'CLAUDE_API_KEY': 'test_api_key',
                'MEMORY_STORAGE_PRIORITY': priority
            }):
                settings = AppSettings()
                assert settings.validate_configuration() is True
                assert settings.MEMORY_STORAGE_PRIORITY == priority
    
    def test_settings_validation_storage_priority_invalid(self):
        """Test validation fails for invalid storage priority."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'MEMORY_STORAGE_PRIORITY': 'invalid'
        }):
            settings = AppSettings()
            with pytest.raises(ValueError) as exc_info:
                settings.validate_configuration()
            assert "MEMORY_STORAGE_PRIORITY must be 'high', 'medium', or 'low'" in str(exc_info.value)
    
    def test_settings_validation_log_level_valid(self):
        """Test validation passes for valid log level values."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        
        for level in valid_levels:
            with patch.dict(os.environ, {
                'NEO4J_PASSWORD': 'test_password',
                'CLAUDE_API_KEY': 'test_api_key',
                'LOG_LEVEL': level
            }):
                settings = AppSettings()
                assert settings.validate_configuration() is True
                assert settings.LOG_LEVEL == level
    
    def test_settings_validation_log_level_invalid(self):
        """Test validation fails for invalid log level."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'LOG_LEVEL': 'INVALID'
        }):
            settings = AppSettings()
            with pytest.raises(ValueError) as exc_info:
                settings.validate_configuration()
            assert "LOG_LEVEL must be a valid logging level" in str(exc_info.value)
    
    def test_settings_boolean_conversion(self):
        """Test boolean environment variable conversion."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'DEBUG_MODE': 'true'
        }):
            settings = AppSettings()
            assert settings.DEBUG_MODE is True
        
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'DEBUG_MODE': 'false'
        }):
            settings = AppSettings()
            assert settings.DEBUG_MODE is False
    
    def test_settings_optional_redis_password(self):
        """Test that Redis password is optional."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            assert settings.REDIS_PASSWORD is None
        
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'REDIS_PASSWORD': 'redis_secret'
        }):
            settings = AppSettings()
            assert settings.REDIS_PASSWORD == "redis_secret"
    
    def test_settings_optional_logfire_token(self):
        """Test that Logfire token is optional."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = AppSettings()
            assert settings.LOGFIRE_TOKEN is None
        
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'LOGFIRE_TOKEN': 'logfire_token_123'
        }):
            settings = AppSettings()
            assert settings.LOGFIRE_TOKEN == "logfire_token_123"


class TestGetSettings:
    """Test suite for get_settings function and caching behavior."""
    
    def test_get_settings_returns_valid_settings(self):
        """Test that get_settings returns a valid AppSettings instance."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings = get_settings()
            assert isinstance(settings, AppSettings)
            assert settings.CLAUDE_API_KEY == "test_api_key"
    
    def test_get_settings_fresh_instances(self):
        """Test that get_settings returns fresh instances (no caching)."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            settings1 = get_settings()
            settings2 = get_settings()
            
            # Should be different instances (no caching)
            assert settings1 is not settings2
            # But should have same values
            assert settings1.CLAUDE_API_KEY == settings2.CLAUDE_API_KEY
    
    def test_get_settings_calls_validation(self):
        """Test that get_settings calls validation on the settings."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            with patch.object(AppSettings, 'validate_configuration') as mock_validate:
                mock_validate.return_value = True
                
                get_settings()
                mock_validate.assert_called_once()
    
    @pytest.mark.performance
    def test_get_settings_performance(self):
        """Test that get_settings meets performance requirements (<50ms)."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            start_time = time.time()
            settings = get_settings()
            duration = time.time() - start_time
            
            # Performance requirement: <50ms (0.05 seconds) without caching
            assert duration < 0.05
            assert settings is not None


class TestSettingsIntegration:
    """Integration tests for settings with different environment scenarios."""
    
    def test_settings_production_environment(self):
        """Test settings configuration for production environment."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'prod_password',
            'CLAUDE_API_KEY': 'prod_api_key',
            'APP_ENV': 'production',
            'DEBUG_MODE': 'false',
            'LOG_LEVEL': 'WARNING',
            'NEO4J_URI': 'bolt://prod-neo4j:7687',
            'REDIS_URL': 'redis://prod-redis:6379'
        }):
            settings = AppSettings()
            
            assert settings.APP_ENV == "production"
            assert settings.DEBUG_MODE is False
            assert settings.LOG_LEVEL == "WARNING"
            assert settings.NEO4J_URI == "bolt://prod-neo4j:7687"
            assert settings.REDIS_URL == "redis://prod-redis:6379"
            assert settings.validate_configuration() is True
    
    def test_settings_development_environment(self):
        """Test settings configuration for development environment."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'dev_password',
            'CLAUDE_API_KEY': 'dev_api_key',
            'APP_ENV': 'development',
            'DEBUG_MODE': 'true',
            'LOG_LEVEL': 'DEBUG'
        }):
            settings = AppSettings()
            
            assert settings.APP_ENV == "development"
            assert settings.DEBUG_MODE is True
            assert settings.LOG_LEVEL == "DEBUG"
            assert settings.validate_configuration() is True
    
    def test_settings_with_monitoring_enabled(self):
        """Test settings with monitoring services enabled."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'LOGFIRE_TOKEN': 'logfire_monitoring_token'
        }):
            settings = AppSettings()
            
            assert settings.LOGFIRE_TOKEN == "logfire_monitoring_token"
            assert settings.validate_configuration() is True