"""Integration tests for F1 config module consumption by other modules."""

import pytest
import os
from unittest.mock import patch

# Test imports as if we're other modules consuming F1
from src.config import (
    get_settings,
    DatabaseConfigFactory,
    AgentConfigFactory,
    ConversationContext,
    MemoryDecision,
    ConfigurationError,
)


class TestF1ModuleIntegration:
    """Test F1 module integration for consuming modules F2, F3, C1, C2, I1, I2."""
    
    def test_basic_module_import_and_usage(self):
        """Test that other modules can import and use F1 components."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            # Simulate what F2 (infrastructure) would do
            settings = get_settings()
            db_factory = DatabaseConfigFactory(settings)
            db_config = db_factory.create_database_config()
            
            assert db_config.uri == "bolt://localhost:7687"
            assert db_config.user == "neo4j"
            assert db_config.password == "test_password"
    
    def test_agent_module_consumption(self):
        """Test agent modules C1, C2 can use F1 for configuration."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'MEMORY_CONFIDENCE_THRESHOLD': '0.8'
        }):
            # Simulate what C1 (Memory Agent) would do
            settings = get_settings()
            agent_factory = AgentConfigFactory(settings)
            agent_config = agent_factory.create_agent_config()
            
            assert agent_config.api_key == "test_api_key"
            assert agent_config.confidence_threshold == 0.8
            assert agent_config.model == "claude-3-sonnet-20240229"
    
    def test_shared_models_consumption(self):
        """Test that all modules can use shared F1 models."""
        from datetime import datetime, timezone
        
        # Simulate what any module would do with shared models
        context = ConversationContext(
            platform="claude_desktop",
            user_id="test_user",
            session_id="test_session",
            timestamp=datetime.now(timezone.utc),
            content="Test conversation for integration"
        )
        
        decision = MemoryDecision(
            should_store=True,
            confidence=0.9,
            reasoning="High-value conversation for testing integration",
            storage_priority="high"
        )
        
        assert context.platform == "claude_desktop"
        assert decision.should_store is True
        assert decision.confidence == 0.9
    
    def test_exception_handling_integration(self):
        """Test that modules can properly handle F1 exceptions."""
        # Test exception inheritance and catching
        try:
            raise ConfigurationError("Test configuration error")
        except ConfigurationError as e:
            assert "Test configuration error" in str(e)
    
    def test_environment_override_integration(self):
        """Test modules can override config while respecting environment."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'env_password',
            'CLAUDE_API_KEY': 'env_api_key',
            'NEO4J_URI': 'bolt://env-server:7687'
        }):
            settings = get_settings()
            db_factory = DatabaseConfigFactory(settings)
            
            # Test environment defaults
            default_config = db_factory.create_database_config()
            assert default_config.uri == "bolt://env-server:7687"
            assert default_config.password == "env_password"
            
            # Test override capability for modules
            override_config = db_factory.create_database_config(
                uri="bolt://override-server:7687",
                max_connection_pool_size=20
            )
            assert override_config.uri == "bolt://override-server:7687"  # Overridden
            assert override_config.password == "env_password"  # From environment
            assert override_config.max_connection_pool_size == 20  # Overridden
    
    @pytest.mark.performance
    def test_module_consumption_performance(self):
        """Test that F1 consumption meets performance requirements for modules."""
        import time
        
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
        }):
            # Simulate typical module startup sequence
            start_time = time.time()
            
            # What modules typically do on startup
            settings = get_settings()  # Cached after first call
            db_factory = DatabaseConfigFactory(settings)
            agent_factory = AgentConfigFactory(settings)
            
            db_config = db_factory.create_database_config()
            agent_config = agent_factory.create_agent_config()
            
            duration = time.time() - start_time
            
            # Should complete module configuration in under 50ms
            assert duration < 0.05
            assert db_config is not None
            assert agent_config is not None


class TestF1FactoryPatternCompliance:
    """Test F1 factory pattern compliance for consuming modules."""
    
    def test_environment_authority_compliance(self):
        """Test that F1 respects environment variable authority."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'env_password',
            'CLAUDE_API_KEY': 'env_api_key',
            'NEO4J_URI': 'bolt://env-authority:7687',
            'MEMORY_CONFIDENCE_THRESHOLD': '0.85'
        }):
            settings = get_settings()
            
            # Environment variables should be the authority
            assert settings.NEO4J_URI == "bolt://env-authority:7687"
            assert settings.NEO4J_PASSWORD == "env_password"
            assert settings.CLAUDE_API_KEY == "env_api_key"
            assert settings.MEMORY_CONFIDENCE_THRESHOLD == 0.85
    
    def test_no_hardcoded_defaults_in_models(self):
        """Test that F1 models don't have hardcoded defaults that defeat env vars."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'test_password',
            'CLAUDE_API_KEY': 'test_api_key',
            'NEO4J_URI': 'bolt://custom:7687'
        }):
            settings = get_settings()
            db_factory = DatabaseConfigFactory(settings)
            
            # Configuration should come from environment, not hardcoded defaults
            config = db_factory.create_database_config()
            assert config.uri == "bolt://custom:7687"  # From environment
            
    def test_factory_override_capability(self):
        """Test that modules can override specific values while keeping env defaults."""
        with patch.dict(os.environ, {
            'NEO4J_PASSWORD': 'env_password',
            'CLAUDE_API_KEY': 'env_api_key',
        }):
            settings = get_settings()
            agent_factory = AgentConfigFactory(settings)
            
            # Module-specific overrides while respecting environment
            test_config = agent_factory.create_agent_config(
                model="claude-3-haiku-20240307",  # Override for testing
                temperature=0.0  # Override for deterministic testing
            )
            
            # Overrides applied
            assert test_config.model == "claude-3-haiku-20240307"
            assert test_config.temperature == 0.0
            
            # Environment values preserved
            assert test_config.api_key == "env_api_key"
            assert test_config.confidence_threshold == 0.7  # Default from settings