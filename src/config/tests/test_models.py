"""Comprehensive tests for shared Pydantic models."""

import pytest
from datetime import datetime, timezone
from uuid import UUID
from pydantic import ValidationError
import time

from src.config.models import (
    ConversationContext,
    MemoryDecision,
    MemoryQuery,
    MemoryResult,
    HealthStatus,
    SystemMetrics,
)


class TestConversationContext:
    """Test suite for ConversationContext model."""
    
    def test_conversation_context_creation_valid(self):
        """Test successful creation of ConversationContext with valid data."""
        timestamp = datetime.now(timezone.utc)
        context = ConversationContext(
            platform="claude_desktop",
            user_id="user123",
            session_id="session456",
            timestamp=timestamp,
            content="Test conversation content"
        )
        
        assert context.platform == "claude_desktop"
        assert context.user_id == "user123"
        assert context.session_id == "session456"
        assert context.timestamp == timestamp
        assert context.content == "Test conversation content"
        assert context.metadata == {}
    
    def test_conversation_context_with_metadata(self):
        """Test ConversationContext creation with metadata."""
        metadata = {"source": "test", "version": "1.0"}
        context = ConversationContext(
            platform="claude_code",
            user_id="user123",
            session_id="session456",
            timestamp=datetime.now(timezone.utc),
            content="Test content",
            metadata=metadata
        )
        
        assert context.metadata == metadata
    
    def test_conversation_context_platform_validation(self):
        """Test platform field validation with valid values."""
        valid_platforms = ["claude_desktop", "claude_code", "custom_agent"]
        
        for platform in valid_platforms:
            context = ConversationContext(
                platform=platform,
                user_id="user123",
                session_id="session456",
                timestamp=datetime.now(timezone.utc),
                content="Test content"
            )
            assert context.platform == platform
    
    def test_conversation_context_platform_invalid(self):
        """Test platform field validation with invalid values."""
        with pytest.raises(ValidationError) as exc_info:
            ConversationContext(
                platform="invalid_platform",
                user_id="user123",
                session_id="session456",
                timestamp=datetime.now(timezone.utc),
                content="Test content"
            )
        assert "Input should be" in str(exc_info.value)
    
    def test_conversation_context_immutability(self):
        """Test that ConversationContext instances are immutable."""
        context = ConversationContext(
            platform="claude_desktop",
            user_id="user123",
            session_id="session456",
            timestamp=datetime.now(timezone.utc),
            content="Test content"
        )
        
        with pytest.raises(ValidationError):
            context.user_id = "new_user"
    
    @pytest.mark.performance
    def test_conversation_context_creation_performance(self):
        """Test ConversationContext creation performance (<10ms)."""
        start_time = time.time()
        
        context = ConversationContext(
            platform="claude_desktop",
            user_id="user123",
            session_id="session456",
            timestamp=datetime.now(timezone.utc),
            content="Test content"
        )
        
        duration = time.time() - start_time
        assert duration < 0.01  # <10ms requirement
        assert context is not None


class TestMemoryDecision:
    """Test suite for MemoryDecision model."""
    
    def test_memory_decision_creation_valid(self):
        """Test successful creation of MemoryDecision with valid data."""
        decision = MemoryDecision(
            should_store=True,
            confidence=0.85,
            reasoning="High-value user preference detected",
            storage_priority="high"
        )
        
        assert decision.should_store is True
        assert decision.confidence == 0.85
        assert decision.reasoning == "High-value user preference detected"
        assert decision.storage_priority == "high"
        assert decision.memory_type is None
    
    def test_memory_decision_with_memory_type(self):
        """Test MemoryDecision creation with memory type specified."""
        decision = MemoryDecision(
            should_store=True,
            confidence=0.9,
            reasoning="User preference about AI assistants",
            storage_priority="high",
            memory_type="preference"
        )
        
        assert decision.memory_type == "preference"
    
    def test_memory_decision_confidence_validation_valid(self):
        """Test confidence field validation with valid values."""
        valid_confidences = [0.0, 0.5, 1.0, 0.123, 0.999]
        
        for confidence in valid_confidences:
            decision = MemoryDecision(
                should_store=True,
                confidence=confidence,
                reasoning="Test reasoning",
                storage_priority="medium"
            )
            assert decision.confidence == confidence
    
    def test_memory_decision_confidence_validation_invalid(self):
        """Test confidence field validation with invalid values."""
        invalid_confidences = [-0.1, 1.1, 2.0, -1.0]
        
        for confidence in invalid_confidences:
            with pytest.raises(ValidationError) as exc_info:
                MemoryDecision(
                    should_store=True,
                    confidence=confidence,
                    reasoning="Test reasoning",
                    storage_priority="medium"
                )
            assert "Input should be" in str(exc_info.value)
    
    def test_memory_decision_storage_priority_validation(self):
        """Test storage_priority field validation."""
        valid_priorities = ["high", "medium", "low"]
        
        for priority in valid_priorities:
            decision = MemoryDecision(
                should_store=True,
                confidence=0.8,
                reasoning="Test reasoning",
                storage_priority=priority
            )
            assert decision.storage_priority == priority
    
    def test_memory_decision_memory_type_validation(self):
        """Test memory_type field validation."""
        valid_types = ["preference", "fact", "relationship"]
        
        for memory_type in valid_types:
            decision = MemoryDecision(
                should_store=True,
                confidence=0.8,
                reasoning="Test reasoning",
                storage_priority="medium",
                memory_type=memory_type
            )
            assert decision.memory_type == memory_type
    
    def test_memory_decision_immutability(self):
        """Test that MemoryDecision instances are immutable."""
        decision = MemoryDecision(
            should_store=True,
            confidence=0.8,
            reasoning="Test reasoning",
            storage_priority="medium"
        )
        
        with pytest.raises(ValidationError):
            decision.confidence = 0.9


class TestMemoryQuery:
    """Test suite for MemoryQuery model."""
    
    def test_memory_query_creation_valid(self):
        """Test successful creation of MemoryQuery with valid data."""
        query = MemoryQuery(
            query_type="preference",
            search_terms=["AI", "assistant", "preference"]
        )
        
        assert query.query_type == "preference"
        assert query.search_terms == ["AI", "assistant", "preference"]
        assert query.temporal_filter is None
        assert query.confidence_threshold == 0.7
        assert query.max_results == 5
        assert query.context_expansion is True
    
    def test_memory_query_with_all_fields(self):
        """Test MemoryQuery creation with all fields specified."""
        temporal_filter = {"after": "2024-01-01", "before": "2024-12-31"}
        query = MemoryQuery(
            query_type="fact",
            search_terms=["Python", "programming"],
            temporal_filter=temporal_filter,
            confidence_threshold=0.9,
            max_results=10,
            context_expansion=False
        )
        
        assert query.query_type == "fact"
        assert query.temporal_filter == temporal_filter
        assert query.confidence_threshold == 0.9
        assert query.max_results == 10
        assert query.context_expansion is False
    
    def test_memory_query_confidence_threshold_validation(self):
        """Test confidence_threshold field validation."""
        valid_thresholds = [0.0, 0.5, 1.0]
        
        for threshold in valid_thresholds:
            query = MemoryQuery(
                query_type="preference",
                search_terms=["test"],
                confidence_threshold=threshold
            )
            assert query.confidence_threshold == threshold
    
    def test_memory_query_max_results_validation(self):
        """Test max_results field validation."""
        valid_results = [1, 5, 50, 100]
        
        for max_results in valid_results:
            query = MemoryQuery(
                query_type="preference",
                search_terms=["test"],
                max_results=max_results
            )
            assert query.max_results == max_results
    
    def test_memory_query_max_results_invalid(self):
        """Test max_results field validation with invalid values."""
        invalid_results = [0, -1, 101, 1000]
        
        for max_results in invalid_results:
            with pytest.raises(ValidationError):
                MemoryQuery(
                    query_type="preference",
                    search_terms=["test"],
                    max_results=max_results
                )


class TestMemoryResult:
    """Test suite for MemoryResult model."""
    
    def test_memory_result_creation_valid(self):
        """Test successful creation of MemoryResult."""
        timestamp = datetime.now(timezone.utc)
        result = MemoryResult(
            content="User prefers concise responses",
            memory_type="preference",
            confidence=0.9,
            timestamp=timestamp
        )
        
        assert isinstance(result.id, UUID)
        assert result.content == "User prefers concise responses"
        assert result.memory_type == "preference"
        assert result.confidence == 0.9
        assert result.timestamp == timestamp
        assert result.source_context == {}
    
    def test_memory_result_with_source_context(self):
        """Test MemoryResult creation with source context."""
        source_context = {"platform": "claude_desktop", "session": "123"}
        result = MemoryResult(
            content="Test memory",
            memory_type="fact",
            confidence=0.8,
            timestamp=datetime.now(timezone.utc),
            source_context=source_context
        )
        
        assert result.source_context == source_context
    
    def test_memory_result_immutability(self):
        """Test that MemoryResult instances are immutable."""
        result = MemoryResult(
            content="Test memory",
            memory_type="fact",
            confidence=0.8,
            timestamp=datetime.now(timezone.utc)
        )
        
        with pytest.raises(ValidationError):
            result.confidence = 0.9


class TestHealthStatus:
    """Test suite for HealthStatus model."""
    
    def test_health_status_creation_valid(self):
        """Test successful creation of HealthStatus."""
        timestamp = datetime.now(timezone.utc)
        status = HealthStatus(
            component="database",
            status="healthy",
            message="All connections active",
            timestamp=timestamp
        )
        
        assert status.component == "database"
        assert status.status == "healthy"
        assert status.message == "All connections active"
        assert status.timestamp == timestamp
        assert status.metadata == {}
        assert status.response_time_ms is None
    
    def test_health_status_with_response_time(self):
        """Test HealthStatus with response time."""
        status = HealthStatus(
            component="api",
            status="healthy",
            message="API responding normally",
            timestamp=datetime.now(timezone.utc),
            response_time_ms=45.5
        )
        
        assert status.response_time_ms == 45.5
    
    def test_health_status_validation(self):
        """Test status field validation."""
        valid_statuses = ["healthy", "degraded", "unhealthy"]
        
        for status_value in valid_statuses:
            status = HealthStatus(
                component="test",
                status=status_value,
                message="Test message",
                timestamp=datetime.now(timezone.utc)
            )
            assert status.status == status_value


class TestSystemMetrics:
    """Test suite for SystemMetrics model."""
    
    def test_system_metrics_creation_valid(self):
        """Test successful creation of SystemMetrics."""
        timestamp = datetime.now(timezone.utc)
        metrics = SystemMetrics(
            memory_usage_mb=512.5,
            cpu_usage_percent=45.2,
            active_connections=5,
            total_memories_stored=1000,
            average_query_time_ms=25.5,
            timestamp=timestamp
        )
        
        assert metrics.memory_usage_mb == 512.5
        assert metrics.cpu_usage_percent == 45.2
        assert metrics.active_connections == 5
        assert metrics.total_memories_stored == 1000
        assert metrics.average_query_time_ms == 25.5
        assert metrics.timestamp == timestamp
    
    def test_system_metrics_validation_cpu_percent(self):
        """Test CPU percentage validation."""
        valid_cpu_values = [0.0, 50.0, 100.0]
        
        for cpu_value in valid_cpu_values:
            metrics = SystemMetrics(
                memory_usage_mb=100.0,
                cpu_usage_percent=cpu_value,
                active_connections=1,
                total_memories_stored=0,
                average_query_time_ms=10.0,
                timestamp=datetime.now(timezone.utc)
            )
            assert metrics.cpu_usage_percent == cpu_value
    
    def test_system_metrics_validation_negative_values(self):
        """Test validation for non-negative integer fields."""
        with pytest.raises(ValidationError):
            SystemMetrics(
                memory_usage_mb=100.0,
                cpu_usage_percent=50.0,
                active_connections=-1,  # Invalid: negative
                total_memories_stored=0,
                average_query_time_ms=10.0,
                timestamp=datetime.now(timezone.utc)
            )
    
    def test_system_metrics_immutability(self):
        """Test that SystemMetrics instances are immutable."""
        metrics = SystemMetrics(
            memory_usage_mb=100.0,
            cpu_usage_percent=50.0,
            active_connections=5,
            total_memories_stored=100,
            average_query_time_ms=20.0,
            timestamp=datetime.now(timezone.utc)
        )
        
        with pytest.raises(ValidationError):
            metrics.cpu_usage_percent = 75.0