"""Comprehensive tests for custom exceptions."""

import pytest

from src.config.exceptions import (
    ConfigurationError,
    EnvironmentVariableError,
    ValidationError,
    ConfigurationFactoryError,
    PerformanceError,
    MemoryError,
    MemoryStorageError,
    MemoryRetrievalError,
    AgentError,
    AgentInitializationError,
    AgentCommunicationError,
)


class TestConfigurationError:
    """Test suite for ConfigurationError base exception."""
    
    def test_configuration_error_creation(self):
        """Test basic ConfigurationError creation."""
        error = ConfigurationError("Test configuration error")
        assert str(error) == "Test configuration error"
        assert isinstance(error, Exception)
    
    def test_configuration_error_inheritance(self):
        """Test that ConfigurationError is properly inherited by subclasses."""
        env_error = EnvironmentVariableError("TEST_VAR")
        validation_error = ValidationError("field", "value")
        factory_error = ConfigurationFactoryError("TestFactory")
        performance_error = PerformanceError("test_op", 0.1, 0.05)
        
        assert isinstance(env_error, ConfigurationError)
        assert isinstance(validation_error, ConfigurationError)
        assert isinstance(factory_error, ConfigurationError)
        assert isinstance(performance_error, ConfigurationError)


class TestEnvironmentVariableError:
    """Test suite for EnvironmentVariableError."""
    
    def test_environment_variable_error_default_message(self):
        """Test EnvironmentVariableError with default message."""
        error = EnvironmentVariableError("DATABASE_URL")
        
        assert error.variable_name == "DATABASE_URL"
        assert "Environment variable 'DATABASE_URL' is required but not set" in str(error)
        assert error.message == "Environment variable 'DATABASE_URL' is required but not set"
    
    def test_environment_variable_error_custom_message(self):
        """Test EnvironmentVariableError with custom message."""
        custom_message = "DATABASE_URL must be a valid connection string"
        error = EnvironmentVariableError("DATABASE_URL", custom_message)
        
        assert error.variable_name == "DATABASE_URL"
        assert error.message == custom_message
        assert str(error) == custom_message
    
    def test_environment_variable_error_attributes(self):
        """Test that EnvironmentVariableError preserves all attributes."""
        error = EnvironmentVariableError("API_KEY", "API key is invalid")
        
        assert hasattr(error, 'variable_name')
        assert hasattr(error, 'message')
        assert error.variable_name == "API_KEY"
        assert error.message == "API key is invalid"


class TestValidationError:
    """Test suite for ValidationError."""
    
    def test_validation_error_default_message(self):
        """Test ValidationError with default message."""
        error = ValidationError("confidence", 1.5)
        
        assert error.field_name == "confidence"
        assert error.value == 1.5
        assert "Validation failed for field 'confidence' with value '1.5'" in str(error)
    
    def test_validation_error_custom_message(self):
        """Test ValidationError with custom message."""
        custom_message = "Confidence must be between 0.0 and 1.0"
        error = ValidationError("confidence", 1.5, custom_message)
        
        assert error.field_name == "confidence"
        assert error.value == 1.5
        assert error.message == custom_message
        assert str(error) == custom_message
    
    def test_validation_error_various_value_types(self):
        """Test ValidationError with different value types."""
        # String value
        error1 = ValidationError("name", "")
        assert error1.value == ""
        
        # None value
        error2 = ValidationError("config", None)
        assert error2.value is None
        
        # Complex object
        complex_value = {"key": "value"}
        error3 = ValidationError("settings", complex_value)
        assert error3.value == complex_value


class TestConfigurationFactoryError:
    """Test suite for ConfigurationFactoryError."""
    
    def test_factory_error_default_message(self):
        """Test ConfigurationFactoryError with default message."""
        error = ConfigurationFactoryError("DatabaseFactory")
        
        assert error.factory_name == "DatabaseFactory"
        assert "Configuration factory 'DatabaseFactory' operation failed" in str(error)
    
    def test_factory_error_custom_message(self):
        """Test ConfigurationFactoryError with custom message."""
        custom_message = "Failed to create database configuration due to invalid parameters"
        error = ConfigurationFactoryError("DatabaseFactory", custom_message)
        
        assert error.factory_name == "DatabaseFactory"
        assert error.message == custom_message
        assert str(error) == custom_message
    
    def test_factory_error_in_try_catch(self):
        """Test ConfigurationFactoryError in exception handling context."""
        def failing_factory_operation():
            raise ConfigurationFactoryError("TestFactory", "Simulated failure")
        
        with pytest.raises(ConfigurationFactoryError) as exc_info:
            failing_factory_operation()
        
        assert exc_info.value.factory_name == "TestFactory"
        assert "Simulated failure" in str(exc_info.value)


class TestPerformanceError:
    """Test suite for PerformanceError."""
    
    def test_performance_error_creation(self):
        """Test PerformanceError creation with timing information."""
        error = PerformanceError("database_query", 0.1, 0.05)
        
        assert error.operation == "database_query"
        assert error.duration == 0.1
        assert error.threshold == 0.05
        assert "Operation 'database_query' took 0.100s, exceeding threshold of 0.050s" in str(error)
    
    def test_performance_error_formatting(self):
        """Test PerformanceError message formatting."""
        error = PerformanceError("config_load", 0.0678, 0.05)
        
        message = str(error)
        assert "config_load" in message
        assert "0.068s" in message  # Rounded to 3 decimal places
        assert "0.050s" in message
    
    def test_performance_error_attributes(self):
        """Test PerformanceError attribute preservation."""
        error = PerformanceError("test_operation", 1.234, 0.5)
        
        assert error.operation == "test_operation"
        assert error.duration == 1.234
        assert error.threshold == 0.5
        assert hasattr(error, 'message')


class TestMemoryError:
    """Test suite for MemoryError and its subclasses."""
    
    def test_memory_error_base(self):
        """Test MemoryError base exception."""
        error = MemoryError("Base memory error")
        assert str(error) == "Base memory error"
        assert isinstance(error, Exception)
    
    def test_memory_error_inheritance(self):
        """Test MemoryError inheritance by subclasses."""
        storage_error = MemoryStorageError("store_operation")
        retrieval_error = MemoryRetrievalError("retrieve_query")
        
        assert isinstance(storage_error, MemoryError)
        assert isinstance(retrieval_error, MemoryError)


class TestMemoryStorageError:
    """Test suite for MemoryStorageError."""
    
    def test_memory_storage_error_basic(self):
        """Test MemoryStorageError with basic operation description."""
        error = MemoryStorageError("save_user_preference")
        
        assert error.operation == "save_user_preference"
        assert error.details is None
        assert "Memory storage operation failed: save_user_preference" in str(error)
    
    def test_memory_storage_error_with_details(self):
        """Test MemoryStorageError with additional details."""
        error = MemoryStorageError("save_conversation", "Database connection timeout")
        
        assert error.operation == "save_conversation"
        assert error.details == "Database connection timeout"
        assert "Memory storage operation failed: save_conversation - Database connection timeout" in str(error)
    
    def test_memory_storage_error_attributes(self):
        """Test MemoryStorageError attribute preservation."""
        error = MemoryStorageError("test_op", "test_details")
        
        assert hasattr(error, 'operation')
        assert hasattr(error, 'details')
        assert hasattr(error, 'message')


class TestMemoryRetrievalError:
    """Test suite for MemoryRetrievalError."""
    
    def test_memory_retrieval_error_basic(self):
        """Test MemoryRetrievalError with basic query description."""
        error = MemoryRetrievalError("find_user_preferences")
        
        assert error.query == "find_user_preferences"
        assert error.details is None
        assert "Memory retrieval operation failed: find_user_preferences" in str(error)
    
    def test_memory_retrieval_error_with_details(self):
        """Test MemoryRetrievalError with additional details."""
        error = MemoryRetrievalError("search_conversations", "Query timeout after 30 seconds")
        
        assert error.query == "search_conversations"
        assert error.details == "Query timeout after 30 seconds"
        assert "Memory retrieval operation failed: search_conversations - Query timeout after 30 seconds" in str(error)


class TestAgentError:
    """Test suite for AgentError and its subclasses."""
    
    def test_agent_error_base(self):
        """Test AgentError base exception."""
        error = AgentError("Base agent error")
        assert str(error) == "Base agent error"
        assert isinstance(error, Exception)
    
    def test_agent_error_inheritance(self):
        """Test AgentError inheritance by subclasses."""
        init_error = AgentInitializationError("MemoryAgent")
        comm_error = AgentCommunicationError("MemoryAgent", "Claude API")
        
        assert isinstance(init_error, AgentError)
        assert isinstance(comm_error, AgentError)


class TestAgentInitializationError:
    """Test suite for AgentInitializationError."""
    
    def test_agent_initialization_error_basic(self):
        """Test AgentInitializationError with basic agent type."""
        error = AgentInitializationError("MemoryAgent")
        
        assert error.agent_type == "MemoryAgent"
        assert error.details is None
        assert "Agent initialization failed for type 'MemoryAgent'" in str(error)
    
    def test_agent_initialization_error_with_details(self):
        """Test AgentInitializationError with additional details."""
        error = AgentInitializationError("ConversationAgent", "Missing API key")
        
        assert error.agent_type == "ConversationAgent"
        assert error.details == "Missing API key"
        assert "Agent initialization failed for type 'ConversationAgent' - Missing API key" in str(error)


class TestAgentCommunicationError:
    """Test suite for AgentCommunicationError."""
    
    def test_agent_communication_error_basic(self):
        """Test AgentCommunicationError with basic agent and service."""
        error = AgentCommunicationError("MemoryAgent", "Claude API")
        
        assert error.agent_type == "MemoryAgent"
        assert error.service == "Claude API"
        assert error.details is None
        assert "Agent communication failed: MemoryAgent -> Claude API" in str(error)
    
    def test_agent_communication_error_with_details(self):
        """Test AgentCommunicationError with additional details."""
        error = AgentCommunicationError("ConversationAgent", "Neo4j Database", "Connection refused")
        
        assert error.agent_type == "ConversationAgent"
        assert error.service == "Neo4j Database"
        assert error.details == "Connection refused"
        assert "Agent communication failed: ConversationAgent -> Neo4j Database - Connection refused" in str(error)
    
    def test_agent_communication_error_attributes(self):
        """Test AgentCommunicationError attribute preservation."""
        error = AgentCommunicationError("TestAgent", "TestService", "TestDetails")
        
        assert hasattr(error, 'agent_type')
        assert hasattr(error, 'service')
        assert hasattr(error, 'details')
        assert hasattr(error, 'message')


class TestExceptionChaining:
    """Test suite for exception chaining and context preservation."""
    
    def test_exception_chaining_with_raise_from(self):
        """Test exception chaining with 'raise from' pattern."""
        def operation_that_fails():
            try:
                # Simulate an operation that fails
                raise ValueError("Original validation error")
            except ValueError as e:
                raise ValidationError("field_name", "invalid_value", "Custom validation message") from e
        
        with pytest.raises(ValidationError) as exc_info:
            operation_that_fails()
        
        # Check that the original exception is preserved
        assert exc_info.value.__cause__ is not None
        assert isinstance(exc_info.value.__cause__, ValueError)
        assert "Original validation error" in str(exc_info.value.__cause__)
    
    def test_exception_context_preservation(self):
        """Test that exception context is properly preserved."""
        def nested_operation():
            try:
                raise EnvironmentVariableError("DATABASE_URL")
            except EnvironmentVariableError as e:
                raise ConfigurationFactoryError("DatabaseFactory", "Failed due to environment issue") from e
        
        with pytest.raises(ConfigurationFactoryError) as exc_info:
            nested_operation()
        
        # Verify the exception chain
        factory_error = exc_info.value
        assert factory_error.factory_name == "DatabaseFactory"
        assert "Failed due to environment issue" in str(factory_error)
        
        # Verify the cause
        assert isinstance(factory_error.__cause__, EnvironmentVariableError)
        assert factory_error.__cause__.variable_name == "DATABASE_URL"


class TestExceptionUsagePatterns:
    """Test suite for common exception usage patterns."""
    
    def test_configuration_error_handling_pattern(self):
        """Test typical configuration error handling pattern."""
        def configure_database():
            # Simulate configuration that might fail
            raise EnvironmentVariableError("NEO4J_PASSWORD", "Password not found in environment")
        
        with pytest.raises(ConfigurationError) as exc_info:
            configure_database()
        
        # Should be able to catch as base ConfigurationError
        assert isinstance(exc_info.value, ConfigurationError)
        assert isinstance(exc_info.value, EnvironmentVariableError)
    
    def test_memory_error_handling_pattern(self):
        """Test typical memory error handling pattern."""
        def memory_operation():
            # Simulate memory operation that might fail
            raise MemoryStorageError("save_preference", "Network timeout")
        
        with pytest.raises(MemoryError) as exc_info:
            memory_operation()
        
        # Should be able to catch as base MemoryError
        assert isinstance(exc_info.value, MemoryError)
        assert isinstance(exc_info.value, MemoryStorageError)
    
    def test_agent_error_handling_pattern(self):
        """Test typical agent error handling pattern."""
        def agent_operation():
            # Simulate agent operation that might fail
            raise AgentCommunicationError("MemoryAgent", "Claude API", "Rate limit exceeded")
        
        with pytest.raises(AgentError) as exc_info:
            agent_operation()
        
        # Should be able to catch as base AgentError
        assert isinstance(exc_info.value, AgentError)
        assert isinstance(exc_info.value, AgentCommunicationError)