"""F1 Core Configuration Models module.

This module provides configuration management and shared data models 
for the entire Intelligent Memory Orchestrator system.
"""

from .settings import AppSettings, get_settings
from .models import (
    ConversationContext,
    MemoryDecision,
    MemoryQuery,
    MemoryResult,
    HealthStatus,
    SystemMetrics,
)
from .factories import (
    ConfigProtocol,
    DatabaseConfig,
    RedisConfig,
    AgentConfig,
    MonitoringConfig,
    DatabaseConfigFactory,
    RedisConfigFactory,
    AgentConfigFactory,
    MonitoringConfigFactory,
)
from .exceptions import (
    ConfigurationError,
    EnvironmentVariableError,
    ValidationError,
    ConfigurationFactoryError,
    PerformanceError,
    MemoryError,
    MemoryStorageError,
    MemoryRetrievalError,
    AgentError,
    AgentInitializationError,
    AgentCommunicationError,
)

__all__ = [
    # Settings
    "AppSettings",
    "get_settings",
    # Models
    "ConversationContext",
    "MemoryDecision",
    "MemoryQuery",
    "MemoryResult",
    "HealthStatus",
    "SystemMetrics",
    # Factories
    "ConfigProtocol",
    "DatabaseConfig",
    "RedisConfig",
    "AgentConfig",
    "MonitoringConfig",
    "DatabaseConfigFactory",
    "RedisConfigFactory",
    "AgentConfigFactory",
    "MonitoringConfigFactory",
    # Exceptions
    "ConfigurationError",
    "EnvironmentVariableError",
    "ValidationError",
    "ConfigurationFactoryError",
    "PerformanceError",
    "MemoryError",
    "MemoryStorageError",
    "MemoryRetrievalError",
    "AgentError",
    "AgentInitializationError",
    "AgentCommunicationError",
]